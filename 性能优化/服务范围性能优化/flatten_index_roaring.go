package main

// FlattenIndexMap uses a single map per TPL, mapping the (Origin, Dest) pair to a single flattened index.
type FlattenIndexMap struct {
	data map[uint32]map[uint32]bool
}

// NewFlattenIndexMap now builds from decoupled data, performing a Cartesian product internally.
// This is expected to be VERY SLOW.
func NewFlattenIndexMap(data map[uint32]TplCoverage) *FlattenIndexMap {
	f := &FlattenIndexMap{
		data: make(map[uint32]map[uint32]bool),
	}

	for tplID, coverage := range data {
		// Pre-allocate map with estimated capacity (Cartesian product size)
		f.data[tplID] = make(map[uint32]bool, len(coverage.Origins)*len(coverage.Dests))

		// Cartesian product generation
		for _, originID := range coverage.Origins {
			for _, destID := range coverage.Dests {
				index := originID*NumLocations + destID
				f.data[tplID][index] = true
			}
		}
	}
	return f
}

func (f *FlattenIndexMap) IsServiceable(tpl, origin, dest uint32) bool {
	if tplMap, ok := f.data[tpl]; ok {
		// Calculate the flattened index to check
		index := origin*NumLocations + dest
		return tplMap[index]
	}
	return false
}
