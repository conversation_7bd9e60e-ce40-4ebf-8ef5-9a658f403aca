package main

import (
	"context"
	"encoding/binary"
	"fmt"
	"log"
	"math/rand"
	"os"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
)

// TestData 测试数据结构
type TestData struct {
	ShopID      uint64
	SellerLoc   uint16
	BuyerLoc    uint16
	ChannelID   uint32
	Date        time.Time
	WindowStart uint8
	WindowSize  uint8
	Min         uint32
	Max         uint32
}

// Strategy 优化策略接口
type Strategy interface {
	GetName() string
	GetDescription() string
	WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (writtenKeys int, err error)
	ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error
}

// BenchResult 基准测试结果
type BenchResult struct {
	Strategy        Strategy
	SampleSize      int
	ActualKeyCount  int
	WriteTime       time.Duration
	ReadTime        time.Duration
	MemoryUsed      int64
	MemoryPerRecord float64
	WriteOPS        float64
	ReadOPS         float64
}

// ===== 工具函数：公共压缩算法 =====

// compressValueToInt 将 min,max(<256) 压缩为 16 位小整数
// 8位打包：min(8位) + max(8位) = 16位 ≤ 65535
// 解码：min = v >> 8，max = v & 0xFF
// 0xFF = 255，用于取低8位；支持范围 [0,255]
func compressValueToInt(min, max uint32) int {
	return int((min << 8) | max)
}

// uint64ToBase62 Base62编码
func uint64ToBase62(num uint64) string {
	if num == 0 {
		return "0"
	}
	const base62Chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	var result strings.Builder
	for num > 0 {
		result.WriteByte(base62Chars[num%62])
		num /= 62
	}
	s := result.String()
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// compressAggregationField 所有聚合策略通用Field压缩函数（16进制编码）
// 压缩 Weekday(3bit) + ChannelID(5bit) + WindowIndex(3bit) = 11位
// 输出: 1-3字符的16进制字符串，平均2.84字符，性能优于Base64URL编码
func compressAggregationField(data TestData) string {
	weekday := uint32(data.Date.Weekday()) & 0x7
	channelCompressed := (data.ChannelID - 8000) & 0x1F
	windowIndex := uint32(data.WindowStart/4) & 0x7

	// 11位打包: Weekday(3bit) + ChannelID(5bit) + WindowIndex(3bit)
	packed := (weekday << 8) | (channelCompressed << 3) | windowIndex
	return strconv.FormatUint(uint64(packed), 16)
}

// ===== 策略1：原版HashMap =====

type OriginalStrategy struct{}

func (s *OriginalStrategy) GetName() string        { return "Original" }
func (s *OriginalStrategy) GetDescription() string { return "原始HashMap格式，无任何优化" }

func (s *OriginalStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	pipe := rdb.Pipeline()
	batchSize := 1000
	writtenKeys := make(map[string]struct{})

	for i, data := range testData {
		// KEY格式: shopId_channelId_pickupLocationId_deliverLocationId_year_month_day
		key := fmt.Sprintf("%d_%d_%d_%d_%d_%d_%d", data.ShopID, data.ChannelID, data.SellerLoc,
			data.BuyerLoc, data.Date.Year(), int(data.Date.Month()), data.Date.Day())
		// Field格式: startHour_endHour
		field := fmt.Sprintf("%d_%d", data.WindowStart, data.WindowStart+data.WindowSize)
		value := fmt.Sprintf("%d,%d", data.Min, data.Max)

		pipe.HSet(ctx, key, field, value)
		writtenKeys[key] = struct{}{}

		if (i+1)%batchSize == 0 || i == len(testData)-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	return len(writtenKeys), nil
}

func (s *OriginalStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000

	for i := 0; i < readSampleSize; i++ {
		data := testData[i%len(testData)]
		// KEY格式: shopId_channelId_pickupLocationId_deliverLocationId_year_month_day
		key := fmt.Sprintf("%d_%d_%d_%d_%d_%d_%d", data.ShopID, data.ChannelID, data.SellerLoc,
			data.BuyerLoc, data.Date.Year(), int(data.Date.Month()), data.Date.Day())
		// Field格式: startHour_endHour
		field := fmt.Sprintf("%d_%d", data.WindowStart, data.WindowStart+data.WindowSize)

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

// ===== 策略2：仅Value压缩优化 =====

type ValueIntOptimizedStrategy struct{}

func (s *ValueIntOptimizedStrategy) GetName() string { return "ValueIntOptimized" }
func (s *ValueIntOptimizedStrategy) GetDescription() string {
	return "保持原版HashMap结构，仅将Value压缩为小整数"
}

func (s *ValueIntOptimizedStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	pipe := rdb.Pipeline()
	batchSize := 1000
	writtenKeys := make(map[string]struct{})

	for i, data := range testData {
		key := fmt.Sprintf("%d_%d_%d_%d_%s", data.ShopID, data.SellerLoc,
			data.BuyerLoc, data.ChannelID, data.Date.Format("20060102"))
		field := fmt.Sprintf("%d_%d", data.WindowStart, data.WindowStart+data.WindowSize)
		value := compressValueToInt(data.Min, data.Max) // 使用小整数压缩

		pipe.HSet(ctx, key, field, value)
		writtenKeys[key] = struct{}{}

		if (i+1)%batchSize == 0 || i == len(testData)-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	return len(writtenKeys), nil
}

func (s *ValueIntOptimizedStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000

	for i := 0; i < readSampleSize; i++ {
		data := testData[i%len(testData)]
		key := fmt.Sprintf("%d_%d_%d_%d_%s", data.ShopID, data.SellerLoc,
			data.BuyerLoc, data.ChannelID, data.Date.Format("20060102"))
		field := fmt.Sprintf("%d_%d", data.WindowStart, data.WindowStart+data.WindowSize)

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

// ===== 策略3：Base62 KEY编码压缩 =====

type Base62Strategy struct{}

func (s *Base62Strategy) GetName() string { return "Base62" }
func (s *Base62Strategy) GetDescription() string {
	return "Base62键压缩: Key用Base62, Field用4小时窗口索引"
}

func (s *Base62Strategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	pipe := rdb.Pipeline()
	batchSize := 1000
	writtenKeys := make(map[string]struct{})

	for i, data := range testData {
		key := s.compressKey(data)
		field := strconv.Itoa(int(data.WindowStart / 4)) // 窗口索引
		value := fmt.Sprintf("%d,%d", data.Min, data.Max)

		pipe.HSet(ctx, key, field, value)
		writtenKeys[key] = struct{}{}

		if (i+1)%batchSize == 0 || i == len(testData)-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	return len(writtenKeys), nil
}

func (s *Base62Strategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000

	for i := 0; i < readSampleSize; i++ {
		data := testData[i%len(testData)]
		key := s.compressKey(data)
		field := strconv.Itoa(int(data.WindowStart / 4))

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

func (s *Base62Strategy) compressKey(data TestData) string {
	shopB62 := uint64ToBase62(data.ShopID)
	sellerB62 := uint64ToBase62(uint64(data.SellerLoc))
	buyerB62 := uint64ToBase62(uint64(data.BuyerLoc))
	channelB62 := uint64ToBase62(uint64(data.ChannelID))
	weekdayB62 := uint64ToBase62(uint64(data.Date.Weekday()))
	return fmt.Sprintf("%s_%s_%s_%s_%s", shopB62, sellerB62, buyerB62, channelB62, weekdayB62)
}

// ===== 策略4：二进制打包KEY编码压缩 =====

type BinaryPackedStrategy struct{}

func (s *BinaryPackedStrategy) GetName() string { return "BinaryPacked" }
func (s *BinaryPackedStrategy) GetDescription() string {
	return "二进制打包Key(Base64URL)，Field用单字节索引，Value自适应"
}

func (s *BinaryPackedStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	pipe := rdb.Pipeline()
	batchSize := 1000
	writtenKeys := make(map[string]struct{})

	for i, data := range testData {
		key := s.compressKey(data)
		field := strconv.Itoa(int(data.WindowStart / 4))
		value := fmt.Sprintf("%d,%d", data.Min, data.Max)

		pipe.HSet(ctx, key, field, value)
		writtenKeys[key] = struct{}{}

		if (i+1)%batchSize == 0 || i == len(testData)-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	return len(writtenKeys), nil
}

func (s *BinaryPackedStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000

	for i := 0; i < readSampleSize; i++ {
		data := testData[i%len(testData)]
		key := s.compressKey(data)
		field := strconv.Itoa(int(data.WindowStart / 4))

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

func (s *BinaryPackedStrategy) compressKey(data TestData) string {
	// 二进制打包：ShopID(30b)+Seller(16b)+Buyer(16b)+Channel(5b)+Weekday(3b)=70b
	shop := data.ShopID & ((1 << 30) - 1)
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyer := uint64(data.BuyerLoc) & ((1 << 16) - 1)
	var chanNorm uint64
	if data.ChannelID >= 8000 && data.ChannelID < 8020 {
		chanNorm = uint64(data.ChannelID-8000) & ((1 << 5) - 1)
	} else {
		chanNorm = uint64(data.ChannelID) & ((1 << 5) - 1)
	}
	weekday := uint64(data.Date.Weekday()) & 0x7

	// 拆分为高6位和低64位
	high6 := uint8((shop >> 24) & 0x3F)
	shopLow24 := uint64(shop & ((1 << 24) - 1))

	// 低64位布局
	var low uint64
	low |= shopLow24 << 40
	low |= seller << 24
	low |= buyer << 8
	low |= chanNorm << 3
	low |= weekday

	// 将70位数据转换为16进制字符串
	// 高6位单独处理
	high6Str := strconv.FormatUint(uint64(high6), 16)
	// 低64位转换
	lowStr := strconv.FormatUint(low, 16)
	// 组合：高6位 + ":" + 低64位，确保唯一性
	return high6Str + ":" + lowStr
}

// ===== 策略5：64位Key压缩 =====

type Binary64BitStrategy struct{}

func (s *Binary64BitStrategy) GetName() string { return "Binary64Bit" }
func (s *Binary64BitStrategy) GetDescription() string {
	return "64位Key压缩: ShopID(20位)+SellerLoc(16位)+BuyerLoc(16位)+其他(8位)"
}

func (s *Binary64BitStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	pipe := rdb.Pipeline()
	batchSize := 1000
	writtenKeys := make(map[string]struct{})

	for i, data := range testData {
		key := s.compressKey(data)
		field := strconv.Itoa(int(data.WindowStart / 4))
		value := compressValueToInt(data.Min, data.Max)

		pipe.HSet(ctx, key, field, value)
		writtenKeys[key] = struct{}{}

		if (i+1)%batchSize == 0 || i == len(testData)-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	return len(writtenKeys), nil
}

func (s *Binary64BitStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000

	for i := 0; i < readSampleSize; i++ {
		data := testData[i%len(testData)]
		key := s.compressKey(data)
		field := strconv.Itoa(int(data.WindowStart / 4))

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

func (s *Binary64BitStrategy) compressKey(data TestData) string {
	// 64位极限压缩：ShopID(20)+SellerLoc(16)+BuyerLoc(16)+ChannelID(5)+Date(3)=60位
	shopIDCompressed := uint64(data.ShopID - 100000000)
	if shopIDCompressed > (1<<20)-1 {
		shopIDCompressed = shopIDCompressed & ((1 << 20) - 1)
	}

	sellerCompressed := uint64(data.SellerLoc - 10000)
	if sellerCompressed > (1<<16)-1 {
		sellerCompressed = sellerCompressed & ((1 << 16) - 1)
	}

	buyerCompressed := uint64(data.BuyerLoc - 10000)
	if buyerCompressed > (1<<16)-1 {
		buyerCompressed = buyerCompressed & ((1 << 16) - 1)
	}

	channelCompressed := uint64(data.ChannelID - 8000)
	if channelCompressed > (1<<5)-1 {
		channelCompressed = channelCompressed & ((1 << 5) - 1)
	}

	today := time.Now().Truncate(24 * time.Hour)
	dateOffset := uint64(data.Date.Sub(today).Hours()/24 + 7)
	if dateOffset > (1<<3)-1 {
		dateOffset = dateOffset & ((1 << 3) - 1)
	}

	// 64位打包
	var packedKey uint64
	packedKey |= shopIDCompressed << 40
	packedKey |= sellerCompressed << 24
	packedKey |= buyerCompressed << 8
	packedKey |= channelCompressed << 3
	packedKey |= dateOffset

	return strconv.FormatUint(packedKey, 16)
}

// ===== 策略6：店铺地址聚合 =====

type ShopLocationAggregationStrategy struct{}

func (s *ShopLocationAggregationStrategy) GetName() string { return "ShopLocationAggregation" }
func (s *ShopLocationAggregationStrategy) GetDescription() string {
	return "按店铺和地址聚合，减少Key数量"
}

func (s *ShopLocationAggregationStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	pipe := rdb.Pipeline()
	batchSize := 1000
	writtenKeys := make(map[string]struct{})

	for i, data := range testData {
		// KEY: Shop+SellerLoc+BuyerLoc (不包含Date和ChannelID)
		key := fmt.Sprintf("%d_%d_%d", data.ShopID, data.SellerLoc, data.BuyerLoc)
		// Field: Weekday+Channel+Window
		weekday := data.Date.Weekday()
		field := fmt.Sprintf("%d_%d_%d", weekday, data.ChannelID, data.WindowStart)
		value := fmt.Sprintf("%d,%d", data.Min, data.Max)

		pipe.HSet(ctx, key, field, value)
		writtenKeys[key] = struct{}{}

		if (i+1)%batchSize == 0 || i == len(testData)-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	return len(writtenKeys), nil
}

func (s *ShopLocationAggregationStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000

	for i := 0; i < readSampleSize; i++ {
		data := testData[i%len(testData)]
		key := fmt.Sprintf("%d_%d_%d", data.ShopID, data.SellerLoc, data.BuyerLoc)
		weekday := data.Date.Weekday()
		field := fmt.Sprintf("%d_%d_%d", weekday, data.ChannelID, data.WindowStart)

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

// ===== 策略7：聚合+BinaryPack Key压缩 =====

type AggregationBinaryPackStrategy struct{}

func (s *AggregationBinaryPackStrategy) GetName() string { return "AggregationBinaryPack" }
func (s *AggregationBinaryPackStrategy) GetDescription() string {
	return "店铺地址聚合 + BinaryPack压缩"
}

func (s *AggregationBinaryPackStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	pipe := rdb.Pipeline()
	batchSize := 1000
	writtenKeys := make(map[string]bool)

	for i, data := range testData {
		key := s.compressAggregatedKey(data)
		field := compressAggregationField(data)
		value := fmt.Sprintf("%d,%d", data.Min, data.Max)

		pipe.HSet(ctx, key, field, value)
		writtenKeys[key] = true

		if (i+1)%batchSize == 0 || i == len(testData)-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	return len(writtenKeys), nil
}

func (s *AggregationBinaryPackStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000
	interval := len(testData) / readSampleSize
	if interval == 0 {
		interval = 1
	}

	for i := 0; i < readSampleSize && i*interval < len(testData); i++ {
		data := testData[i*interval]
		key := s.compressAggregatedKey(data)
		field := compressAggregationField(data)

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

func (s *AggregationBinaryPackStrategy) compressAggregatedKey(data TestData) string {
	// 聚合KEY压缩：ShopID(30b) + SellerLoc(16b) + BuyerLoc(16b) = 62位
	var shopNorm uint64
	if data.ShopID >= 100000000 {
		shopNorm = data.ShopID - 100000000
	} else {
		shopNorm = data.ShopID
	}
	shop := shopNorm & ((1 << 30) - 1)
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyer := uint64(data.BuyerLoc) & ((1 << 16) - 1)

	packed := (shop << 32) | (seller << 16) | buyer
	return strconv.FormatUint(packed, 16)
}

// ===== 策略8：聚合+Binary压缩+Value INT压缩 =====

type AggregationBinaryIntStrategy struct{}

func (s *AggregationBinaryIntStrategy) GetName() string { return "AggregationBinaryInt" }
func (s *AggregationBinaryIntStrategy) GetDescription() string {
	return "店铺地址聚合 + Binary Key压缩 + Value整数压缩"
}

func (s *AggregationBinaryIntStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	pipe := rdb.Pipeline()
	batchSize := 1000
	writtenKeys := make(map[string]bool)

	for i, data := range testData {
		key := s.compressAggregatedKey(data)
		field := compressAggregationField(data)
		value := compressValueToInt(data.Min, data.Max)

		pipe.HSet(ctx, key, field, value)
		writtenKeys[key] = true

		if (i+1)%batchSize == 0 || i == len(testData)-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	return len(writtenKeys), nil
}

func (s *AggregationBinaryIntStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000
	interval := len(testData) / readSampleSize
	if interval == 0 {
		interval = 1
	}

	for i := 0; i < readSampleSize && i*interval < len(testData); i++ {
		data := testData[i*interval]
		key := s.compressAggregatedKey(data)
		field := compressAggregationField(data)

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

func (s *AggregationBinaryIntStrategy) compressAggregatedKey(data TestData) string {
	var shopNorm uint64
	if data.ShopID >= 100000000 {
		shopNorm = data.ShopID - 100000000
	} else {
		shopNorm = data.ShopID
	}
	shop := shopNorm & ((1 << 30) - 1)
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyer := uint64(data.BuyerLoc) & ((1 << 16) - 1)

	packed := (shop << 32) | (seller << 16) | buyer
	return strconv.FormatUint(packed, 16)
}

// ===== 策略11：KV + 原生位级打包 =====

type KVBinaryBitPackStrategy struct{}

func (s *KVBinaryBitPackStrategy) GetName() string { return "KVBinaryBitPack" }
func (s *KVBinaryBitPackStrategy) GetDescription() string {
	return "KV结构: 8B二进制BinaryPack Key + 紧凑位流(7B/条)，通过总长度推断条目数"
}

func (s *KVBinaryBitPackStrategy) compressAggregatedKey(data TestData) string {
	var shopNorm uint64
	if data.ShopID >= 100000000 {
		shopNorm = data.ShopID - 100000000
	} else {
		shopNorm = data.ShopID
	}
	shop := shopNorm & ((1 << 30) - 1)
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyer := uint64(data.BuyerLoc) & ((1 << 16) - 1)
	packed := (shop << 32) | (seller << 16) | buyer
	var buf [8]byte
	binary.BigEndian.PutUint64(buf[:], packed)
	return string(buf[:])
}

// packWC6To7Bytes 将 Weekday(3b)|Channel(5b)|6组窗口[Min(4b)|Max(4b)] 打成56位，按大端顺序输出7字节
func packWC6To7Bytes(weekday uint8, channel uint8, pairs *[12]uint8) [7]byte {
	w := weekday & 0x7
	c := channel & 0x1F
	var v uint64
	// 逐段左移拼接：w(3) | c(5) | 12个4bit
	v = 0
	v = (v << 3) | uint64(w)
	v = (v << 5) | uint64(c)
	for i := 0; i < 12; i++ {
		v = (v << 4) | uint64(pairs[i]&0xF)
	}
	var out [7]byte
	for i := 6; i >= 0; i-- {
		out[i] = byte(v & 0xFF)
		v >>= 8
	}
	return out
}

// packWC6EntriesToBytes 将若干个56bit条目直接拼接成字节流
// 每个条目7字节，总长度/7即可得到条目数
func packWC6EntriesToBytes(entries map[uint8]*[12]uint8) []byte {
	n := len(entries)
	buf := make([]byte, 7*n)
	off := 0
	for wc, arr := range entries {
		weekday := wc >> 5
		channel := wc & 0x1F
		b := packWC6To7Bytes(weekday, channel, arr)
		copy(buf[off:off+7], b[:])
		off += 7
	}
	return buf
}

func (s *KVBinaryBitPackStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	// 聚合到 Key 下的 (Weekday,Channel) 维度，每个维度固定6个窗口
	groups := make(map[string]map[uint8]*[12]uint8)
	for _, d := range testData {
		key := s.compressAggregatedKey(d)
		m := groups[key]
		if m == nil {
			m = make(map[uint8]*[12]uint8)
			groups[key] = m
		}
		w := uint8(uint32(d.Date.Weekday()) & 0x7)
		c := uint8((d.ChannelID - 8000) & 0x1F)
		wc := (w << 5) | c
		arr := m[wc]
		if arr == nil {
			arr = new([12]uint8)
			m[wc] = arr
		}
		idx := int((d.WindowStart / 4) & 0x7) // 0..5
		if idx < 6 {
			arr[idx*2] = uint8(d.Min & 0xF)
			arr[idx*2+1] = uint8(d.Max & 0xF)
		}
	}
	pipe := rdb.Pipeline()
	const batchSize = 1000
	i := 0
	for key, entries := range groups {
		buf := packWC6EntriesToBytes(entries)
		pipe.Set(ctx, key, buf, 0)
		i++
		if i%batchSize == 0 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	if len(groups)%batchSize != 0 {
		if _, err := pipe.Exec(ctx); err != nil {
			return 0, err
		}
	}
	return len(groups), nil
}

func (s *KVBinaryBitPackStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	interval := len(testData) / readSampleSize
	if interval == 0 {
		interval = 1
	}
	pipe := rdb.Pipeline()
	const batchSize = 1000
	for i := 0; i < readSampleSize && i*interval < len(testData); i++ {
		d := testData[i*interval]
		key := s.compressAggregatedKey(d)
		pipe.Get(ctx, key)
		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

// ===== 策略12：HashMap分片优化 =====

type HashMapShardedStrategy struct{}

func (s *HashMapShardedStrategy) GetName() string { return "HashMapSharded" }
func (s *HashMapShardedStrategy) GetDescription() string {
	return "HashMap: Key(Shop,Seller,Buyer/100), Field(Buyer%100,Channel), Value(packed Weekday+Windows)"
}

// compressKey: shop(30b)|seller(16b)|buyer_quotient(10b) = 56 bits
func (s *HashMapShardedStrategy) compressKey(data TestData) string {
	var shopNorm uint64
	if data.ShopID >= 100000000 {
		shopNorm = data.ShopID - 100000000
	} else {
		shopNorm = data.ShopID
	}
	shop := shopNorm & ((1 << 30) - 1)
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyerQuotient := uint64(data.BuyerLoc/100) & 0x3FF

	packedKey := (shop << 26) | (seller << 10) | buyerQuotient
	var buf [8]byte
	binary.BigEndian.PutUint64(buf[:], packedKey)
	return string(buf[1:]) // 56 bits = 7 bytes
}

// compressField: buyer_shard(7b)|channel(5b) = 12 bits
func (s *HashMapShardedStrategy) compressField(data TestData) string {
	buyerShard := uint16(data.BuyerLoc%100) & 0x7F
	channelCompressed := uint16((data.ChannelID - 8000) & 0x1F)

	packedField := (buyerShard << 5) | channelCompressed
	var buf [2]byte
	binary.BigEndian.PutUint16(buf[:], packedField)
	return string(buf[:])
}

// packWeekdayWindowsTo7Bytes packs a weekday and its 6 window pairs into 7 bytes.
func (s *HashMapShardedStrategy) packWeekdayWindowsTo7Bytes(weekday uint8, pairs *[12]uint8) [7]byte {
	// 51 bits: Weekday(3b) | 6 * (Min(4b) | Max(4b))
	var v uint64
	v = (v << 3) | (uint64(weekday) & 0x7)
	for i := 0; i < 12; i++ {
		v = (v << 4) | (uint64(pairs[i]) & 0xF)
	}

	var out [7]byte
	// Place 51 bits into the 7-byte array (big-endian)
	for i := 6; i >= 0; i-- {
		out[i] = byte(v & 0xFF)
		v >>= 8
	}
	return out
}

func (s *HashMapShardedStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	// Groups data before writing: map[key] -> map[field] -> map[weekday] -> array of 6 (min,max) pairs
	groups := make(map[string]map[string]map[uint8]*[12]uint8)

	for _, d := range testData {
		key := s.compressKey(d)
		field := s.compressField(d)
		weekday := uint8(d.Date.Weekday())
		windowIdx := int(d.WindowStart / 4)

		if groups[key] == nil {
			groups[key] = make(map[string]map[uint8]*[12]uint8)
		}
		if groups[key][field] == nil {
			groups[key][field] = make(map[uint8]*[12]uint8)
		}
		if groups[key][field][weekday] == nil {
			groups[key][field][weekday] = new([12]uint8)
		}

		if windowIdx < 6 {
			minMaxArr := groups[key][field][weekday]
			minMaxArr[windowIdx*2] = uint8(d.Min & 0xF)
			minMaxArr[windowIdx*2+1] = uint8(d.Max & 0xF)
		}
	}

	pipe := rdb.Pipeline()
	const batchSize = 1000
	i := 0
	writtenKeys := 0
	for key, fields := range groups {
		writtenKeys++
		for field, weekdays := range fields {
			var finalValueBlob []byte
			for weekday, minMaxArr := range weekdays {
				// A nil array might be stored if windowIdx >= 6, check for it.
				if minMaxArr != nil {
					weekdayBlob := s.packWeekdayWindowsTo7Bytes(weekday, minMaxArr)
					finalValueBlob = append(finalValueBlob, weekdayBlob[:]...)
				}
			}
			pipe.HSet(ctx, key, field, finalValueBlob)
			i++
			if i%batchSize == 0 {
				if _, err := pipe.Exec(ctx); err != nil {
					return 0, err
				}
				pipe = rdb.Pipeline()
			}
		}
	}

	if i%batchSize != 0 {
		if _, err := pipe.Exec(ctx); err != nil {
			return 0, err
		}
	}
	return writtenKeys, nil
}

func (s *HashMapShardedStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000
	interval := len(testData) / readSampleSize
	if interval == 0 {
		interval = 1
	}

	for i := 0; i < readSampleSize && i*interval < len(testData); i++ {
		data := testData[i*interval]
		key := s.compressKey(data)
		field := s.compressField(data)

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

// ===== 策略13：HashMap终极Value压缩 =====

type HashMapValueOptimizedStrategy struct{}

// minMax is a helper struct for grouping data before packing.
type minMax struct {
	min     uint32
	max     uint32
	weekday time.Weekday
	isSet   bool
}

func (s *HashMapValueOptimizedStrategy) GetName() string { return "HashMapValueOptimized" }
func (s *HashMapValueOptimizedStrategy) GetDescription() string {
	return "HashMap: 聚合Window和Date到Value, Max->Delta(3b)"
}

func (s *HashMapValueOptimizedStrategy) compressKey(data TestData) string {
	var shopNorm uint64
	if data.ShopID >= 100000000 {
		shopNorm = data.ShopID - 100000000
	} else {
		shopNorm = data.ShopID
	}
	shop := shopNorm & ((1 << 30) - 1)
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyerQuotient := uint64(data.BuyerLoc/100) & 0x3FF

	packedKey := (shop << 26) | (seller << 10) | buyerQuotient
	var buf [8]byte
	binary.BigEndian.PutUint64(buf[:], packedKey)
	return string(buf[1:]) // 56 bits = 7 bytes
}

func (s *HashMapValueOptimizedStrategy) compressField(data TestData) string {
	buyerShard := uint16(data.BuyerLoc%100) & 0x7F
	channelCompressed := uint16((data.ChannelID - 8000) & 0x1F)

	packedField := (buyerShard << 5) | channelCompressed
	var buf [2]byte
	binary.BigEndian.PutUint16(buf[:], packedField)
	return string(buf[:])
}

func (s *HashMapValueOptimizedStrategy) packFinalValue(data *[3][6]minMax) []byte {
	var bits [129]bool
	bitOffset := 0

	// Pack Day 0
	if data[0][0].isSet {
		weekday := uint(data[0][0].weekday)
		for i := 2; i >= 0; i-- { // 3 bits for weekday
			bits[bitOffset+i] = (weekday & 1) == 1
			weekday >>= 1
		}
		bitOffset += 3

		for i := 0; i < 6; i++ { // 6 windows
			min := uint(data[0][i].min)
			delta := uint(data[0][i].max - data[0][i].min)
			for j := 3; j >= 0; j-- { // 4 bits for min
				bits[bitOffset+j] = (min & 1) == 1
				min >>= 1
			}
			bitOffset += 4
			for j := 2; j >= 0; j-- { // 3 bits for delta
				bits[bitOffset+j] = (delta & 1) == 1
				delta >>= 1
			}
			bitOffset += 3
		}
	} else {
		bitOffset += 45 // Skip 45 bits if day 0 is not set
	}

	// Pack Day 1 and Day 2
	for dayIdx := 1; dayIdx < 3; dayIdx++ {
		if data[dayIdx][0].isSet {
			for i := 0; i < 6; i++ { // 6 windows
				min := uint(data[dayIdx][i].min)
				delta := uint(data[dayIdx][i].max - data[dayIdx][i].min)
				for j := 3; j >= 0; j-- { // 4 bits for min
					bits[bitOffset+j] = (min & 1) == 1
					min >>= 1
				}
				bitOffset += 4
				for j := 2; j >= 0; j-- { // 3 bits for delta
					bits[bitOffset+j] = (delta & 1) == 1
					delta >>= 1
				}
				bitOffset += 3
			}
		} else {
			bitOffset += 42 // Skip 42 bits if day is not set
		}
	}

	// Convert bit array to byte array
	var bytes [17]byte
	for i := 0; i < 129; i++ {
		if bits[i] {
			byteIndex := i / 8
			bitInByteIndex := 7 - (i % 8)
			bytes[byteIndex] |= 1 << bitInByteIndex
		}
	}
	return bytes[:]
}

func (s *HashMapValueOptimizedStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	// Groups data before writing: map[key] -> map[field] -> [day_index][window_index] -> minMax
	groups := make(map[string]map[string]*[3][6]minMax)

	// Find the most recent date in the dataset to calculate day_index relative to it
	var mostRecentDate time.Time
	if len(testData) > 0 {
		// This is a simplification for the benchmark; in reality, you'd use a fixed epoch.
		// We find the max date to handle the 3-day window regardless of when the test is run.
		mostRecentDate = testData[0].Date
		for i := 1; i < len(testData); i++ {
			if testData[i].Date.After(mostRecentDate) {
				mostRecentDate = testData[i].Date
			}
		}
	}
	mostRecentDate = mostRecentDate.Truncate(24 * time.Hour)

	for _, d := range testData {
		key := s.compressKey(d)
		field := s.compressField(d)

		if groups[key] == nil {
			groups[key] = make(map[string]*[3][6]minMax)
		}
		if groups[key][field] == nil {
			groups[key][field] = new([3][6]minMax)
		}

		dayIndex := int(mostRecentDate.Sub(d.Date.Truncate(24*time.Hour)).Hours() / 24)
		windowIdx := int(d.WindowStart / 4)

		if dayIndex >= 0 && dayIndex < 3 && windowIdx >= 0 && windowIdx < 6 {
			dayData := groups[key][field]
			dayData[dayIndex][windowIdx] = minMax{
				min:     d.Min,
				max:     d.Max,
				weekday: d.Date.Weekday(),
				isSet:   true,
			}
		}
	}

	pipe := rdb.Pipeline()
	const batchSize = 1000
	i := 0
	writtenKeys := 0
	for key, fields := range groups {
		writtenKeys++
		for field, dataArr := range fields {
			valueBlob := s.packFinalValue(dataArr)
			pipe.HSet(ctx, key, field, valueBlob)
			i++
			if i%batchSize == 0 {
				if _, err := pipe.Exec(ctx); err != nil {
					return 0, err
				}
				pipe = rdb.Pipeline()
			}
		}
	}

	if i%batchSize != 0 {
		if _, err := pipe.Exec(ctx); err != nil {
			return 0, err
		}
	}
	return writtenKeys, nil
}

func (s *HashMapValueOptimizedStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000
	interval := len(testData) / readSampleSize
	if interval == 0 {
		interval = 1
	}

	for i := 0; i < readSampleSize && i*interval < len(testData); i++ {
		data := testData[i*interval]
		key := s.compressKey(data)
		field := s.compressField(data)

		pipe.HGet(ctx, key, field)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

// ===== 策略14：HashMap Value分组优化 =====

type HashMapValueSplitOptimizedStrategy struct{}

func (s *HashMapValueSplitOptimizedStrategy) GetName() string { return "HashMapValueSplitOptimized" }
func (s *HashMapValueSplitOptimizedStrategy) GetDescription() string {
	return "Value分组: Channel下沉到Value, 3/2个Channel一组, 减少Field数量"
}

func (s *HashMapValueSplitOptimizedStrategy) compressKey(data TestData) string {
	var shopNorm uint64
	if data.ShopID >= 100000000 {
		shopNorm = data.ShopID - 100000000
	} else {
		shopNorm = data.ShopID
	}
	shop := shopNorm & 0xFFFFFFFF
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyerQuotient := uint64(data.BuyerLoc/400) & 0xFF // 8 bits for quotient

	packedKey := (shop << 24) | (seller << 8) | buyerQuotient
	var buf [8]byte
	binary.BigEndian.PutUint64(buf[:], packedKey)
	return string(buf[1:]) // 56 bits = 7 bytes
}

func (s *HashMapValueSplitOptimizedStrategy) compressField(data TestData) string {
	buyerShard := uint16(data.BuyerLoc % 400) // 0-399, needs 9 bits
	channelIndex := data.ChannelID - 8000
	groupIndex := uint16(channelIndex / 3) // 0-6, needs 3 bits

	// Pack into 12 bits: buyer_shard(9b) | group_index(3b)
	packedField := (buyerShard << 3) | groupIndex

	var buf [2]byte
	binary.BigEndian.PutUint16(buf[:], packedField)
	return string(buf[:])
}

// packChannelData packs 3 days of data for a single channel into 17 bytes (134 bits).
func (s *HashMapValueSplitOptimizedStrategy) packChannelData(data *[3][6]minMax, channelIndex int) []byte {
	var bits [134]bool // 50 (day0) + 42 (day1) + 42 (day2) = 134 bits
	bitOffset := 0

	// Pack Day 0 (50 bits)
	if data[0][0].isSet {
		// 1. Pack Channel Index (5 bits)
		chIdx := uint(channelIndex)
		for i := 4; i >= 0; i-- {
			bits[bitOffset+i] = (chIdx & 1) == 1
			chIdx >>= 1
		}
		bitOffset += 5

		// 2. Pack Weekday (3 bits)
		weekday := uint(data[0][0].weekday)
		for i := 2; i >= 0; i-- {
			bits[bitOffset+i] = (weekday & 1) == 1
			weekday >>= 1
		}
		bitOffset += 3

		// 3. Pack 6 windows (42 bits)
		for i := 0; i < 6; i++ { // 6 windows
			min := uint(data[0][i].min)
			delta := uint(data[0][i].max - data[0][i].min)
			for j := 3; j >= 0; j-- { // 4 bits for min
				bits[bitOffset+j] = (min & 1) == 1
				min >>= 1
			}
			bitOffset += 4
			for j := 2; j >= 0; j-- { // 3 bits for delta
				bits[bitOffset+j] = (delta & 1) == 1
				delta >>= 1
			}
			bitOffset += 3
		}
	} else {
		bitOffset += 50 // Skip 50 bits if day 0 is not set
	}

	// Pack Day 1 and Day 2 (42 bits each)
	for dayIdx := 1; dayIdx < 3; dayIdx++ {
		if data[dayIdx][0].isSet {
			for i := 0; i < 6; i++ { // 6 windows
				min := uint(data[dayIdx][i].min)
				delta := uint(data[dayIdx][i].max - data[dayIdx][i].min)
				for j := 3; j >= 0; j-- { // 4 bits for min
					bits[bitOffset+j] = (min & 1) == 1
					min >>= 1
				}
				bitOffset += 4
				for j := 2; j >= 0; j-- { // 3 bits for delta
					bits[bitOffset+j] = (delta & 1) == 1
					delta >>= 1
				}
				bitOffset += 3
			}
		} else {
			bitOffset += 42 // Skip 42 bits if day is not set
		}
	}

	// Convert bit array to byte array (134 bits -> 17 bytes)
	var bytes [17]byte
	for i := 0; i < 134; i++ {
		if bits[i] {
			byteIndex := i / 8
			bitInByteIndex := 7 - (i % 8)
			bytes[byteIndex] |= 1 << bitInByteIndex
		}
	}
	return bytes[:]
}

func (s *HashMapValueSplitOptimizedStrategy) packGroupValue(channelDataMap map[int]*[3][6]minMax) []byte {
	var finalBlob []byte
	// Iterate through channels, pack each one with its index embedded, and concatenate.
	for channelIndex, channelData := range channelDataMap {
		// The new packChannelData embeds the index in the 17-byte blob.
		channelBlob := s.packChannelData(channelData, channelIndex)
		finalBlob = append(finalBlob, channelBlob...)
	}
	return finalBlob
}

func (s *HashMapValueSplitOptimizedStrategy) WriteData(ctx context.Context, rdb redis.Cmdable, testData []TestData) (int, error) {
	// Groups data: map[key] -> map[field] -> map[channel_index] -> [day_index][window_index] -> minMax
	groups := make(map[string]map[string]map[int]*[3][6]minMax)

	var mostRecentDate time.Time
	if len(testData) > 0 {
		mostRecentDate = testData[0].Date
		for i := 1; i < len(testData); i++ {
			if testData[i].Date.After(mostRecentDate) {
				mostRecentDate = testData[i].Date
			}
		}
	}
	mostRecentDate = mostRecentDate.Truncate(24 * time.Hour)

	for _, d := range testData {
		key := s.compressKey(d)
		field := s.compressField(d)

		if groups[key] == nil {
			groups[key] = make(map[string]map[int]*[3][6]minMax)
		}
		if groups[key][field] == nil {
			groups[key][field] = make(map[int]*[3][6]minMax)
		}

		channelIndex := int(d.ChannelID - 8000)
		if groups[key][field][channelIndex] == nil {
			groups[key][field][channelIndex] = new([3][6]minMax)
		}

		dayIndex := int(mostRecentDate.Sub(d.Date.Truncate(24*time.Hour)).Hours() / 24)
		windowIdx := int(d.WindowStart / 4)

		if dayIndex >= 0 && dayIndex < 3 && windowIdx >= 0 && windowIdx < 6 {
			dayData := groups[key][field][channelIndex]
			dayData[dayIndex][windowIdx] = minMax{
				min:     d.Min,
				max:     d.Max,
				weekday: d.Date.Weekday(),
				isSet:   true,
			}
		}
	}

	pipe := rdb.Pipeline()
	const batchSize = 1000
	i := 0
	writtenKeys := 0
	for key, fields := range groups {
		writtenKeys++
		for field, channelDataMap := range fields {
			valueBlob := s.packGroupValue(channelDataMap)
			pipe.HSet(ctx, key, field, valueBlob)
			i++
			if i%batchSize == 0 {
				if _, err := pipe.Exec(ctx); err != nil {
					return 0, err
				}
				pipe = rdb.Pipeline()
			}
		}
	}

	if i%batchSize != 0 {
		if _, err := pipe.Exec(ctx); err != nil {
			return 0, err
		}
	}
	return writtenKeys, nil
}

func (s *HashMapValueSplitOptimizedStrategy) ReadData(ctx context.Context, rdb redis.Cmdable, testData []TestData, readSampleSize int) error {
	pipe := rdb.Pipeline()
	batchSize := 1000
	interval := len(testData) / readSampleSize
	if interval == 0 {
		interval = 1
	}

	for i := 0; i < readSampleSize && i*interval < len(testData); i++ {
		data := testData[i*interval]
		key := s.compressKey(data)
		buyerShard := uint16(data.BuyerLoc % 400)

		// Assume 2 channel groups exist and fetch both fields using HMGET.
		// Field for group 0
		packedField0 := (buyerShard << 3) | 0
		var buf0 [2]byte
		binary.BigEndian.PutUint16(buf0[:], packedField0)
		field0 := string(buf0[:])

		// Field for group 1
		packedField1 := (buyerShard << 3) | 1
		var buf1 [2]byte
		binary.BigEndian.PutUint16(buf1[:], packedField1)
		field1 := string(buf1[:])

		pipe.HMGet(ctx, key, field0, field1)

		if (i+1)%batchSize == 0 || i == readSampleSize-1 {
			if _, err := pipe.Exec(ctx); err != nil {
				return err
			}
			pipe = rdb.Pipeline()
		}
	}
	return nil
}

// ===== 主函数和基准测试框架 =====

func main() {
	fmt.Println("🚀 Redis时效存储优化基准测试")
	fmt.Println(strings.Repeat("=", 70))

	// 连接Redis集群（用于读写操作）
	rdb := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:    []string{"v2lzw.elasticredis.cloud.shopee.io:15323"},
		Password: "DPOBESH9",
	})
	defer rdb.Close()

	// 连接所有Redis节点（用于内存管理）
	nodeAddrs := []string{
		"**************:15323",
		"*************:15323",
		"*************:15323",
	}

	var memoryRdbs []*redis.Client
	for _, addr := range nodeAddrs {
		nodeClient := redis.NewClient(&redis.Options{
			Addr:     addr,
			Password: "DPOBESH9",
			DB:       0,
		})
		memoryRdbs = append(memoryRdbs, nodeClient)
	}

	// 确保所有连接都会关闭
	defer func() {
		for _, client := range memoryRdbs {
			client.Close()
		}
	}()

	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Fatal("Redis集群连接失败:", err)
	}

	// 验证所有节点连接
	for i, nodeClient := range memoryRdbs {
		if err := nodeClient.Ping(ctx).Err(); err != nil {
			log.Fatalf("Redis节点%s连接失败: %v", nodeAddrs[i], err)
		}
	}

	size := 10000000
	fmt.Printf("\n📊 测试数据量: %s条记录\n", formatNumber(size))
	fmt.Println(strings.Repeat("-", 70))

	runBenchmark(ctx, rdb, memoryRdbs, size)
}

func runBenchmark(ctx context.Context, rdb redis.Cmdable, memoryRdbs []*redis.Client, sampleSize int) {
	// 生成测试数据
	testData := generateTestData(sampleSize)
	fmt.Printf("🧪 生成测试数据: %d个Key，每Key6个窗口，总计%d条记录\n", sampleSize, len(testData))

	// 定义所有12种策略
	strategies := []Strategy{
		//&OriginalStrategy{},
		//&ValueIntOptimizedStrategy{},
		//&Base62Strategy{},
		//&BinaryPackedStrategy{},
		//&Binary64BitStrategy{},
		//&ShopLocationAggregationStrategy{},
		//&AggregationBinaryPackStrategy{},
		//&AggregationBinaryIntStrategy{},
		//&KVBinaryBitPackStrategy{},
		//&HashMapShardedStrategy{},
		//&HashMapValueOptimizedStrategy{},
		&HashMapValueSplitOptimizedStrategy{},
	}

	var results []BenchResult

	// 测试每个策略
	for i, strategy := range strategies {
		fmt.Printf("\n[%d/%d] 🧪 测试策略: %s\n", i+1, len(strategies), strategy.GetName())
		fmt.Printf("描述: %s\n", strategy.GetDescription())

		result := benchmarkStrategy(ctx, rdb, memoryRdbs, strategy, testData)
		results = append(results, result)

		printStrategyResult(result)

		// 短暂休息，避免内存压力
		if sampleSize >= 1000000 {
			fmt.Printf("💤 内存回收中...\n")
			runtime.GC()
			time.Sleep(3 * time.Second)
		} else {
			time.Sleep(1 * time.Second)
		}
	}

	// 打印对比总结
	fmt.Printf("\n📈 综合对比总结 (数据量: %s)\n", formatNumber(sampleSize))
	fmt.Println(strings.Repeat("=", 90))
	printComparisonTable(results)

	// 导出Markdown报告
	exportMarkdownReport(sampleSize, results)
}

func benchmarkStrategy(ctx context.Context, rdb redis.Cmdable, memoryRdbs []*redis.Client, strategy Strategy, testData []TestData) BenchResult {
	// 清空所有节点数据库
	for _, nodeClient := range memoryRdbs {
		nodeClient.FlushAll(ctx)
	}
	runtime.GC()

	// 记录所有节点的基准内存
	baseMemory := getBenchmarkRedisClusterMemory(ctx, memoryRdbs)

	// 写入测试
	startTime := time.Now()
	actualKeyCount, err := strategy.WriteData(ctx, rdb, testData)
	writeTime := time.Since(startTime)

	if err != nil {
		log.Printf("写入失败: %v", err)
		actualKeyCount = 0
	}

	// 记录所有节点写入后内存
	afterWriteMemory := getBenchmarkRedisClusterMemory(ctx, memoryRdbs)
	usedMemory := afterWriteMemory - baseMemory

	// 读取测试
	readSampleSize := min(len(testData)/10, 10000)
	startTime = time.Now()
	err = strategy.ReadData(ctx, rdb, testData, readSampleSize)
	readTime := time.Since(startTime)

	if err != nil {
		log.Printf("读取失败: %v", err)
	}

	return BenchResult{
		Strategy:        strategy,
		SampleSize:      len(testData),
		ActualKeyCount:  actualKeyCount,
		WriteTime:       writeTime,
		ReadTime:        readTime,
		MemoryUsed:      usedMemory,
		MemoryPerRecord: float64(usedMemory) / float64(len(testData)),
		WriteOPS:        float64(len(testData)) / writeTime.Seconds(),
		ReadOPS:         float64(readSampleSize) / readTime.Seconds(),
	}
}

func printStrategyResult(result BenchResult) {
	fmt.Printf("  ⚡ 写入性能: %s (%.0f ops/s)\n",
		result.WriteTime.Round(time.Millisecond), result.WriteOPS)
	fmt.Printf("  ⚡ 读取性能: %s (%.0f ops/s)\n",
		result.ReadTime.Round(time.Millisecond), result.ReadOPS)
	fmt.Printf("  💾 内存使用: %s (%.1f字节/记录)\n",
		formatBytes(result.MemoryUsed), result.MemoryPerRecord)
}

func printComparisonTable(results []BenchResult) {
	fmt.Printf("%-22s %-10s %-12s %-10s %-10s\n",
		"策略", "Key数量", "内存使用", "写入OPS", "读取OPS")
	fmt.Println(strings.Repeat("-", 70))

	baselineMemory := float64(0)
	if len(results) > 0 {
		baselineMemory = results[0].MemoryPerRecord
	}

	for _, result := range results {
		memoryReduction := ""
		if baselineMemory > 0 && result.MemoryPerRecord < baselineMemory {
			reduction := (baselineMemory - result.MemoryPerRecord) / baselineMemory * 100
			memoryReduction = fmt.Sprintf("(-%.0f%%)", reduction)
		}

		fmt.Printf("%-22s %-10s %-12s %-10.0f %-10.0f\n",
			truncateString(result.Strategy.GetName(), 20),
			formatNumber(result.ActualKeyCount),
			formatBytes(result.MemoryUsed)+memoryReduction,
			result.WriteOPS,
			result.ReadOPS,
		)
	}
}

func generateTestData(size int) []TestData {
	rand.Seed(time.Now().UnixNano())

	const DAYS = 3
	windows := []uint8{0, 4, 8, 12, 16, 20}
	const BUYERS_PER_SHOP_SELLER = 5000 // Target this many buyers per shop/seller pair

	// 'size' is the number of (S,Se,B,Date,Channel) groups.
	// Total records = size * len(windows)
	expectedTotalRecords := size * len(windows)

	// Number of unique (S,Se,B) tuples to generate
	// The number of channels generated per day is probabilistic (average 3.5).
	// To get the target 'size' of groups, we need to calculate the number of tuples based on the average.
	const avgChannelsPerDay = 3.5 // 0.75 * 3 channels + 0.25 * 5 channels
	numTuples := int(float64(size) / (float64(DAYS) * avgChannelsPerDay))
	if numTuples == 0 {
		numTuples = 1
	}

	// Number of (S,Se) pairs
	numShopSellerPairs := numTuples / BUYERS_PER_SHOP_SELLER
	if numShopSellerPairs == 0 {
		numShopSellerPairs = 1
	}

	fmt.Printf("📊 数据生成策略: %d个(店铺,卖家)组合, 每组~%d个买家\n",
		numShopSellerPairs,
		BUYERS_PER_SHOP_SELLER)

	shopIDs := make([]uint64, 0, 100000)
	locations := make([]uint16, 0, 40000)
	fiveChannels := []uint32{8000, 8001, 8002, 8003, 8004}
	threeChannels := []uint32{8000, 8001, 8002}

	for i := 0; i < 100000; i++ {
		shopIDs = append(shopIDs, uint64(100000000+i))
	}
	for i := 0; i < 40000; i++ {
		locations = append(locations, uint16(10000+i))
	}

	// Pre-generate the (Shop, Seller) pairs
	shopSellerPairs := make([][2]uint64, numShopSellerPairs)
	for i := 0; i < numShopSellerPairs; i++ {
		shopSellerPairs[i] = [2]uint64{
			shopIDs[rand.Intn(len(shopIDs))],
			uint64(locations[rand.Intn(len(locations))]),
		}
	}

	// Create the list of (S, Se, B) tuples
	type ssebTuple struct {
		shopID    uint64
		sellerLoc uint16
		buyerLoc  uint16
	}
	tuples := make([]ssebTuple, 0, numTuples)
	for i := 0; i < numTuples; i++ {
		pair := shopSellerPairs[i%numShopSellerPairs] // Cycle through S,Se pairs

		// Systematic buyerLoc generation to ensure 100x sharding reduction
		buyerIndexForPair := i / numShopSellerPairs
		quotient := buyerIndexForPair / 400
		offset := buyerIndexForPair % 400

		// Start buyerLoc from 10000
		buyerLoc := uint16((100+quotient)*400 + offset)

		tuples = append(tuples, ssebTuple{
			shopID:    pair[0],
			sellerLoc: uint16(pair[1]),
			buyerLoc:  buyerLoc,
		})
	}

	data := make([]TestData, 0, expectedTotalRecords)

	// For each (S,Se,B) tuple, generate all combinations of date, channel, and window
	for _, tuple := range tuples {
		// For this tuple, generate 3 days of data
		for dayIdx := 0; dayIdx < DAYS; dayIdx++ {
			date := time.Now().AddDate(0, 0, -dayIdx)

			// Probabilistically choose between 3 or 5 channels
			var channelsToGenerate []uint32
			if rand.Float32() < 0.75 {
				channelsToGenerate = threeChannels
			} else {
				channelsToGenerate = fiveChannels
			}

			// For each day, generate 3 or 5 channels of data
			for _, channelID := range channelsToGenerate {
				// For each channel, generate 6 windows of data
				for _, ws := range windows {
					minV := uint32(rand.Intn(8) + 1)
					maxV := minV + uint32(rand.Intn(2))
					if maxV > 9 {
						maxV = 9
					}

					data = append(data, TestData{
						ShopID:      tuple.shopID,
						SellerLoc:   tuple.sellerLoc,
						BuyerLoc:    tuple.buyerLoc,
						ChannelID:   channelID,
						Date:        date,
						WindowStart: ws,
						WindowSize:  4,
						Min:         minV,
						Max:         maxV,
					})
				}
			}
		}
	}

	fmt.Printf("✅ 实际生成 %d 条记录，目标 %d 条记录\n", len(data), expectedTotalRecords)
	return data
}

// 工具函数
func getBenchmarkRedisMemory(ctx context.Context, rdb redis.Cmdable) int64 {
	info, err := rdb.Info(ctx, "memory").Result()
	if err != nil {
		return 0
	}

	lines := strings.Split(info, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "used_memory:") {
			memStr := strings.TrimPrefix(line, "used_memory:")
			memStr = strings.TrimSpace(memStr)
			var mem int64
			fmt.Sscanf(memStr, "%d", &mem)
			return mem
		}
	}
	return 0
}

// 获取集群所有节点的内存总和
func getBenchmarkRedisClusterMemory(ctx context.Context, memoryRdbs []*redis.Client) int64 {
	var totalMemory int64
	for _, nodeClient := range memoryRdbs {
		memory := getBenchmarkRedisMemory(ctx, nodeClient)
		totalMemory += memory
	}
	return totalMemory
}

func formatNumber(n int) string {
	if n >= 1000000 {
		return fmt.Sprintf("%.1fM", float64(n)/1000000)
	} else if n >= 1000 {
		return fmt.Sprintf("%.1fK", float64(n)/1000)
	}
	return strconv.Itoa(n)
}

func formatBytes(bytes int64) string {
	if bytes >= 1024*1024*1024 {
		return fmt.Sprintf("%.1fGB", float64(bytes)/(1024*1024*1024))
	} else if bytes >= 1024*1024 {
		return fmt.Sprintf("%.1fMB", float64(bytes)/(1024*1024))
	} else if bytes >= 1024 {
		return fmt.Sprintf("%.1fKB", float64(bytes)/1024)
	}
	return fmt.Sprintf("%dB", bytes)
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-2] + ".."
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func exportMarkdownReport(sampleSize int, results []BenchResult) {
	var b strings.Builder
	b.WriteString("# Redis存储优化基准报告\n\n")
	b.WriteString(fmt.Sprintf("- 数据量: %s\n", formatNumber(sampleSize)))
	b.WriteString(fmt.Sprintf("- 生成时间: %s\n\n", time.Now().Format(time.RFC3339)))

	b.WriteString("| 策略 | Key数量 | 内存使用 | 写入OPS | 读取OPS |\n")
	b.WriteString("|------|--------:|---------:|--------:|--------:|\n")

	baselineMemory := float64(0)
	if len(results) > 0 {
		baselineMemory = results[0].MemoryPerRecord
	}

	for _, r := range results {
		mem := formatBytes(r.MemoryUsed)
		if baselineMemory > 0 && r.MemoryPerRecord < baselineMemory {
			reduction := (baselineMemory - r.MemoryPerRecord) / baselineMemory * 100
			mem = fmt.Sprintf("%s (-%.0f%%)", mem, reduction)
		}
		b.WriteString(fmt.Sprintf(
			"| %s | %s | %s | %.0f | %.0f |\n",
			truncateString(r.Strategy.GetName(), 32),
			formatNumber(r.ActualKeyCount),
			mem,
			r.WriteOPS,
			r.ReadOPS,
		))
	}

	filename := fmt.Sprintf("benchmark_report_%s.md", time.Now().Format("20060102_150405"))
	if err := os.WriteFile(filename, []byte(b.String()), 0644); err != nil {
		fmt.Printf("写入Markdown报告失败: %v\n", err)
	} else {
		fmt.Printf("📄 已生成Markdown报告: %s\n", filename)
	}
}
