package main

import "github.com/RoaringBitmap/roaring"

// NestedMapRoaring uses a nested map structure: map[TplID]map[OriginID]*roaring.Bitmap
type NestedMapRoaring struct {
	data map[uint32]map[uint32]*roaring.Bitmap
}

// NewNestedMapRoaring now builds from decoupled data, performing a Cartesian product internally.
// This is expected to be VERY SLOW.
func NewNestedMapRoaring(data map[uint32]TplCoverage) *NestedMapRoaring {
	r := &NestedMapRoaring{
		data: make(map[uint32]map[uint32]*roaring.Bitmap),
	}

	for tplID, coverage := range data {
		r.data[tplID] = make(map[uint32]*roaring.Bitmap)
		// Cartesian product generation
		for _, originID := range coverage.Origins {
			destBitmap := roaring.New()
			destBitmap.AddMany(coverage.Dests)
			r.data[tplID][originID] = destBitmap
		}
	}
	return r
}

func (r *NestedMapRoaring) IsServiceable(tpl, origin, dest uint32) bool {
	if tplMap, ok := r.data[tpl]; ok {
		if destBitmap, ok := tplMap[origin]; ok {
			return destBitmap.Contains(dest)
		}
	}
	return false
}

// BatchIsServiceable processes a batch of queries efficiently
func (r *NestedMapRoaring) BatchIsServiceable(queries []ServicePair) []bool {
	results := make([]bool, len(queries))
	for idx, query := range queries {
		results[idx] = r.IsServiceable(query.TplID, query.OriginID, query.DestID)
	}
	return results
}
