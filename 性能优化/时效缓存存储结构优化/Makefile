# Redis键优化实验环境 Makefile
.PHONY: help up down restart logs build test benchmark memory-test migrate clean dev monitor status

# Docker Compose项目名称（避免中文路径问题）
PROJECT_NAME = redis-key-optimization

# 默认目标
help: ## 显示帮助信息
	@echo "Redis键优化实验环境 - 可用命令："
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# 环境管理
up: ## 启动Redis和基础服务
	docker-compose -p $(PROJECT_NAME) up -d redis redis-insight
	@echo "等待Redis启动..."
	@sleep 5
	@echo "✅ Redis已启动"
	@echo "   Redis CLI: make redis-cli"
	@echo "   Redis信息: make redis-info"

up-full: ## 启动所有服务(包括监控)
	docker-compose -p $(PROJECT_NAME) --profile monitoring up -d
	@echo "等待所有服务启动..."
	@sleep 10
	@echo "✅ 所有服务已启动："
	@echo "   Redis: localhost:6379"
	@echo "   RedisInsight: http://localhost:8001"
	@echo "   Prometheus: http://localhost:9090"
	@echo "   Grafana: http://localhost:3000 (admin/admin123)"

down: ## 停止所有服务
	docker-compose -p $(PROJECT_NAME) --profile monitoring down
	@echo "✅ 所有服务已停止"

restart: ## 重启所有服务
	$(MAKE) down
	$(MAKE) up

logs: ## 查看Redis日志
	docker-compose -p $(PROJECT_NAME) logs -f redis

status: ## 查看服务状态
	docker-compose -p $(PROJECT_NAME) ps

# 构建和开发
build: ## 构建应用镜像
	docker-compose -p $(PROJECT_NAME) build app

dev: ## 启动开发环境
	docker-compose -p $(PROJECT_NAME) --profile app up -d
	docker-compose -p $(PROJECT_NAME) exec app bash

# 测试和基准测试


benchmark: up ## 运行综合优化基准测试 (推荐)
	@echo "🚀 运行综合优化基准测试..."
	@echo "对比: 原版 vs KEY编码 vs 数据结构优化 vs 组合优化"
	@echo "测试数据量: 1万、10万、100万条记录"
	@echo "这是完整的性能对比测试，推荐使用此命令"
	docker-compose -p $(PROJECT_NAME) exec -T redis redis-cli flushall
	go run benchmark.go
	@echo "✅ 综合优化基准测试完成"

benchmark-large: up ## 运行大数据量综合基准测试 (100万+)
	@echo "🚀 运行大数据量综合优化基准测试..."
	@echo "对比: 原版 vs KEY编码 vs 数据结构优化 vs 组合优化"
	docker-compose -p $(PROJECT_NAME) exec -T redis redis-cli flushall
	LARGE_DATA=true go run benchmark.go
	@echo "✅ 大数据量综合优化基准测试完成"



encoding-monitor: up ## 启动编码监控服务
	@echo "🔍 启动Redis编码监控服务..."
	@echo "监控面板: http://localhost:8080"
	go run monitoring/encoding_monitor.go



# 示例运行
demo: up ## 运行HashMap优化示例
	@echo "⚡ 运行HashMap优化示例..."
	docker-compose -p $(PROJECT_NAME) exec -T redis redis-cli flushall
	go run examples/hashmap_optimization.go
	@echo "✅ HashMap优化示例完成"

shell-demo: ## 运行完整shell演示
	@echo "🎬 开始完整演示..."
	./run_demo.sh
	@echo "✅ 演示完成"

# 监控相关
monitor: ## 启动监控服务
	docker-compose -p $(PROJECT_NAME) --profile monitoring up -d redis-exporter prometheus grafana
	@echo "📊 监控服务已启动："
	@echo "   Prometheus: http://localhost:9090"
	@echo "   Grafana: http://localhost:3000"

redis-cli: ## 连接到Redis CLI
	docker-compose -p $(PROJECT_NAME) exec redis redis-cli

redis-info: ## 查看Redis信息
	docker-compose -p $(PROJECT_NAME) exec redis redis-cli info

# 数据管理
flush: ## 清空Redis数据
	docker-compose -p $(PROJECT_NAME) exec -T redis redis-cli flushall
	@echo "✅ Redis数据已清空"

backup: ## 备份Redis数据
	@mkdir -p ./backups
	docker-compose -p $(PROJECT_NAME) exec -T redis redis-cli --rdb - > ./backups/redis_backup_$(shell date +%Y%m%d_%H%M%S).rdb
	@echo "✅ Redis数据已备份到 ./backups/"

# 清理
clean: down ## 清理所有容器和数据
	docker-compose -p $(PROJECT_NAME) down -v --remove-orphans
	docker system prune -f
	@echo "✅ 所有容器和数据已清理"

clean-logs: ## 清理日志文件
	find . -name "*.log" -delete
	find . -name "*_results.json" -delete
	@echo "✅ 日志文件已清理"

# 工具命令
install-tools: ## 安装Go开发工具
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install golang.org/x/tools/cmd/goimports@latest
	@echo "✅ 开发工具已安装"

lint: ## 运行代码检查
	golangci-lint run ./...

format: ## 格式化代码
	goimports -w .
	go fmt ./...

# 性能分析
profile-cpu: up ## 运行CPU性能分析
	@echo "🔥 运行CPU性能分析..."
	go test -cpuprofile=cpu.prof -bench=. ./tests/
	go tool pprof cpu.prof

profile-mem: up ## 运行内存性能分析
	@echo "🧠 运行内存性能分析..."
	go test -memprofile=mem.prof -bench=. ./tests/
	go tool pprof mem.prof

# 文档和报告
docs: ## 生成性能报告
	@echo "📄 生成性能报告..."
	@mkdir -p docs/reports
	@echo "报告生成中..."
	@echo "✅ 报告已保存到 docs/reports/"

# 环境变量检查
check-env: ## 检查环境配置
	@echo "🔍 检查环境配置..."
	@docker --version || (echo "❌ Docker未安装" && exit 1)
	@docker-compose --version || (echo "❌ Docker Compose未安装" && exit 1)
	@go version || (echo "❌ Go未安装" && exit 1)
	@echo "✅ 环境配置正常"
