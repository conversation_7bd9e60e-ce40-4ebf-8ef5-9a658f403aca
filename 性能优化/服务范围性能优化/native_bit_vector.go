package main

const (
	// We have two partitions (Origin, Dest), each of size NumLocations.
	// Total bits needed = 2 * NumLocations.
	// A uint64 has 64 bits. Vector size is ceil(total_bits / 64).
	bitVectorSize = (2*NumLocations + 63) / 64
)

// NativeBitVector uses a raw []uint64 slice as a bit vector.
// This is the most memory-compact and potentially fastest solution for dense data.
type NativeBitVector struct {
	data map[uint32][]uint64
}

// NewNativeBitVector builds the bit vectors from the decoupled data source.
func NewNativeBitVector(data map[uint32]TplCoverage) *NativeBitVector {
	n := &NativeBitVector{
		data: make(map[uint32][]uint64),
	}

	for tplID, coverage := range data {
		vector := make([]uint64, bitVectorSize)

		// Set bits for origins
		for _, originID := range coverage.Origins {
			wordIndex := originID / 64
			bitIndex := originID % 64
			vector[wordIndex] |= (1 << bitIndex)
		}

		// Set bits for destinations with an offset
		for _, destID := range coverage.Dests {
			index := destID + NumLocations
			wordIndex := index / 64
			bitIndex := index % 64
			vector[wordIndex] |= (1 << bitIndex)
		}

		n.data[tplID] = vector
	}

	return n
}

// IsServiceable performs a branchless query on the raw bit vector.
func (n *NativeBitVector) IsServiceable(tpl, origin, dest uint32) bool {
	if vector, ok := n.data[tpl]; ok {
		// Calculate indices and masks for origin
		originWordIndex := origin / 64
		originBitIndex := origin % 64

		// Calculate indices and masks for destination
		destIndex := dest + NumLocations
		destWordIndex := destIndex / 64
		destBitIndex := destIndex % 64

		// Perform checks using pure bitwise operations. The result is either 1 or 0.
		checkOrigin := (vector[originWordIndex] >> originBitIndex) & 1
		checkDest := (vector[destWordIndex] >> destBitIndex) & 1

		// Final result is 1 only if both checks are 1.
		return (checkOrigin & checkDest) == 1
	}
	return false
}

// BatchIsServiceable processes a batch of queries efficiently
func (n *NativeBitVector) BatchIsServiceable(queries []ServicePair) []bool {
	results := make([]bool, len(queries))
	for idx, query := range queries {
		results[idx] = n.IsServiceable(query.TplID, query.OriginID, query.DestID)
	}
	return results
}
