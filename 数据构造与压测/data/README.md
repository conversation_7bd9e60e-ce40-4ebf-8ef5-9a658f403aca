# Go假数据生成库对比

对比了7个流行的Go假数据生成库，帮助选择合适的工具。

## 快速测试

```bash
go mod tidy
go test -v
go test -bench=. -benchmem  # 性能测试
```

## 库对比

### 1. **gofakeit** - 功能全面
```go
gofakeit.Seed(42)
name := gofakeit.Name()           // 姓名
email := gofakeit.Email()         // 邮箱
phone := gofakeit.Phone()         // 电话
car := gofakeit.Car()             // 汽车信息
```
**特点**: 160+函数，API简洁，数据类型最丰富

### 2. **bxcodec/faker** - 结构体标签
```go
type User struct {
    Username  string `faker:"username"`
    Email     string `faker:"email"`
    ID        int    `faker:"boundary_start=1, boundary_end=1000"`
}
var user User
faker.FakeData(&user)
```
**特点**: 声明式配置，支持结构体标签，边界控制

### 3. **icrowley/fake** - 轻量级
```go
name := fake.FullName()
email := fake.EmailAddress()
phone := fake.Phone()
```
**特点**: 简单直接，支持多语言，轻量高效

### 4. **randomdata** - 参数化控制
```go
firstName := randomdata.FirstName(randomdata.Male)
city := randomdata.City()
number := randomdata.Number(1, 100)
```
**特点**: 地理数据丰富，参数可控制

### 5. **go-faker/faker** - 真实地址
```go
name := gofaker.Name()
address := gofaker.GetRealAddress()  // 真实存在的地址
creditCard := gofaker.CCNumber()
```
**特点**: 提供真实地址坐标，信用卡数据

### 6. **uarand** - User-Agent专用
```go
userAgent := uarand.GetRandom()
// Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36...
```
**特点**: 专门生成真实的User-Agent字符串

## 选型推荐

| 场景 | 推荐库 | 理由 |
|------|--------|------|
| **通用测试** | `gofakeit` | 功能最全面，API最友好 |
| **结构体填充** | `bxcodec/faker` | 标签配置，声明式 |
| **性能敏感** | `icrowley/fake` | 轻量级，速度快 |
| **地理位置数据** | `randomdata` | 地理数据丰富准确 |
| **需要真实地址** | `go-faker/faker` | 提供真实存在的地址 |
| **网络爬虫** | `uarand` | 专业User-Agent工具 |
