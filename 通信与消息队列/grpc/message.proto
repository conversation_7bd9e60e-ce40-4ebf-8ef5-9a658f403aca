syntax = "proto3";
package message;

import "google/protobuf/timestamp.proto";

option go_package = "benchmark/pb";

message Person {
  string name = 1;
  int32 age = 2;
  string email = 3;
  repeated Address addresses = 4;
  repeated string phone_numbers = 5;
  
  // 添加新的数据类型
  bool is_student = 6;
  float height = 7;
  double weight = 8;
  bytes avatar = 9;
  google.protobuf.Timestamp birth_date = 10;
  repeated int64 favorite_numbers = 11;
  map<string, string> metadata = 12;
  Scores academic_scores = 13;
}

message Address {
  string street = 1;
  string city = 2;
  string state = 3;
  string country = 4;
  string postal_code = 5;
  
  // 添加新字段
  bool is_current = 6;
  float latitude = 7;
  double longitude = 8;
}

// 新增的嵌套消息类型
message Scores {
  double math = 1;
  double physics = 2;
  double chemistry = 3;
  map<string, double> electives = 4;
} 