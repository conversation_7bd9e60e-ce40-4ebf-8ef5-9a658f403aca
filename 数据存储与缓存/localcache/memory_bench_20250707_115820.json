{"Time":"2025-07-07T11:58:21.352285+08:00","Action":"start","Package":"localcache"}
{"Time":"2025-07-07T11:58:22.003919+08:00","Action":"output","Package":"localcache","Output":"goos: darwin\n"}
{"Time":"2025-07-07T11:58:22.005018+08:00","Action":"output","Package":"localcache","Output":"goarch: amd64\n"}
{"Time":"2025-07-07T11:58:22.005039+08:00","Action":"output","Package":"localcache","Output":"pkg: localcache\n"}
{"Time":"2025-07-07T11:58:22.005049+08:00","Action":"output","Package":"localcache","Output":"cpu: Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz\n"}
{"Time":"2025-07-07T11:58:22.005077+08:00","Action":"run","Package":"localcache","Test":"BenchmarkMemoryUsage"}
{"Time":"2025-07-07T11:58:22.005086+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage","Output":"=== RUN   BenchmarkMemoryUsage\n"}
{"Time":"2025-07-07T11:58:22.005102+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage","Output":"BenchmarkMemoryUsage\n"}
{"Time":"2025-07-07T11:58:22.048167+08:00","Action":"run","Package":"localcache","Test":"BenchmarkMemoryUsage/GoCache"}
{"Time":"2025-07-07T11:58:22.048219+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/GoCache","Output":"=== RUN   BenchmarkMemoryUsage/GoCache\n"}
{"Time":"2025-07-07T11:58:22.048276+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/GoCache","Output":"BenchmarkMemoryUsage/GoCache\n"}
{"Time":"2025-07-07T11:58:22.529727+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/GoCache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04813 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:22.989688+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04746 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:23.472886+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04262 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:23.953871+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04814 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:24.397246+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04781 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:24.809803+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04722 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:25.239609+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04778 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:25.670182+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04649 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:26.102571+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04072 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:26.52216+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04543 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:26.944651+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04347 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:27.377967+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.05136 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:27.788128+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04741 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:28.196058+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04491 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:28.615232+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.05043 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:29.06866+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.07211 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:29.491985+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.04592 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:29.973084+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.05822 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:30.657504+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.05533 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:31.141501+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoCache-12         \t1000000000\t         0.05327 ns/op\t      88 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:31.141579+08:00","Action":"run","Package":"localcache","Test":"BenchmarkMemoryUsage/BigCache"}
{"Time":"2025-07-07T11:58:31.141591+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/BigCache","Output":"=== RUN   BenchmarkMemoryUsage/BigCache\n"}
{"Time":"2025-07-07T11:58:31.14162+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/BigCache","Output":"BenchmarkMemoryUsage/BigCache\n"}
{"Time":"2025-07-07T11:58:32.311095+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/BigCache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.08060 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:33.094485+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.08993 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:33.859401+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07740 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:34.645075+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07975 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:35.425212+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07883 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:36.1743+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07289 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:36.964107+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07310 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:37.749993+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07455 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:38.517586+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07798 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:39.291017+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.08215 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:40.085096+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.08344 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:40.814309+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.06872 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:41.527564+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.06881 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:42.250948+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07047 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:42.976082+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07360 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:43.733915+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07036 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:44.457186+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07029 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:45.175167+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07273 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:45.901796+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.06802 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:46.648446+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/BigCache-12        \t1000000000\t         0.07520 ns/op\t    3365 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:46.648503+08:00","Action":"run","Package":"localcache","Test":"BenchmarkMemoryUsage/FreeCache"}
{"Time":"2025-07-07T11:58:46.648513+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/FreeCache","Output":"=== RUN   BenchmarkMemoryUsage/FreeCache\n"}
{"Time":"2025-07-07T11:58:46.648547+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/FreeCache","Output":"BenchmarkMemoryUsage/FreeCache\n"}
{"Time":"2025-07-07T11:58:47.15582+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/FreeCache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05347 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:47.641444+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05518 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:48.176531+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05779 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:48.676832+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05971 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:49.180057+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05713 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:49.681703+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05652 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:50.188573+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.06370 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:50.689475+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05275 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:51.198115+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05546 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:51.6851+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05892 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:52.209038+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05370 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:52.695216+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05241 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:53.208299+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05991 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:53.712894+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.06065 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:54.223797+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05902 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:54.745264+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05660 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:55.261604+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.06345 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:55.764673+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05378 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:56.26766+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.06420 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:56.762561+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FreeCache-12       \t1000000000\t         0.05191 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:56.762613+08:00","Action":"run","Package":"localcache","Test":"BenchmarkMemoryUsage/Ristretto"}
{"Time":"2025-07-07T11:58:56.762633+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/Ristretto","Output":"=== RUN   BenchmarkMemoryUsage/Ristretto\n"}
{"Time":"2025-07-07T11:58:56.762642+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/Ristretto","Output":"BenchmarkMemoryUsage/Ristretto\n"}
{"Time":"2025-07-07T11:58:57.429806+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/Ristretto","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.05620 ns/op\t     698 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:58.091099+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07145 ns/op\t     685 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:58.748893+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.05927 ns/op\t     724 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:58:59.484149+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.05998 ns/op\t     703 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:00.199958+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.05927 ns/op\t     696 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:00.86347+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.06920 ns/op\t     687 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:01.528687+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07618 ns/op\t     684 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:02.24925+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.06323 ns/op\t     694 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:02.957185+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07608 ns/op\t     682 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:03.662383+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.08100 ns/op\t     684 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:04.315616+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07497 ns/op\t     682 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:05.040544+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07639 ns/op\t     683 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:05.76496+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07456 ns/op\t     684 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:06.494273+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07345 ns/op\t     683 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:07.265098+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.08825 ns/op\t     685 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:08.02502+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07865 ns/op\t     683 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:08.758426+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07741 ns/op\t     677 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:09.481172+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07188 ns/op\t     681 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:10.189789+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07484 ns/op\t     683 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:10.897689+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Ristretto-12       \t1000000000\t         0.07316 ns/op\t     685 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:10.897737+08:00","Action":"run","Package":"localcache","Test":"BenchmarkMemoryUsage/FastCache"}
{"Time":"2025-07-07T11:59:10.897746+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/FastCache","Output":"=== RUN   BenchmarkMemoryUsage/FastCache\n"}
{"Time":"2025-07-07T11:59:10.897753+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/FastCache","Output":"BenchmarkMemoryUsage/FastCache\n"}
{"Time":"2025-07-07T11:59:11.098586+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/FastCache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02654 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:11.291086+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02584 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:11.494438+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.01926 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:11.689941+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.01979 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:11.880932+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02567 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:12.066525+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.01871 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:12.28498+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02738 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:12.491221+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.01957 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:12.675177+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02173 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:12.862933+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02636 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:13.044342+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02087 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:13.248542+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02354 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:13.431915+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02106 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:13.609423+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02407 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:13.792888+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02284 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:13.985579+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02617 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:14.213693+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.03073 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:14.432301+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02258 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:14.636385+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02607 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:14.846383+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/FastCache-12       \t1000000000\t         0.02486 ns/op\t      39 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:14.846432+08:00","Action":"run","Package":"localcache","Test":"BenchmarkMemoryUsage/GoLRU"}
{"Time":"2025-07-07T11:59:14.846466+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/GoLRU","Output":"=== RUN   BenchmarkMemoryUsage/GoLRU\n"}
{"Time":"2025-07-07T11:59:14.846473+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/GoLRU","Output":"BenchmarkMemoryUsage/GoLRU\n"}
{"Time":"2025-07-07T11:59:15.362696+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/GoLRU","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.06842 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:15.881322+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05677 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:16.372333+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05669 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:16.860041+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05668 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:17.366689+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05916 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:17.871812+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.06262 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:18.394113+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05683 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:18.890369+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05605 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:19.505047+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05991 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:20.022562+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05542 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:20.519907+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.06266 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:21.269138+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.06117 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:21.793368+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.06690 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:22.308949+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05508 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:22.834086+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.06061 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:23.335435+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05504 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:23.843082+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.06343 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:24.359461+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.06141 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:24.859351+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05588 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:25.351266+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/GoLRU-12           \t1000000000\t         0.05644 ns/op\t       0 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:25.351307+08:00","Action":"run","Package":"localcache","Test":"BenchmarkMemoryUsage/Otter"}
{"Time":"2025-07-07T11:59:25.351316+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/Otter","Output":"=== RUN   BenchmarkMemoryUsage/Otter\n"}
{"Time":"2025-07-07T11:59:25.351322+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/Otter","Output":"BenchmarkMemoryUsage/Otter\n"}
{"Time":"2025-07-07T11:59:25.995824+08:00","Action":"output","Package":"localcache","Test":"BenchmarkMemoryUsage/Otter","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06059 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:26.642111+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06466 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:27.297211+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06576 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:27.957647+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.07129 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:28.602422+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.07014 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:29.225556+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06852 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:29.864897+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06659 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:30.51285+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.07042 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:31.161301+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06113 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:31.807477+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06354 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:32.461484+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06654 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:33.137916+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06322 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:33.788431+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06151 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:34.418606+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06153 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:35.042057+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06193 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:35.661888+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.05871 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:36.308816+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06159 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:36.962437+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06936 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:37.596235+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06716 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:38.249576+08:00","Action":"output","Package":"localcache","Output":"BenchmarkMemoryUsage/Otter-12           \t1000000000\t         0.06144 ns/op\t     110 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-07T11:59:38.249674+08:00","Action":"output","Package":"localcache","Output":"PASS\n"}
{"Time":"2025-07-07T11:59:38.280856+08:00","Action":"output","Package":"localcache","Output":"ok  \tlocalcache\t76.923s\n"}
{"Time":"2025-07-07T11:59:38.281281+08:00","Action":"pass","Package":"localcache","Elapsed":76.927}
