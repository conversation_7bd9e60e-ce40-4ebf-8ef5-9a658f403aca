// Code generated by the FlatBuffers compiler. DO NOT EDIT.

package Flat

import (
	flatbuffers "github.com/google/flatbuffers/go"
)

type MetadataEntry struct {
	_tab flatbuffers.Table
}

func GetRootAsMetadataEntry(buf []byte, offset flatbuffers.UOffsetT) *MetadataEntry {
	n := flatbuffers.GetUOffsetT(buf[offset:])
	x := &MetadataEntry{}
	x.Init(buf, n+offset)
	return x
}

func FinishMetadataEntryBuffer(builder *flatbuffers.Builder, offset flatbuffers.UOffsetT) {
	builder.Finish(offset)
}

func GetSizePrefixedRootAsMetadataEntry(buf []byte, offset flatbuffers.UOffsetT) *MetadataEntry {
	n := flatbuffers.GetUOffsetT(buf[offset+flatbuffers.SizeUint32:])
	x := &MetadataEntry{}
	x.Init(buf, n+offset+flatbuffers.SizeUint32)
	return x
}

func FinishSizePrefixedMetadataEntryBuffer(builder *flatbuffers.Builder, offset flatbuffers.UOffsetT) {
	builder.FinishSizePrefixed(offset)
}

func (rcv *MetadataEntry) Init(buf []byte, i flatbuffers.UOffsetT) {
	rcv._tab.Bytes = buf
	rcv._tab.Pos = i
}

func (rcv *MetadataEntry) Table() flatbuffers.Table {
	return rcv._tab
}

func (rcv *MetadataEntry) Key() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(4))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func (rcv *MetadataEntry) Value() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(6))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func MetadataEntryStart(builder *flatbuffers.Builder) {
	builder.StartObject(2)
}
func MetadataEntryAddKey(builder *flatbuffers.Builder, key flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(0, flatbuffers.UOffsetT(key), 0)
}
func MetadataEntryAddValue(builder *flatbuffers.Builder, value flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(1, flatbuffers.UOffsetT(value), 0)
}
func MetadataEntryEnd(builder *flatbuffers.Builder) flatbuffers.UOffsetT {
	return builder.EndObject()
}
