// Code generated by the FlatBuffers compiler. DO NOT EDIT.

package Flat

import (
	flatbuffers "github.com/google/flatbuffers/go"
)

type Scores struct {
	_tab flatbuffers.Table
}

func GetRootAsScores(buf []byte, offset flatbuffers.UOffsetT) *Scores {
	n := flatbuffers.GetUOffsetT(buf[offset:])
	x := &Scores{}
	x.Init(buf, n+offset)
	return x
}

func FinishScoresBuffer(builder *flatbuffers.Builder, offset flatbuffers.UOffsetT) {
	builder.Finish(offset)
}

func GetSizePrefixedRootAsScores(buf []byte, offset flatbuffers.UOffsetT) *Scores {
	n := flatbuffers.GetUOffsetT(buf[offset+flatbuffers.SizeUint32:])
	x := &Scores{}
	x.Init(buf, n+offset+flatbuffers.SizeUint32)
	return x
}

func FinishSizePrefixedScoresBuffer(builder *flatbuffers.Builder, offset flatbuffers.UOffsetT) {
	builder.FinishSizePrefixed(offset)
}

func (rcv *Scores) Init(buf []byte, i flatbuffers.UOffsetT) {
	rcv._tab.Bytes = buf
	rcv._tab.Pos = i
}

func (rcv *Scores) Table() flatbuffers.Table {
	return rcv._tab
}

func (rcv *Scores) Math() float64 {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(4))
	if o != 0 {
		return rcv._tab.GetFloat64(o + rcv._tab.Pos)
	}
	return 0.0
}

func (rcv *Scores) MutateMath(n float64) bool {
	return rcv._tab.MutateFloat64Slot(4, n)
}

func (rcv *Scores) Physics() float64 {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(6))
	if o != 0 {
		return rcv._tab.GetFloat64(o + rcv._tab.Pos)
	}
	return 0.0
}

func (rcv *Scores) MutatePhysics(n float64) bool {
	return rcv._tab.MutateFloat64Slot(6, n)
}

func (rcv *Scores) Chemistry() float64 {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(8))
	if o != 0 {
		return rcv._tab.GetFloat64(o + rcv._tab.Pos)
	}
	return 0.0
}

func (rcv *Scores) MutateChemistry(n float64) bool {
	return rcv._tab.MutateFloat64Slot(8, n)
}

func (rcv *Scores) Electives(obj *ElectiveScore, j int) bool {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(10))
	if o != 0 {
		x := rcv._tab.Vector(o)
		x += flatbuffers.UOffsetT(j) * 4
		x = rcv._tab.Indirect(x)
		obj.Init(rcv._tab.Bytes, x)
		return true
	}
	return false
}

func (rcv *Scores) ElectivesLength() int {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(10))
	if o != 0 {
		return rcv._tab.VectorLen(o)
	}
	return 0
}

func ScoresStart(builder *flatbuffers.Builder) {
	builder.StartObject(4)
}
func ScoresAddMath(builder *flatbuffers.Builder, math float64) {
	builder.PrependFloat64Slot(0, math, 0.0)
}
func ScoresAddPhysics(builder *flatbuffers.Builder, physics float64) {
	builder.PrependFloat64Slot(1, physics, 0.0)
}
func ScoresAddChemistry(builder *flatbuffers.Builder, chemistry float64) {
	builder.PrependFloat64Slot(2, chemistry, 0.0)
}
func ScoresAddElectives(builder *flatbuffers.Builder, electives flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(3, flatbuffers.UOffsetT(electives), 0)
}
func ScoresStartElectivesVector(builder *flatbuffers.Builder, numElems int) flatbuffers.UOffsetT {
	return builder.StartVector(4, numElems, 4)
}
func ScoresEnd(builder *flatbuffers.Builder) flatbuffers.UOffsetT {
	return builder.EndObject()
}
