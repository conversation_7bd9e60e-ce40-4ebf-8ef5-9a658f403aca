package localcache

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/VictoriaMetrics/fastcache"
	"github.com/allegro/bigcache/v3"
	"github.com/coocood/freecache"
	"github.com/dgraph-io/ristretto"
	lru "github.com/hashicorp/golang-lru"
	"github.com/maypok86/otter"
	gocache "github.com/patrickmn/go-cache"
)

// ===== Cache Policy Interface =====
type Policy interface {
	Get(key string) (string, bool)
	Set(key string, value string)
	Name() string
	Init(capacity int) error
	Close()
}

// AccessEvent represents a cache access event
type AccessEvent struct {
	key string
}

func NewAccessEvent(key string) AccessEvent {
	return AccessEvent{key: key}
}

func (e AccessEvent) Key() string {
	return e.key
}

// PolicyStats tracks hit/miss statistics
type PolicyStats struct {
	policy Policy
	hits   uint64
	misses uint64
}

func NewPolicyStats(p Policy) *PolicyStats {
	return &PolicyStats{
		policy: p,
	}
}

func (ps *PolicyStats) Record(event AccessEvent) {
	key := event.Key()
	_, found := ps.policy.Get(key)
	if found {
		ps.hits++
	} else {
		ps.policy.Set(key, key) // Use key as value
		ps.misses++
	}
}

func (ps *PolicyStats) Name() string {
	return ps.policy.Name()
}

func (ps *PolicyStats) Init(capacity int) error {
	return ps.policy.Init(capacity)
}

func (ps *PolicyStats) HitRatio() float64 {
	total := ps.hits + ps.misses
	if total == 0 {
		return 0
	}
	return 100.0 * float64(ps.hits) / float64(total)
}

func (ps *PolicyStats) Close() {
	ps.policy.Close()
}

// SimulationResult represents the result of a cache simulation
type SimulationResult struct {
	name     string
	capacity int
	hitRatio float64
}

func NewSimulationResult(name string, capacity int, hitRatio float64) SimulationResult {
	return SimulationResult{
		name:     name,
		capacity: capacity,
		hitRatio: hitRatio,
	}
}

func (r SimulationResult) Name() string      { return r.name }
func (r SimulationResult) Capacity() int     { return r.capacity }
func (r SimulationResult) HitRatio() float64 { return r.hitRatio }

// TraceGenerator generates access events
type TraceGenerator interface {
	Generate() <-chan AccessEvent
}

// ZipfTraceGenerator generates access events following Zipf distribution
type ZipfTraceGenerator struct {
	s     float64
	v     float64
	imax  uint64
	limit uint64
	seed  int64
}

func NewZipfTraceGenerator(s, v float64, imax, limit uint64, seed int64) *ZipfTraceGenerator {
	return &ZipfTraceGenerator{
		s:     s,
		v:     v,
		imax:  imax,
		limit: limit,
		seed:  seed,
	}
}

func (g *ZipfTraceGenerator) Generate() <-chan AccessEvent {
	ch := make(chan AccessEvent, 1000) // Buffered channel
	go func() {
		defer close(ch)

		r := rand.New(rand.NewSource(g.seed))
		zipf := rand.NewZipf(r, g.s, g.v, g.imax)

		for i := uint64(0); i < g.limit; i++ {
			key := fmt.Sprintf("key%d", zipf.Uint64())
			ch <- NewAccessEvent(key)
		}
	}()
	return ch
}

// CacheSimulator runs cache simulations
type CacheSimulator struct {
	capacities []int
	policies   []Policy
	generator  TraceGenerator
}

func NewCacheSimulator(capacities []int, policies []Policy, generator TraceGenerator) *CacheSimulator {
	return &CacheSimulator{
		capacities: capacities,
		policies:   policies,
		generator:  generator,
	}
}

func (s *CacheSimulator) Simulate() ([]SimulationResult, error) {
	var results []SimulationResult

	for _, capacity := range s.capacities {
		for _, policy := range s.policies {
			// Clone policy for this simulation
			clonedPolicy := s.clonePolicy(policy)
			if err := clonedPolicy.Init(capacity); err != nil {
				return nil, fmt.Errorf("failed to init policy %s: %w", clonedPolicy.Name(), err)
			}

			stats := NewPolicyStats(clonedPolicy)

			// Run simulation
			events := s.generator.Generate()
			for event := range events {
				stats.Record(event)
			}

			result := NewSimulationResult(
				stats.Name(),
				capacity,
				stats.HitRatio(),
			)
			results = append(results, result)

			stats.Close()
		}
	}

	return results, nil
}

// clonePolicy creates a new instance of the policy
func (s *CacheSimulator) clonePolicy(policy Policy) Policy {
	switch policy.(type) {
	case *BigCachePolicy:
		return &BigCachePolicy{}
	case *FreeCachePolicy:
		return &FreeCachePolicy{}
	case *RistrettoPolicy:
		return &RistrettoPolicy{}
	case *GolangLRUPolicy:
		return &GolangLRUPolicy{}
	case *GoCachePolicy:
		return &GoCachePolicy{}
	case *OtterPolicy:
		return &OtterPolicy{}
	case *FastCachePolicy:
		return &FastCachePolicy{}
	case *SyncMapPolicy:
		return &SyncMapPolicy{}
	default:
		panic(fmt.Sprintf("unknown policy type: %T", policy))
	}
}

// ===== Policy Implementations =====

// CapacityControlledBigCache 容量控制的BigCache包装器
type CapacityControlledBigCache struct {
	client   *bigcache.BigCache
	capacity int
	keys     map[string]bool
	keyList  []string
	mu       sync.RWMutex
}

func (c *CapacityControlledBigCache) Get(key string) (string, bool) {
	_, err := c.client.Get(key)
	return key, err == nil
}

func (c *CapacityControlledBigCache) Set(key string, value string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果key已存在，直接更新
	if c.keys[key] {
		_ = c.client.Set(key, []byte(value))
		return
	}

	// 如果达到容量限制，先删除最旧的key (FIFO)
	if len(c.keyList) >= c.capacity && c.capacity > 0 {
		oldestKey := c.keyList[0]
		c.keyList = c.keyList[1:]
		delete(c.keys, oldestKey)
		_ = c.client.Delete(oldestKey)
	}

	// 添加新key
	c.keys[key] = true
	c.keyList = append(c.keyList, key)
	_ = c.client.Set(key, []byte(value))
}

func (c *CapacityControlledBigCache) Close() {
	if c.client != nil {
		_ = c.client.Close()
	}
}

// CapacityControlledGoCache 容量控制的GoCache包装器
type CapacityControlledGoCache struct {
	client   *gocache.Cache
	capacity int
	keys     map[string]bool
	keyList  []string
	mu       sync.RWMutex
}

func (c *CapacityControlledGoCache) Get(key string) (string, bool) {
	val, found := c.client.Get(key)
	if !found {
		return "", false
	}
	return val.(string), true
}

func (c *CapacityControlledGoCache) Set(key string, value string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果key已存在，直接更新
	if c.keys[key] {
		c.client.Set(key, value, gocache.NoExpiration)
		return
	}

	// 如果达到容量限制，先删除最旧的key (FIFO)
	if len(c.keyList) >= c.capacity && c.capacity > 0 {
		oldestKey := c.keyList[0]
		c.keyList = c.keyList[1:]
		delete(c.keys, oldestKey)
		c.client.Delete(oldestKey)
	}

	// 添加新key
	c.keys[key] = true
	c.keyList = append(c.keyList, key)
	c.client.Set(key, value, gocache.NoExpiration)
}

func (c *CapacityControlledGoCache) Close() {
	// No explicit cleanup needed
}

// CapacityControlledSyncMap 容量控制的sync.Map包装器
type CapacityControlledSyncMap struct {
	client   sync.Map
	capacity int
	keys     map[string]bool
	keyList  []string
	mu       sync.RWMutex
}

func (c *CapacityControlledSyncMap) Get(key string) (string, bool) {
	val, ok := c.client.Load(key)
	if !ok {
		return "", false
	}
	return val.(string), true
}

func (c *CapacityControlledSyncMap) Set(key string, value string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果key已存在，直接更新
	if c.keys[key] {
		c.client.Store(key, value)
		return
	}

	// 如果达到容量限制，先删除最旧的key (FIFO)
	if len(c.keyList) >= c.capacity && c.capacity > 0 {
		oldestKey := c.keyList[0]
		c.keyList = c.keyList[1:]
		delete(c.keys, oldestKey)
		c.client.Delete(oldestKey)
	}

	// 添加新key
	c.keys[key] = true
	c.keyList = append(c.keyList, key)
	c.client.Store(key, value)
}

func (c *CapacityControlledSyncMap) Close() {
	// No explicit cleanup needed
}

// CapacityControlledFastCache 容量控制的FastCache包装器
type CapacityControlledFastCache struct {
	client   *fastcache.Cache
	capacity int
	keys     map[string]bool
	keyList  []string
	mu       sync.RWMutex
}

func (c *CapacityControlledFastCache) Get(key string) (string, bool) {
	val := c.client.Get(nil, []byte(key))
	return string(val), val != nil
}

func (c *CapacityControlledFastCache) Set(key string, value string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果key已存在，直接更新
	if c.keys[key] {
		c.client.Set([]byte(key), []byte(value))
		return
	}

	// 如果达到容量限制，先删除最旧的key (FIFO)
	if len(c.keyList) >= c.capacity && c.capacity > 0 {
		oldestKey := c.keyList[0]
		c.keyList = c.keyList[1:]
		delete(c.keys, oldestKey)
		c.client.Del([]byte(oldestKey))
	}

	// 添加新key
	c.keys[key] = true
	c.keyList = append(c.keyList, key)
	c.client.Set([]byte(key), []byte(value))
}

func (c *CapacityControlledFastCache) Close() {
	if c.client != nil {
		c.client.Reset()
	}
}

// BigCachePolicy wraps allegro/bigcache using native capacity control
type BigCachePolicy struct {
	client *bigcache.BigCache
}

func (p *BigCachePolicy) Get(key string) (string, bool) {
	_, err := p.client.Get(key)
	return key, err == nil
}

func (p *BigCachePolicy) Set(key string, value string) {
	_ = p.client.Set(key, []byte(value))
}

func (p *BigCachePolicy) Name() string {
	return "bigcache"
}

func (p *BigCachePolicy) Init(capacity int) error {
	config := bigcache.DefaultConfig(10 * time.Minute)

	// 使用BigCache原生的容量控制机制
	// 通过精确的内存限制来控制容量
	config.MaxEntriesInWindow = capacity // 窗口内最大条目数
	config.MaxEntrySize = 500            // 条目大小限制

	// 关键：根据容量计算更严格的内存限制
	// 使用更小的字节估算来强制BigCache进行淘汰
	estimatedBytesPerEntry := 80 // 减少估算值
	maxMemoryMB := (capacity * estimatedBytesPerEntry) / (1024 * 1024)
	if maxMemoryMB < 1 {
		maxMemoryMB = 1 // 至少1MB
	}
	// 进一步减少内存限制来强制淘汰
	maxMemoryMB = maxMemoryMB / 2
	if maxMemoryMB < 1 {
		maxMemoryMB = 1
	}
	config.HardMaxCacheSize = maxMemoryMB // 更严格的内存限制
	config.Shards = 16                    // 合理的分片数

	client, err := bigcache.New(context.Background(), config)
	if err != nil {
		return err
	}

	p.client = client
	return nil
}

func (p *BigCachePolicy) Close() {
	if p.client != nil {
		_ = p.client.Close()
	}
}

// FreeCachePolicy wraps coocood/freecache
type FreeCachePolicy struct {
	client *freecache.Cache
}

func (p *FreeCachePolicy) Get(key string) (string, bool) {
	val, err := p.client.Get([]byte(key))
	return string(val), err == nil
}

func (p *FreeCachePolicy) Set(key string, value string) {
	_ = p.client.Set([]byte(key), []byte(value), 0)
}

func (p *FreeCachePolicy) Name() string {
	return "freecache"
}

func (p *FreeCachePolicy) Init(capacity int) error {
	memorySize := capacity * 150
	if memorySize < 1024*1024 {
		memorySize = 1024 * 1024
	}
	p.client = freecache.NewCache(memorySize)
	return nil
}

func (p *FreeCachePolicy) Close() {
	if p.client != nil {
		p.client.Clear()
	}
}

// RistrettoPolicy wraps dgraph-io/ristretto
type RistrettoPolicy struct {
	client *ristretto.Cache
}

func (p *RistrettoPolicy) Get(key string) (string, bool) {
	val, found := p.client.Get(key)
	if !found {
		return "", false
	}
	return val.(string), true
}

func (p *RistrettoPolicy) Set(key string, value string) {
	p.client.Set(key, value, 1)
	p.client.Wait() // 关键修复：等待异步操作完成，确保测试准确性
}

func (p *RistrettoPolicy) Name() string {
	return "ristretto"
}

func (p *RistrettoPolicy) Init(capacity int) error {
	client, err := ristretto.NewCache(&ristretto.Config{
		NumCounters:        int64(capacity) * 10,
		MaxCost:            int64(capacity),
		BufferItems:        64,
		IgnoreInternalCost: true, // 关键修复：忽略内部成本计算
	})
	if err != nil {
		return err
	}
	p.client = client
	return nil
}

func (p *RistrettoPolicy) Close() {
	if p.client != nil {
		p.client.Close()
	}
}

// GolangLRUPolicy wraps hashicorp/golang-lru
type GolangLRUPolicy struct {
	client *lru.Cache
}

func (p *GolangLRUPolicy) Get(key string) (string, bool) {
	val, found := p.client.Get(key)
	if !found {
		return "", false
	}
	return val.(string), true
}

func (p *GolangLRUPolicy) Set(key string, value string) {
	p.client.Add(key, value)
}

func (p *GolangLRUPolicy) Name() string {
	return "golang-lru"
}

func (p *GolangLRUPolicy) Init(capacity int) error {
	client, err := lru.New(capacity)
	if err != nil {
		return err
	}
	p.client = client
	return nil
}

func (p *GolangLRUPolicy) Close() {
	// No explicit cleanup needed
}

// GoCachePolicy wraps patrickmn/go-cache using native expiration for capacity simulation
type GoCachePolicy struct {
	client         *gocache.Cache
	capacity       int
	itemCount      int
	expirationTime time.Duration
}

func (p *GoCachePolicy) Get(key string) (string, bool) {
	val, found := p.client.Get(key)
	if !found {
		return "", false
	}
	return val.(string), true
}

func (p *GoCachePolicy) Set(key string, value string) {
	// 使用Go-Cache的原生特性：当缓存项超过预期容量时，使用更短的过期时间
	if _, exists := p.client.Get(key); !exists {
		p.itemCount++
	}

	// 根据当前使用容量动态调整过期时间来模拟容量限制
	var expiration time.Duration
	if p.itemCount > p.capacity {
		// 超过容量时使用很短的过期时间，促进淘汰
		expiration = time.Duration(p.capacity) * time.Millisecond / time.Duration(p.itemCount)
		if expiration < 10*time.Millisecond {
			expiration = 10 * time.Millisecond
		}
	} else {
		// 未超过容量时使用较长的过期时间
		expiration = 10 * time.Minute
	}

	p.client.Set(key, value, expiration)
}

func (p *GoCachePolicy) Name() string {
	return "go-cache"
}

func (p *GoCachePolicy) Init(capacity int) error {
	// 使用Go-Cache的原生过期机制，设置较频繁的清理来模拟容量限制
	cleanupInterval := 100 * time.Millisecond
	p.client = gocache.New(5*time.Minute, cleanupInterval)
	p.capacity = capacity
	p.itemCount = 0
	return nil
}

func (p *GoCachePolicy) Close() {
	// No explicit cleanup needed
}

// OtterPolicy wraps maypok86/otter
type OtterPolicy struct {
	client otter.Cache[string, string]
}

func (p *OtterPolicy) Get(key string) (string, bool) {
	return p.client.Get(key)
}

func (p *OtterPolicy) Set(key string, value string) {
	p.client.Set(key, value)
}

func (p *OtterPolicy) Name() string {
	return "otter"
}

func (p *OtterPolicy) Init(capacity int) error {
	client, err := otter.MustBuilder[string, string](capacity).Build()
	if err != nil {
		return err
	}
	p.client = client
	return nil
}

func (p *OtterPolicy) Close() {
	p.client.Close()
}

// FastCachePolicy wraps VictoriaMetrics/fastcache using native memory limits
type FastCachePolicy struct {
	client *fastcache.Cache
}

func (p *FastCachePolicy) Get(key string) (string, bool) {
	val := p.client.Get(nil, []byte(key))
	return string(val), val != nil
}

func (p *FastCachePolicy) Set(key string, value string) {
	p.client.Set([]byte(key), []byte(value))
}

func (p *FastCachePolicy) Name() string {
	return "fastcache"
}

func (p *FastCachePolicy) Init(capacity int) error {
	// 使用FastCache的原生内存限制来控制容量，设置更严格的限制
	// 根据容量计算更小的内存大小，强制FastCache进行淘汰
	// 使用很小的字节估算来强制频繁淘汰
	estimatedBytesPerEntry := 25 // 大幅减少字节估算
	memorySize := capacity * estimatedBytesPerEntry

	// 设置最小值避免过小
	if memorySize < 2*1024 {
		memorySize = 2 * 1024 // 最小2KB
	}

	p.client = fastcache.New(memorySize)
	return nil
}

func (p *FastCachePolicy) Close() {
	// FastCache会自动处理清理
}

// SyncMapPolicy wraps sync.Map - maintains its native unlimited capacity nature
type SyncMapPolicy struct {
	client sync.Map
}

func (p *SyncMapPolicy) Get(key string) (string, bool) {
	val, ok := p.client.Load(key)
	if !ok {
		return "", false
	}
	return val.(string), true
}

func (p *SyncMapPolicy) Set(key string, value string) {
	p.client.Store(key, value)
}

func (p *SyncMapPolicy) Name() string {
	return "sync-map"
}

func (p *SyncMapPolicy) Init(capacity int) error {
	// sync.Map的本质就是无容量限制 - 这是它的设计特性
	// capacity参数对sync.Map无意义，保持其原生行为
	return nil
}

func (p *SyncMapPolicy) Close() {
	// No explicit cleanup needed
}
