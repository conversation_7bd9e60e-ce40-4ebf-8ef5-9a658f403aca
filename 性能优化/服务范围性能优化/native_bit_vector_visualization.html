<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原生位图 (NativeBitVector) 方案可视化</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #0056b3;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        code {
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
        }
        .vector-container {
            display: flex;
            flex-wrap: wrap;
            border: 1px solid #ccc;
            padding: 5px;
            margin-top: 15px;
            border-radius: 4px;
        }
        .word {
            border: 1px solid #adb5bd;
            padding: 8px;
            margin: 2px;
            font-size: 12px;
            min-width: 60px;
            text-align: center;
            border-radius: 3px;
            background-color: #fff;
            transition: all 0.2s ease-in-out;
        }
        .word.origin-partition { background-color: #e7f5ff; border-color: #90cfff; }
        .word.dest-partition { background-color: #e6fcf5; border-color: #8ce9bf; }
        .word.highlight-strong { background-color: #007bff; color: white; transform: scale(1.1); border-color: #0056b3; }
        .word.highlight-strong-dest { background-color: #20c997; color: white; transform: scale(1.1); border-color: #1a9875; }

        .bit-grid-container {
            margin-top: 20px;
        }
        .bit-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 3px;
            border: 2px solid #333;
            padding: 5px;
            max-width: 400px;
            background-color: #f1f3f5;
        }
        .bit {
            width: 100%;
            padding-bottom: 100%; /* Aspect ratio 1:1 */
            position: relative;
            background-color: #fff;
            border: 1px solid #ced4da;
            transition: all 0.2s ease-in-out;
        }
        .bit-inner {
            position: absolute;
            top: 0; right: 0; bottom: 0; left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #888;
        }
        .bit.highlight-bit {
            background-color: #007bff;
            border-color: #0056b3;
        }
        .bit.highlight-bit-dest {
            background-color: #20c997;
            border-color: #1a9875;
        }
        .bit.highlight-bit .bit-inner, .bit.highlight-bit-dest .bit-inner {
            color: white;
            font-weight: bold;
        }
        .explanation {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        .calc {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            flex-shrink: 0;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>原生位图 (NativeBitVector) 方案可视化</h1>
        
        <h2>1. 整体数据结构</h2>
        <p>方案的核心思想是，为每个渠道创建一个超长的“二进制位清单”，总长度为20000位。清单上的每一个“位”（bit）都代表一个可能性：</p>
        <ul>
            <li><span style="color: #007bff;"><b>前10000位 (位索引 0 至 9999)</b></span>: 用于标记 <b>出发地 (Origin)</b>。如果第 <code>i</code> 位为1，代表地址 <code>i</code> 是一个可用的出发地。</li>
            <li><span style="color: #20c997;"><b>后10000位 (位索引 10000 至 19999)</b></span>: 用于标记 <b>目的地 (Dest)</b>。如果第 <code>j</code> 位为1，代表地址 <code>j - 10000</code> 是一个可用的目的地。</li>
        </ul>
        <p>为了在内存中实现这个20000位的清单，我们使用了一个原生的 <code>[]uint64</code> 切片。每个 <code>uint64</code> 可以存储64个位，因此我们的切片长度为 <code>ceil(20000 / 64) = 313</code>。</p>
        <div class="vector-container">
            <div class="word origin-partition" id="word-0">Word 0</div>
            <div class="word origin-partition" id="word-1">Word 1</div>
            <div class="word origin-partition" id="word-2">Word 2</div>
            <div class="word">...</div>
            <div class="word origin-partition">Word 155</div>
            <div class="word dest-partition">Word 156</div>
            <div class="word dest-partition" id="word-157">Word 157</div>
            <div class="word">...</div>
            <div class="word dest-partition">Word 312</div>
        </div>

        <h2>2. 构建过程示例</h2>
        <p>假设地址ID的取值范围是 <code>0</code> 到 <code>9999</code>。现在，我们需要在一个渠道的位图中，标记 <code>Origin = 130</code> 和 <code>Dest = 65</code> 为可服务。</p>
        
        <h3>标记 Origin = 130</h3>
        <div class="explanation">
            <div class="calc">
                <b>计算:</b><br>
                <code>wordIndex = 130 / 64 = 2</code><br>
                <code>bitIndex  = 130 % 64 = 2</code>
            </div>
            <div class="bit-grid-container">
                <p>这意味着我们需要在 <b>Word 2</b> 的 <b>第2个位</b> (从0开始) 上进行标记。</p>
                <div class="bit-grid" id="origin-grid"></div>
            </div>
        </div>

        <h3 style="margin-top: 20px;">标记 Dest = 65 (映射到 位索引 10065)</h3>
        <div class="explanation">
            <div class="calc">
                <b>计算:</b><br>
                为了将目的地地址存入后半段区域，我们先计算它在整个位图中的绝对索引：<br>
                <code>绝对索引 = Dest地址 + 偏移量 = 65 + 10000 = 10065</code><br>
                然后基于这个绝对索引进行计算：<br>
                <code>wordIndex = 10065 / 64 = 157</code><br>
                <code>bitIndex  = 10065 % 64 = 17</code>
            </div>
            <div class="bit-grid-container">
                <p>这意味着我们需要在 <b>Word 157</b> 的 <b>第17个位</b> 上进行标记。</p>
                <div class="bit-grid" id="dest-grid"></div>
            </div>
        </div>
        <p style="margin-top: 15px;">执行的操作是 <code>vector[wordIndex] |= (1 << bitIndex)</code>，它能确保在不影响其他位的情况下，精确地将目标位置为1。</p>

        <h2>3. 无分支查询示例</h2>
        <p>现在，我们查询 <code>IsServiceable(Product, Origin=130, Dest=65)</code> 是否为真。查询过程就是分别检查 <b>位索引 130</b> 和 <b>位索引 10065</b> 是否都为1。</p>
        <ol>
            <li>
                <b>检查 Origin 130:</b>
                <p>我们定位到 <code>Word 2</code>, <code>Bit 2</code>。执行 <code>(vector[2] >> 2) & 1</code>。由于该位之前已被设为1，该表达式的结果为 <b>1</b>。</p>
            </li>
            <li>
                <b>检查 Dest 65:</b>
                <p>我们定位到 <code>Word 157</code>, <code>Bit 17</code>。执行 <code>(vector[157] >> 17) & 1</code>。由于该位之前也已被设为1，该表达式的结果为 <b>1</b>。</p>
            </li>
            <li>
                <b>合并结果:</b>
                <p>执行最终的位运算 <code>checkOrigin & checkDest</code>，即 <code>1 & 1</code>，结果为 <b>1</b>。</p>
            </li>
            <li>
                <b>得出结论:</b>
                <p><code>(1 == 1)</code> 为真。查询通过！整个过程没有使用 `if` 或 `&&`，执行路径固定，速度极快。</p>
            </li>
        </ol>

    </div>

    <script>
        function createGrid(containerId, highlightBit, highlightClass) {
            const container = document.getElementById(containerId);
            for (let i = 0; i < 64; i++) {
                const bit = document.createElement('div');
                bit.classList.add('bit');
                if (i === highlightBit) {
                    bit.classList.add(highlightClass);
                }
                const inner = document.createElement('div');
                inner.classList.add('bit-inner');
                inner.textContent = i;
                bit.appendChild(inner);
                container.appendChild(bit);
            }
        }

        // Highlight words
        document.getElementById('word-2').classList.add('highlight-strong');
        document.getElementById('word-157').classList.add('highlight-strong-dest');

        // Create grids
        createGrid('origin-grid', 2, 'highlight-bit');
        createGrid('dest-grid', 17, 'highlight-bit-dest');
    </script>

</body>
</html>