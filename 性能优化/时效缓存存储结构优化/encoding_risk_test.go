package main

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/go-redis/redis/v8"
)

func TestEncodingRisk(t *testing.T) {
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   0,
	})
	defer rdb.Close()

	ctx := context.Background()
	rdb.FlushAll(ctx)

	fmt.Println("Redis编码切换风险测试")
	fmt.Println(strings.Repeat("=", 50))

	// 测试不同field数量的编码和内存使用
	testCases := []struct {
		name        string
		fieldCount  int
		description string
	}{
		{"小HashMap", 6, "店铺6个时间窗口"},
		{"中HashMap", 60, "店铺10天数据"},
		{"大HashMap", 600, "店铺100天数据"},
		{"超大HashMap", 1200, "地区聚合数据"},
	}

	for _, tc := range testCases {
		fmt.Printf("\n📊 测试: %s (%s)\n", tc.name, tc.description)
		fmt.Println(strings.Repeat("-", 40))

		rdb.FlushAll(ctx)
		baseMemory := getRedisMemory(ctx, rdb)

		key := fmt.Sprintf("test:%s", tc.name)

		// 添加指定数量的field
		fields := make(map[string]interface{})
		for i := 0; i < tc.fieldCount; i++ {
			fields[fmt.Sprintf("field_%d", i)] = fmt.Sprintf("value_%d", i)
		}

		rdb.HMSet(ctx, key, fields)

		// 检查编码类型
		encoding, _ := rdb.ObjectEncoding(ctx, key).Result()
		afterMemory := getRedisMemory(ctx, rdb)
		usedMemory := afterMemory - baseMemory

		fmt.Printf("Field数量: %d\n", tc.fieldCount)
		fmt.Printf("编码类型: %s\n", encoding)
		fmt.Printf("内存使用: %d 字节\n", usedMemory)
		fmt.Printf("每Field内存: %.1f 字节\n", float64(usedMemory)/float64(tc.fieldCount))

		// 风险评估
		if encoding == "hashtable" {
			fmt.Printf("⚠️  风险: 已切换到hashtable编码，内存开销增大！\n")
		} else {
			fmt.Printf("✅ 安全: 仍使用listpack编码，内存高效\n")
		}
	}

	// 测试临界点
	fmt.Printf("\n🔍 寻找编码切换临界点:\n")
	fmt.Println(strings.Repeat("-", 30))

	rdb.FlushAll(ctx)

	for fieldCount := 500; fieldCount <= 520; fieldCount += 2 {
		rdb.Del(ctx, "threshold_test")

		fields := make(map[string]interface{})
		for i := 0; i < fieldCount; i++ {
			fields[fmt.Sprintf("f%d", i)] = "val"
		}

		rdb.HMSet(ctx, "threshold_test", fields)
		encoding, _ := rdb.ObjectEncoding(ctx, "threshold_test").Result()

		fmt.Printf("Field数量: %d → 编码: %s", fieldCount, encoding)
		if encoding == "hashtable" {
			fmt.Printf(" ⚠️  临界点发现！")
		}
		fmt.Printf("\n")
	}
}

func getRedisMemory(ctx context.Context, rdb *redis.Client) int64 {
	info, err := rdb.Info(ctx, "memory").Result()
	if err != nil {
		return 0
	}

	lines := strings.Split(info, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "used_memory:") {
			memStr := strings.TrimPrefix(line, "used_memory:")
			memStr = strings.TrimSpace(memStr)
			var mem int64
			fmt.Sscanf(memStr, "%d", &mem)
			return mem
		}
	}
	return 0
}
