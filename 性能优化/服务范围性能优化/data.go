package main

import (
	"math/rand"
	"time"
)

const (
	Num3PLs       = 10
	NumLocations  = 10000
	OriginDensity = 0.9 // 90% of origins are serviceable
	DestDensity   = 0.9 // 90% of destinations are serviceable for a given origin
)

// TplCoverage holds the decoupled sets of serviceable origins and destinations for a single TPL.
// This reflects the new understanding of the "real world" data model.
type TplCoverage struct {
	Origins []uint32
	Dests   []uint32
}

// generateDecoupledData is the single source of truth for data generation.
// It creates a map of TPL IDs to their respective coverage sets.
func generateDecoupledData() map[uint32]TplCoverage {
	rand.Seed(time.Now().UnixNano())

	data := make(map[uint32]TplCoverage, Num3PLs)

	numServiceableOrigins := int(float64(NumLocations) * OriginDensity)
	numServiceableDests := int(float64(NumLocations) * DestDensity)

	// Create a template of all possible locations to sample from
	allLocationIDs := make([]uint32, NumLocations)
	for i := 0; i < NumLocations; i++ {
		allLocationIDs[i] = uint32(i)
	}

	for tplID := uint32(0); tplID < Num3PLs; tplID++ {
		// Randomly select 90% of origins for this 3PL
		rand.Shuffle(len(allLocationIDs), func(i, j int) {
			allLocationIDs[i], allLocationIDs[j] = allLocationIDs[j], allLocationIDs[i]
		})
		serviceableOrigins := make([]uint32, numServiceableOrigins)
		copy(serviceableOrigins, allLocationIDs[:numServiceableOrigins])

		// Randomly select 90% of destinations for this 3PL
		rand.Shuffle(len(allLocationIDs), func(i, j int) {
			allLocationIDs[i], allLocationIDs[j] = allLocationIDs[j], allLocationIDs[i]
		})
		serviceableDests := make([]uint32, numServiceableDests)
		copy(serviceableDests, allLocationIDs[:numServiceableDests])

		data[tplID] = TplCoverage{
			Origins: serviceableOrigins,
			Dests:   serviceableDests,
		}
	}

	return data
}

// generateQueries remains the same, producing random queries for testing.
func generateQueries(num int) []ServicePair {
	rand.Seed(time.Now().UnixNano())
	queries := make([]ServicePair, num)
	for i := 0; i < num; i++ {
		queries[i] = ServicePair{
			TplID:    rand.Uint32() % Num3PLs,
			OriginID: rand.Uint32() % NumLocations,
			DestID:   rand.Uint32() % NumLocations,
		}
	}
	return queries
}

// ServicePair is now only used for generating queries.
type ServicePair struct {
	TplID    uint32
	OriginID uint32
	DestID   uint32
}
