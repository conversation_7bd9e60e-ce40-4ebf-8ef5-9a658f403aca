package main

import (
	pb "benchmark/pb"
	flatbuffers_generated "benchmark/pb/Flat"
	"bytes"
	"encoding/gob"
	"encoding/json"
	"testing"

	flatbuffers "github.com/google/flatbuffers/go"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/proto"
)

// JSON序列化基准测试
func BenchmarkJSONMarshal(b *testing.B) {
	person := createTestData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := json.Marshal(person)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// JSON反序列化基准测试
func BenchmarkJSONUnmarshal(b *testing.B) {
	person := createTestData()
	data, _ := json.Marshal(person)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var p Person
		err := json.Unmarshal(data, &p)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// GOB序列化基准测试
func BenchmarkGOBMarshal(b *testing.B) {
	person := createTestData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		enc := gob.NewEncoder(&buf)
		err := enc.Encode(person)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// GOB反序列化基准测试
func BenchmarkGOBUnmarshal(b *testing.B) {
	person := createTestData()
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	_ = enc.Encode(person)
	data := buf.Bytes()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var p Person
		buf := bytes.NewBuffer(data)
		dec := gob.NewDecoder(buf)
		err := dec.Decode(&p)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Protocol Buffer序列化基准测试
func BenchmarkProtobufMarshal(b *testing.B) {
	person := createProtobufTestData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := proto.Marshal(person)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Protocol Buffer反序列化基准测试
func BenchmarkProtobufUnmarshal(b *testing.B) {
	person := createProtobufTestData()
	data, _ := proto.Marshal(person)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		p := &pb.Person{}
		err := proto.Unmarshal(data, p)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BSON序列化基准测试
func BenchmarkBSONMarshal(b *testing.B) {
	person := createTestData()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := bson.Marshal(person)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BSON反序列化基准测试
func BenchmarkBSONUnmarshal(b *testing.B) {
	person := createTestData()
	data, _ := bson.Marshal(person)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var p Person
		err := bson.Unmarshal(data, &p)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// FlatBuffers序列化基准测试
func BenchmarkFlatbuffersMarshal(b *testing.B) {
	personData := createTestData()
	builder := flatbuffers.NewBuilder(1024) // 可以适当调整初始大小
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		builder.Reset()
		personOffset := createFlatbuffersPerson(builder, personData) // 使用 benchmark.go 中的辅助函数
		builder.Finish(personOffset)
		_ = builder.FinishedBytes() // 获取序列化后的字节
	}
}

// FlatBuffers反序列化基准测试
func BenchmarkFlatbuffersUnmarshal(b *testing.B) {
	personData := createTestData()
	builder := flatbuffers.NewBuilder(1024)
	personOffset := createFlatbuffersPerson(builder, personData)
	builder.Finish(personOffset)
	data := builder.FinishedBytes()

	// var fbPerson flatbuffers_generated.Person // 声明一个用于接收的对象
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// FlatBuffers的反序列化通常是直接从字节获取根对象
		_ = flatbuffers_generated.GetRootAsPerson(data, 0)
		// 如果需要将数据 "解压" 到一个 main.Person 结构，会更复杂且不常用
		// 为了与 unmarshalFlatbuffers 函数的行为保持一致，这里仅获取根对象
	}
}
