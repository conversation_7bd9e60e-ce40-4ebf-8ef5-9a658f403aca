# InvertedIndex 策略优化：从 Roaring Bitmap 到 Go 原生 Map

## 改造背景

由于 BITMAP 存储的数据量相对较少（只有 10 个 TPL），使用 Roaring Bitmap 可能存在过度工程化的问题。因此，我们将 InvertedIndex 策略从 Roaring Bitmap 改造为 Go 原生 Map 实现，以简化代码并可能获得更好的性能。

## 实现对比

### 原始实现 (InvertedIndexRoaring)

<augment_code_snippet path="性能优化/服务范围性能优化/inverted_index_roaring.go" mode="EXCERPT">
````go
type InvertedIndexRoaring struct {
	originToTpls map[uint32]*roaring.Bitmap
	destToTpls   map[uint32]*roaring.Bitmap
}

func (i *InvertedIndexRoaring) IsServiceable(tpl, origin, dest uint32) bool {
	originTpls, ok := i.originToTpls[origin]
	if !ok || !originTpls.Contains(tpl) {
		return false
	}
	// ...
}
````
</augment_code_snippet>

### 优化实现 (InvertedIndexMap)

<augment_code_snippet path="性能优化/服务范围性能优化/inverted_index_map.go" mode="EXCERPT">
````go
type InvertedIndexMap struct {
	originToTpls map[uint32]map[uint32]bool
	destToTpls   map[uint32]map[uint32]bool
}

func (i *InvertedIndexMap) IsServiceable(tpl, origin, dest uint32) bool {
	originTpls, ok := i.originToTpls[origin]
	if !ok || !originTpls[tpl] {
		return false
	}
	// ...
}
````
</augment_code_snippet>

## 性能对比结果

### 构建性能 (Build Performance)

| 实现方式 | 构建时间 | 内存分配 | 分配次数 |
|---------|---------|---------|---------|
| InvertedIndexRoaring | 24.7ms | 4.7MB | 155,193 |
| InvertedIndexMap | 18.9ms | 5.0MB | 55,907 |

**改进效果：**
- 构建时间提升：**23.5%** (24.7ms → 18.9ms)
- 分配次数减少：**64.0%** (155,193 → 55,907)
- 内存使用略增：**5.1%** (4.7MB → 5.0MB)

### 查询性能 (Query Performance)

| 实现方式 | 查询时间 | 内存分配 | 分配次数 |
|---------|---------|---------|---------|
| InvertedIndexRoaring | 155.8 ns/op | 0 B/op | 0 allocs/op |
| InvertedIndexMap | 134.2 ns/op | 0 B/op | 0 allocs/op |

**改进效果：**
- 查询时间提升：**13.9%** (155.8ns → 134.2ns)
- 内存分配：两者都是零分配

### 完整基准测试结果

从完整的基准测试中可以看到：

```
--- Testing: InvertedIndexMap ---
Build Time: 21.858849ms
Memory Used by Structure: 3.32 MB
Query Time (1000000 queries): 134.195267ms
Average Query Time: 134.20 ns/op
```

## 优化分析

### 1. 构建阶段优势

**减少对象创建开销：**
- Roaring Bitmap 需要为每个位置创建复杂的数据结构
- Go 原生 Map 直接使用内置的哈希表实现，创建开销更小

**减少内存分配次数：**
- 从 155,193 次分配减少到 55,907 次，减少了 64%
- 说明 Roaring Bitmap 内部有大量小对象分配

### 2. 查询阶段优势

**更直接的数据访问：**
- Map 查找是 O(1) 的哈希表查找
- Roaring Bitmap 的 Contains 操作虽然也很快，但涉及更复杂的内部逻辑

**更好的缓存局部性：**
- Go Map 的内存布局对小规模数据更友好
- 减少了间接访问和指针跳转

### 3. 适用场景分析

**原生 Map 更适合的场景：**
- 数据规模较小（如本例中的 10 个 TPL）
- 需要简单直接的实现
- 对构建性能有要求

**Roaring Bitmap 更适合的场景：**
- 大规模稀疏数据
- 需要位运算操作
- 内存使用是主要考虑因素

## 结论

对于当前的业务场景（10 个 TPL，数据量相对较小），使用 Go 原生 Map 替代 Roaring Bitmap 是一个明智的选择：

1. **性能提升显著**：构建时间提升 23.5%，查询时间提升 13.9%
2. **代码更简洁**：去除了第三方依赖，代码更易理解和维护
3. **内存分配更高效**：大幅减少了内存分配次数
4. **适合业务规模**：对于小规模数据，原生 Map 的性能优于复杂的位图结构

这次优化验证了"选择合适的数据结构比选择复杂的数据结构更重要"的原则。在数据规模不大的情况下，简单的解决方案往往能带来更好的性能和可维护性。
