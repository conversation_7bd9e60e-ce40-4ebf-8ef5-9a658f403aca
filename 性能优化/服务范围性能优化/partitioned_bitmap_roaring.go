package main

import "github.com/RoaringBitmap/roaring"

const (
	// Defines the offset to separate origin and destination IDs in the same bitmap.
	partitionOffset = uint32(NumLocations)
)

// PartitionedBitmapRoaring uses a single bitmap per TPL, with the index space
// partitioned between origins and destinations.
type PartitionedBitmapRoaring struct {
	// The key is the TPL ID, the value is the single combined bitmap.
	data map[uint32]*roaring.Bitmap
}

// NewPartitionedBitmapRoaring builds the structure from the decoupled data source.
func NewPartitionedBitmapRoaring(data map[uint32]TplCoverage) *PartitionedBitmapRoaring {
	p := &PartitionedBitmapRoaring{
		data: make(map[uint32]*roaring.Bitmap),
	}

	// A temporary slice to hold the offset destination IDs for bulk addition.
	offsetDests := make([]uint32, 0, NumLocations)

	for tplID, coverage := range data {
		combinedBitmap := roaring.New()

		// Add all origin IDs directly
		combinedBitmap.AddMany(coverage.Origins)

		// Calculate offset destinations
		offsetDests = offsetDests[:0] // Reset slice without reallocating
		for _, destID := range coverage.Dests {
			offsetDests = append(offsetDests, destID+partitionOffset)
		}

		// Add all offset destination IDs
		combinedBitmap.AddMany(offsetDests)

		p.data[tplID] = combinedBitmap
	}

	return p
}

// IsServiceable checks for serviceability using the partitioned bitmap.
// It performs two 'Contains' checks on the same bitmap object.
func (p *PartitionedBitmapRoaring) IsServiceable(tpl, origin, dest uint32) bool {
	if combinedBitmap, ok := p.data[tpl]; ok {
		return combinedBitmap.Contains(origin) && combinedBitmap.Contains(dest+partitionOffset)
	}
	return false
}
