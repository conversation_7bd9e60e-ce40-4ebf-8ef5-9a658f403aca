package main

import (
	"math/rand"
	"time"
)

const (
	Num3PLs       = 10
	NumLocations  = 10000
	OriginDensity = 0.9 // 90% of origins are serviceable
	DestDensity   = 0.9 // 90% of destinations are serviceable for a given origin

	// Item Card page constants
	NumItemsPerPage = 25 // Number of items shown on one Item Card page
	Num3PLsPerItem  = 5  // Number of 3PLs available for each item
)

// TplCoverage holds the decoupled sets of serviceable origins and destinations for a single TPL.
// This reflects the new understanding of the "real world" data model.
type TplCoverage struct {
	Origins []uint32
	Dests   []uint32
}

// generateDecoupledData is the single source of truth for data generation.
// It creates a map of TPL IDs to their respective coverage sets.
func generateDecoupledData() map[uint32]TplCoverage {
	rand.Seed(time.Now().UnixNano())

	data := make(map[uint32]TplCoverage, Num3PLs)

	numServiceableOrigins := int(float64(NumLocations) * OriginDensity)
	numServiceableDests := int(float64(NumLocations) * DestDensity)

	// Create a template of all possible locations to sample from
	allLocationIDs := make([]uint32, NumLocations)
	for i := 0; i < NumLocations; i++ {
		allLocationIDs[i] = uint32(i)
	}

	for tplID := uint32(0); tplID < Num3PLs; tplID++ {
		// Randomly select 90% of origins for this 3PL
		rand.Shuffle(len(allLocationIDs), func(i, j int) {
			allLocationIDs[i], allLocationIDs[j] = allLocationIDs[j], allLocationIDs[i]
		})
		serviceableOrigins := make([]uint32, numServiceableOrigins)
		copy(serviceableOrigins, allLocationIDs[:numServiceableOrigins])

		// Randomly select 90% of destinations for this 3PL
		rand.Shuffle(len(allLocationIDs), func(i, j int) {
			allLocationIDs[i], allLocationIDs[j] = allLocationIDs[j], allLocationIDs[i]
		})
		serviceableDests := make([]uint32, numServiceableDests)
		copy(serviceableDests, allLocationIDs[:numServiceableDests])

		data[tplID] = TplCoverage{
			Origins: serviceableOrigins,
			Dests:   serviceableDests,
		}
	}

	return data
}

// ItemRequest represents a single item in the batch request
type ItemRequest struct {
	ItemID   uint32 // Item identifier
	SellerID uint32 // Origin (Seller location)
	TplIDs   []uint32 // Available 3PLs for this item
}

// BatchServiceabilityRequest represents a batch request from Item Card page
// One Buyer (DestID) wants to check serviceability for multiple items
type BatchServiceabilityRequest struct {
	BuyerID uint32        // Destination (Buyer location) - same for all items
	Items   []ItemRequest // List of items to check
}

// ServicePair is used for individual queries (kept for backward compatibility)
type ServicePair struct {
	TplID    uint32
	OriginID uint32
	DestID   uint32
}

// generateBatchRequests generates realistic batch requests simulating Item Card page scenarios
func generateBatchRequests(numRequests int) []BatchServiceabilityRequest {
	rand.Seed(time.Now().UnixNano())
	requests := make([]BatchServiceabilityRequest, numRequests)

	for i := 0; i < numRequests; i++ {
		// Each request represents one Buyer viewing an Item Card page
		buyerID := rand.Uint32() % NumLocations

		// Generate items for this page
		items := make([]ItemRequest, NumItemsPerPage)
		for j := 0; j < NumItemsPerPage; j++ {
			// Each item has a different seller
			sellerID := rand.Uint32() % NumLocations

			// Randomly select 3PLs for this item (simulate real business logic)
			availableTpls := make([]uint32, Num3PLsPerItem)
			selectedTpls := rand.Perm(int(Num3PLs))[:Num3PLsPerItem]
			for k, tplIdx := range selectedTpls {
				availableTpls[k] = uint32(tplIdx)
			}

			items[j] = ItemRequest{
				ItemID:   uint32(i*NumItemsPerPage + j), // Unique item ID
				SellerID: sellerID,
				TplIDs:   availableTpls,
			}
		}

		requests[i] = BatchServiceabilityRequest{
			BuyerID: buyerID,
			Items:   items,
		}
	}

	return requests
}

// generateQueries generates individual queries for backward compatibility
func generateQueries(num int) []ServicePair {
	rand.Seed(time.Now().UnixNano())
	queries := make([]ServicePair, num)
	for i := 0; i < num; i++ {
		queries[i] = ServicePair{
			TplID:    rand.Uint32() % Num3PLs,
			OriginID: rand.Uint32() % NumLocations,
			DestID:   rand.Uint32() % NumLocations,
		}
	}
	return queries
}
