import os, json, pandas as pd, matplotlib.pyplot as plt, numpy as np
from pathlib import Path

def draw_perf():
    # 读取 JSON Lines
    records = [json.loads(l) for l in Path('bench_clean.json').open()]

    df = pd.DataFrame(records)

    # 从 Test 字段解析 Cache 与 场景（读写比例）
    # 形如 BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000-12
    df[['Cache','Scenario','Concurrency']] = df['Test'] \
        .str.extract(r'BenchmarkReadWritePerformance/(.*?)_(\d+%_Read_\d+%_Write|100%_Read|100%_Write)_(\d+)-')

    # 计算每个 Cache 在每个场景的平均 NsPerOp
    pivot = df.groupby(['Scenario','Cache'])['NsPerOp'].mean().unstack()

    # 场景排序（自定义）
    scenario_order = ['100%_Read',
                      '75%_Read_25%_Write',
                      '50%_Read_50%_Write',
                      '25%_Read_75%_Write',
                      '100%_Write']
    pivot = pivot.reindex(scenario_order)

    # 去除全 NaN 列（某些缓存库在特定场景可能无数据）
    pivot = pivot.dropna(axis=1, how='all')

    # 纵轴单位转换：ns/op -> ops/s
    pivot = 1e9 / pivot

    # --- 新增：双子图 ---
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 6))

    # 第一张：原始 ops/s 折线图
    pivot.plot(marker='o', ax=ax1)
    ax1.set_ylabel('avg ops/s (higher is better)')
    ax1.set_xlabel('Read / Write ratio scenario')
    ax1.set_title('BenchmarkReadWritePerformance (5-run average)')
    ax1.grid(True, linestyle='--', alpha=0.3)
    ax1.tick_params(axis='x', labelsize=10)

    # 第二张：以 GoLRU 为基准线的相对提升柱状图（y=0为基线）
    if 'GoLRU' in pivot.columns:
        baseline = pivot['GoLRU']
        relative = pivot.divide(baseline, axis=0) - 1
        relative.plot(kind='bar', ax=ax2)
        ax2.set_ylabel('Relative improvement over GoLRU')
        ax2.set_xlabel('Read / Write ratio scenario')
        ax2.set_title('Relative Performance (GoLRU = 0)')
        ax2.axhline(0, color='gray', linestyle='--', linewidth=1)
        ax2.grid(True, linestyle='--', alpha=0.3, axis='y')
        ax2.tick_params(axis='x', labelsize=10)
    else:
        ax2.text(0.5, 0.5, 'GoLRU baseline not found', ha='center', va='center', fontsize=16)
        ax2.axis('off')

    plt.tight_layout()
    plt.savefig('rw_perf_line_with_baseline.png', dpi=150)
    print("双图已保存为 rw_perf_line_with_baseline.png")

def draw_memory_bar(capacities=None):
    import os, json
    import pandas as pd
    import matplotlib.pyplot as plt
    import numpy as np

    if not os.path.exists('memory_bench_clean.json'):
        print('memory_bench_clean.json not found, skip memory usage plot.')
        return
    
    records = []
    with open('memory_bench_clean.json') as f:
        for l in f:
            l = l.strip()
            if not l:
                continue
            try:
                records.append(json.loads(l))
            except Exception as e:
                print(f"Warning: skip line due to JSON error: {e}\nLine: {l}")
    
    if not records:
        print('No valid records found in memory_bench_clean.json, skip plot.')
        return
    
    df = pd.DataFrame(records)
    df['capacity'] = df['capacity'].astype(int)
    df['per_entry_b'] = df['per_entry_b'].astype(float)
    
    # 获取所有可用容量，如果没有指定则使用全部
    available_capacities = sorted(df['capacity'].unique())
    if capacities is None:
        capacities = available_capacities
    else:
        # 过滤只保留指定的容量
        capacities = [cap for cap in capacities if cap in available_capacities]
    
    if not capacities:
        print('No valid capacities found, skip plot.')
        return
    
    # 过滤数据
    df = df[df['capacity'].isin(capacities)]
    if df.empty:
        print('No data for specified capacities, skip plot.')
        return
    
    # 处理重复数据：对同一缓存库和容量的组合取平均值
    df_agg = df.groupby(['name', 'capacity'])['per_entry_b'].mean().reset_index()
    
    # 透视表：行为缓存库，列为容量
    pivot_df = df_agg.pivot(index='name', columns='capacity', values='per_entry_b')
    
    # 获取缓存库名称（按平均内存使用量排序）
    cache_names = pivot_df.mean(axis=1).sort_values().index.tolist()
    pivot_df = pivot_df.reindex(cache_names)
    
    # 设置绘图参数
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # 设置柱子宽度和位置
    n_caches = len(cache_names)
    n_capacities = len(capacities)
    width = 0.8 / n_capacities  # 总宽度的80%分给所有容量
    x = np.arange(n_caches)
    
    # 检查是否有SyncMap数据
    has_syncmap = 'SyncMap' in cache_names
    syncmap_data = None
    if has_syncmap:
        syncmap_data = pivot_df.loc['SyncMap']
    
    # 绘制分组柱状图
    for i, capacity in enumerate(capacities):
        if capacity in pivot_df.columns:
            values = pivot_df[capacity].values
            # 处理NaN值
            values = np.where(np.isnan(values), 0, values)
            
            # 根据与SyncMap的比较设置柱子颜色
            bar_colors = []
            if has_syncmap and capacity in syncmap_data.index and not np.isnan(syncmap_data[capacity]):
                syncmap_value = syncmap_data[capacity]
                for j, cache in enumerate(cache_names):
                    cache_value = values[j]
                    if cache == 'SyncMap':
                        bar_colors.append('#4682B4')  # 蓝色：SyncMap基准
                    elif cache_value == 0:
                        bar_colors.append('lightgray')  # 灰色：无数据
                    elif cache_value < syncmap_value:
                        bar_colors.append('#2E8B57')  # 绿色：更节省内存
                    else:
                        bar_colors.append('#DC143C')  # 红色：更耗内存
            else:
                # 如果没有SyncMap数据，使用原来的颜色方案
                base_color = plt.cm.Set3(i / max(1, n_capacities - 1))
                bar_colors = [base_color] * len(values)
            
            bars = ax.bar(x + i * width - width * (n_capacities - 1) / 2, 
                         values, width, 
                         label=f'Capacity: {capacity:,}',
                         color=bar_colors,
                         alpha=0.8,
                         edgecolor='black',
                         linewidth=0.5)
            
            # 在柱子上添加数值标签（只在值不为0时显示）
            for j, bar in enumerate(bars):
                height = bar.get_height()
                if height > 0:
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.0f}',
                           ha='center', va='bottom', fontsize=8, rotation=45)
    
    # 设置图形属性
    ax.set_xlabel('Cache Library', fontsize=12)
    ax.set_ylabel('Bytes Per Entry (B/entry)', fontsize=12)
    ax.set_title('Cache Memory Usage per Entry - Multi-Capacity Comparison', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(cache_names, rotation=45, ha='right')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.grid(True, alpha=0.3, axis='y')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    filename = f'memory_usage_multi_capacity.png'
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f'多容量内存占用对比图已保存为 {filename}')
    print(f'包含容量: {[f"{cap:,}" for cap in capacities]}')
    
    # 额外生成一个数据摘要
    print("\n=== 内存使用摘要 ===")
    for cache in cache_names:
        print(f"{cache:12}: ", end="")
        for cap in capacities:
            if cap in pivot_df.columns and not np.isnan(pivot_df.loc[cache, cap]):
                cache_value = pivot_df.loc[cache, cap]
                print(f"{cap:>7,}: {cache_value:>6.0f}B  ", end="")
            else:
                print(f"{cap:>7,}: {'N/A':>6}  ", end="")
        print()
    
def draw_memory_baseline_syncmap(capacities=None):
    """绘制以SyncMap为基准的内存使用对比图"""
    import os, json
    import pandas as pd
    import matplotlib.pyplot as plt
    import numpy as np

    if not os.path.exists('memory_bench_clean.json'):
        print('memory_bench_clean.json not found, skip memory baseline plot.')
        return
    
    records = []
    with open('memory_bench_clean.json') as f:
        for l in f:
            l = l.strip()
            if not l:
                continue
            try:
                records.append(json.loads(l))
            except Exception as e:
                print(f"Warning: skip line due to JSON error: {e}\nLine: {l}")
    
    if not records:
        print('No valid records found in memory_bench_clean.json, skip plot.')
        return
    
    df = pd.DataFrame(records)
    df['capacity'] = df['capacity'].astype(int)
    df['per_entry_b'] = df['per_entry_b'].astype(float)
    
    # 获取所有可用容量，如果没有指定则使用全部
    available_capacities = sorted(df['capacity'].unique())
    if capacities is None:
        capacities = available_capacities
    else:
        # 过滤只保留指定的容量
        capacities = [cap for cap in capacities if cap in available_capacities]
    
    if not capacities:
        print('No valid capacities found, skip plot.')
        return
    
    # 过滤数据
    df = df[df['capacity'].isin(capacities)]
    if df.empty:
        print('No data for specified capacities, skip plot.')
        return
    
    # 检查是否有SyncMap数据
    if 'SyncMap' not in df['name'].values:
        print('SyncMap data not found, cannot create baseline comparison.')
        return
    
    # 处理重复数据：对同一缓存库和容量的组合取平均值
    df_agg = df.groupby(['name', 'capacity'])['per_entry_b'].mean().reset_index()
    
    # 透视表：行为缓存库，列为容量
    pivot_df = df_agg.pivot(index='name', columns='capacity', values='per_entry_b')
    
    # 获取SyncMap的数据作为基准
    if 'SyncMap' not in pivot_df.index:
        print('SyncMap data not found in pivot table, cannot create baseline comparison.')
        return
    
    syncmap_baseline = pivot_df.loc['SyncMap']
    
    # 计算相对于SyncMap的百分比
    relative_df = pivot_df.copy()
    for capacity in capacities:
        if capacity in syncmap_baseline.index and not np.isnan(syncmap_baseline[capacity]):
            baseline_value = syncmap_baseline[capacity]
            if baseline_value > 0:
                relative_df[capacity] = (pivot_df[capacity] / baseline_value) * 100
            else:
                relative_df[capacity] = np.nan
        else:
            relative_df[capacity] = np.nan
    
    # 移除SyncMap行（因为它永远是100%）
    if 'SyncMap' in relative_df.index:
        relative_df = relative_df.drop('SyncMap')
    
    # 获取缓存库名称（按平均相对值排序）
    cache_names = relative_df.mean(axis=1).sort_values().index.tolist()
    relative_df = relative_df.reindex(cache_names)
    
    # 设置绘图参数
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # 设置柱子宽度和位置
    n_caches = len(cache_names)
    n_capacities = len(capacities)
    width = 0.8 / n_capacities  # 总宽度的80%分给所有容量
    x = np.arange(n_caches)
    
    # 颜色方案：绿色表示低于基准，红色表示高于基准
    colors = []
    for i in range(n_capacities):
        colors.append(plt.cm.RdYlGn_r(0.3 + 0.4 * i / max(1, n_capacities - 1)))
    
    # 绘制分组柱状图
    for i, capacity in enumerate(capacities):
        if capacity in relative_df.columns:
            values = relative_df[capacity].values
            # 处理NaN值
            values = np.where(np.isnan(values), 0, values)
            
            # 根据值的大小选择颜色
            bar_colors = []
            for val in values:
                if val == 0:
                    bar_colors.append('lightgray')
                elif val < 100:
                    bar_colors.append('#2E8B57')  # 绿色：节省内存
                else:
                    bar_colors.append('#DC143C')  # 红色：消耗更多内存
            
            bars = ax.bar(x + i * width - width * (n_capacities - 1) / 2, 
                         values, width, 
                         label=f'Capacity: {capacity:,}',
                         color=bar_colors,
                         alpha=0.8,
                         edgecolor='black',
                         linewidth=0.5)
            
            # 在柱子上添加数值标签（只在值不为0时显示）
            for j, bar in enumerate(bars):
                height = bar.get_height()
                if height > 0:
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.0f}%',
                           ha='center', va='bottom', fontsize=8, rotation=45)
    
    # 添加100%基准线
    ax.axhline(y=100, color='black', linestyle='--', linewidth=2, alpha=0.7, label='SyncMap Baseline (100%)')
    
    # 设置图形属性
    ax.set_xlabel('Cache Library', fontsize=12)
    ax.set_ylabel('Memory Usage Relative to SyncMap (%)', fontsize=12)
    ax.set_title('Cache Memory Usage Relative to SyncMap Baseline', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(cache_names, rotation=45, ha='right')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.grid(True, alpha=0.3, axis='y')
    
    # 设置Y轴范围，让100%基准线更明显
    y_max = max(100, np.nanmax(relative_df.values) * 1.1) if not relative_df.empty else 200
    ax.set_ylim(0, y_max)
    
    # 在图上添加颜色说明
    ax.text(0.02, 0.98, 'Green: More efficient than SyncMap\nRed: Less efficient than SyncMap', 
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    filename = f'memory_usage_syncmap_baseline.png'
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f'以SyncMap为基准的内存占用对比图已保存为 {filename}')
    print(f'包含容量: {[f"{cap:,}" for cap in capacities]}')
    
    # 额外生成一个数据摘要
    print("\n=== 相对于SyncMap的内存使用摘要 ===")
    print("SyncMap     : ", end="")
    for cap in capacities:
        print(f"{cap:>7,}: {'100%':>6}  ", end="")
    print()
    
    for cache in cache_names:
        print(f"{cache:12}: ", end="")
        for cap in capacities:
            if cap in relative_df.columns and not np.isnan(relative_df.loc[cache, cap]):
                val = relative_df.loc[cache, cap]
                print(f"{cap:>7,}: {val:>5.0f}% ", end="")
            else:
                print(f"{cap:>7,}: {'N/A':>6}  ", end="")
        print()

def draw_cpu_efficiency():
    import os, json, pandas as pd, matplotlib.pyplot as plt, numpy as np
    if not os.path.exists('cpu_bench_clean.json'):
        print('cpu_bench_clean.json not found, skip cpu efficiency plot.')
        return
    with open('cpu_bench_clean.json', encoding='utf-8') as f:
        records = [json.loads(line) for line in f if line.strip()]
    df = pd.DataFrame(records)
    df['concurrency'] = df['concurrency'].astype(int)
    df['efficiency'] = df['qps'] / df['cpu_avg']
    
    # 固定横坐标点，转为字符串，作为分类变量
    x_ticks = [1, 2, 4, 8, 16, 32, 64, 128]
    x_labels = [str(x) for x in x_ticks]
    
    # --- 新增：双子图 ---
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 7))
    
    # 第一张：原始CPU效率曲线图
    for lib, group in df.groupby('lib'):
        group = group.drop_duplicates(subset='concurrency')
        # 以分类变量顺序对齐y值
        y = [group[group['concurrency'] == x]['efficiency'].values[0] if x in group['concurrency'].values else None for x in x_ticks]
        ax1.plot(x_labels, y, marker='o', label=lib, linewidth=2)
    
    ax1.set_xlabel('Concurrency', fontsize=12)
    ax1.set_ylabel('QPS per 1% CPU (QPS/CPU%)', fontsize=12)
    ax1.set_title('CPU Efficiency vs Concurrency', fontsize=14, fontweight='bold')
    ax1.set_xticks(x_labels)  # 强制分类型横坐标
    ax1.legend()
    ax1.grid(True, linestyle='--', alpha=0.3)
    
    # 第二张：以 golang-lru 为基准线的相对性能柱状图
    golru_libs = ['golang-lru', 'golru', 'go-lru', 'GoLRU']  # 可能的名称变体
    baseline_lib = None
    
    # 查找基准库
    for lib_name in golru_libs:
        if lib_name in df['lib'].values:
            baseline_lib = lib_name
            break
    
    if baseline_lib:
        # 创建透视表，便于计算相对性能
        pivot_df = df.pivot_table(index='lib', columns='concurrency', values='efficiency', aggfunc='mean')
        
        # 获取基准库的数据
        baseline_data = pivot_df.loc[baseline_lib]
        
        # 计算相对性能提升（以百分比表示）
        relative_df = pivot_df.copy()
        for concurrency in pivot_df.columns:
            if not pd.isna(baseline_data[concurrency]) and baseline_data[concurrency] > 0:
                relative_df[concurrency] = (pivot_df[concurrency] / baseline_data[concurrency] - 1) * 100
            else:
                relative_df[concurrency] = np.nan
        
        # 移除基准库行（因为它永远是0%）
        if baseline_lib in relative_df.index:
            relative_df = relative_df.drop(baseline_lib)
        
        # 只保留有数据的并发度
        available_concurrencies = [x for x in x_ticks if x in relative_df.columns]
        relative_df = relative_df[available_concurrencies]
        
        # 绘制分组柱状图
        if not relative_df.empty and len(available_concurrencies) > 0:
            # 获取缓存库名称（按平均相对性能排序）
            cache_names = relative_df.mean(axis=1).sort_values(ascending=False).index.tolist()
            relative_df = relative_df.reindex(cache_names)
            
            # 设置柱子宽度和位置
            n_caches = len(cache_names)
            n_concurrencies = len(available_concurrencies)
            width = 0.8 / n_concurrencies
            x = np.arange(n_caches)
            
            # 颜色方案
            colors = plt.cm.Set3(np.linspace(0, 1, n_concurrencies))
            
            # 绘制分组柱状图
            for i, concurrency in enumerate(available_concurrencies):
                values = relative_df[concurrency].values
                # 处理NaN值
                values = np.where(np.isnan(values), 0, values)
                
                # 根据值的大小选择颜色
                bar_colors = []
                for val in values:
                    if val == 0:
                        bar_colors.append('lightgray')
                    elif val > 0:
                        bar_colors.append('#2E8B57')  # 绿色：性能更好
                    else:
                        bar_colors.append('#DC143C')  # 红色：性能更差
                
                bars = ax2.bar(x + i * width - width * (n_concurrencies - 1) / 2, 
                              values, width, 
                              label=f'Concurrency: {concurrency}',
                              color=bar_colors,
                              alpha=0.8,
                              edgecolor='black',
                              linewidth=0.5)
                
                # 在柱子上添加数值标签
                for j, bar in enumerate(bars):
                    height = bar.get_height()
                    if abs(height) > 1:  # 只显示大于1%的差异
                        ax2.text(bar.get_x() + bar.get_width()/2., height,
                               f'{height:+.0f}%',
                               ha='center', va='bottom' if height > 0 else 'top', 
                               fontsize=8, rotation=0)
            
            # 添加0%基准线
            ax2.axhline(y=0, color='black', linestyle='--', linewidth=2, alpha=0.7, 
                       label=f'{baseline_lib} Baseline (0%)')
            
            # 设置图形属性
            ax2.set_xlabel('Cache Library', fontsize=12)
            ax2.set_ylabel('CPU Efficiency Relative to golang-lru (%)', fontsize=12)
            ax2.set_title(f'CPU Efficiency Relative to {baseline_lib}', fontsize=14, fontweight='bold')
            ax2.set_xticks(x)
            ax2.set_xticklabels(cache_names, rotation=45, ha='right')
            ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax2.grid(True, alpha=0.3, axis='y')
            
            # 在图上添加颜色说明
            ax2.text(0.02, 0.98, 'Green: More efficient than golang-lru\nRed: Less efficient than golang-lru', 
                    transform=ax2.transAxes, fontsize=10, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        else:
            ax2.text(0.5, 0.5, f'No sufficient data for {baseline_lib} comparison', 
                    ha='center', va='center', fontsize=16)
            ax2.axis('off')
    else:
        ax2.text(0.5, 0.5, 'golang-lru baseline not found\n(Possible names: golang-lru, golru, go-lru, GoLRU)', 
                ha='center', va='center', fontsize=16)
        ax2.axis('off')
    
    plt.tight_layout()
    plt.savefig('cpu_efficiency_curve.png', dpi=150, bbox_inches='tight')
    plt.close()
    print("CPU效率双图已保存为 cpu_efficiency_curve.png")

def draw_gc():
    import os, json
    import pandas as pd
    import matplotlib.pyplot as plt
    import numpy as np

    if not os.path.exists('gc_bench_clean.json'):
        print('gc_bench_clean.json not found, skip GC plot.')
        return
    records = []
    with open('gc_bench_clean.json') as f:
        for l in f:
            l = l.strip()
            if not l:
                continue
            try:
                records.append(json.loads(l))
            except Exception as e:
                print(f"Warning: skip line due to JSON error: {e}\nLine: {l}")
    if not records:
        print('No valid records found in gc_bench_clean.json, skip plot.')
        return
    
    df = pd.DataFrame(records)
    df['gc_pause_ms'] = df['gc_pause_ms'].astype(float)
    
    # 计算每个库的平均GC暂停时间
    avg_gc = df.groupby('lib')['gc_pause_ms'].mean().sort_values()
    
    # 检查是否有SyncMap数据
    has_syncmap = 'SyncMap' in avg_gc.index
    syncmap_value = None
    if has_syncmap:
        syncmap_value = avg_gc['SyncMap']
    
    # 根据与SyncMap的比较设置颜色
    colors = []
    if has_syncmap:
        for lib, value in avg_gc.items():
            if lib == 'SyncMap':
                colors.append('#4682B4')  # 蓝色：SyncMap基准
            elif value < syncmap_value:
                colors.append('#2E8B57')  # 绿色：更少的GC暂停时间（更好）
            else:
                colors.append('#DC143C')  # 红色：更多的GC暂停时间（更差）
    else:
        # 如果没有SyncMap数据，使用渐变颜色
        colors = plt.cm.RdYlGn_r(np.linspace(0.2, 0.8, len(avg_gc)))
    
    # 绘制水平柱状图
    fig, ax = plt.subplots(figsize=(12, 8))
    bars = ax.barh(range(len(avg_gc)), avg_gc.values, 
                   color=colors, alpha=0.8, 
                   edgecolor='black', linewidth=0.5)
    
    # 设置y轴标签
    ax.set_yticks(range(len(avg_gc)))
    ax.set_yticklabels(avg_gc.index)
    
    # 添加数值标签
    for i, (lib, value) in enumerate(avg_gc.items()):
        ax.text(value + max(avg_gc.values) * 0.01, i, f'{value:.2f}ms',
                va='center', ha='left', fontsize=10)
    
    # 设置图形属性
    ax.set_xlabel('Average GC Pause (ms, lower is better)', fontsize=12)
    ax.set_ylabel('Cache Library', fontsize=12)
    ax.set_title('Cache GC Pause Time Comparison', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3, axis='x')
   
    plt.tight_layout()
    plt.savefig('gc_pause_bar.png', dpi=150, bbox_inches='tight')
    plt.close()
    print("GC 暂停时间柱状图已保存为 gc_pause_bar.png")

def draw_hitrate_simulator(json_file):
    """
    绘制新的simulator命中率测试结果
    处理格式: [{"lib": "bigcache", "capacity": 500, "hit_rate": 54.38}, ...]
    """
    import os
    import json
    import pandas as pd
    import matplotlib.pyplot as plt
    import numpy as np
    
    if not os.path.exists(json_file):
        print(f'{json_file} not found, skip hitrate simulator plot.')
        return
        
    with open(json_file, encoding='utf-8') as f:
        records = json.load(f)
    
    if not records:
        print(f'No data found in {json_file}')
        return
        
    df = pd.DataFrame(records)
    
    # 数据验证和清理
    if 'capacity' not in df.columns or 'hit_rate' not in df.columns or 'lib' not in df.columns:
        print(f'Invalid data format in {json_file}, missing required columns')
        return
        
    df = df[df['capacity'].notnull() & df['hit_rate'].notnull()]
    df['capacity'] = df['capacity'].astype(int)
    df['hit_rate'] = df['hit_rate'].astype(float)
    
    # 按库名和容量排序
    df = df.sort_values(['lib', 'capacity'])
    
    # 创建图表
    plt.figure(figsize=(14, 8))
    
    # 定义颜色、标记和线型样式
    colors = plt.cm.Set1(np.linspace(0, 1, len(df['lib'].unique())))
    markers = ['o', 's', '^', 'D', 'v', 'h', 'p', '*']
    linestyles = ['-', '--', '-.', ':', '-', '--', '-.', ':']
    
    # 检测重叠数据并分组
    overlap_groups = {}
    for lib, group in df.groupby('lib'):
        # 用命中率作为分组key，检测重叠
        hit_rates_key = tuple(sorted(group['hit_rate'].values))
        if hit_rates_key not in overlap_groups:
            overlap_groups[hit_rates_key] = []
        overlap_groups[hit_rates_key].append((lib, group))
    
    # 为每个缓存库绘制曲线，对重叠的使用不同线型
    for i, (lib, group) in enumerate(df.groupby('lib')):
        # 检查是否为重叠组
        hit_rates_key = tuple(sorted(group['hit_rate'].values))
        is_overlapping = len(overlap_groups[hit_rates_key]) > 1
        
        # 对重叠的组使用不同线型和更粗的线
        linewidth = 3.0 if is_overlapping else 2.5
        linestyle = linestyles[i % len(linestyles)]
        alpha = 0.8 if is_overlapping else 1.0
        
        # 为重叠的线添加小幅度偏移避免完全重叠
        x_offset = 0
        if is_overlapping:
            overlap_index = [item[0] for item in overlap_groups[hit_rates_key]].index(lib)
            x_offset = (overlap_index - len(overlap_groups[hit_rates_key])/2) * 0.02
        
        x_values = group['capacity'] * (1 + x_offset)
        
        plt.plot(x_values, group['hit_rate'], 
                marker=markers[i % len(markers)], 
                color=colors[i], 
                label=lib, 
                linewidth=linewidth,
                linestyle=linestyle,
                alpha=alpha,
                markersize=9,
                markerfacecolor='white',
                markeredgewidth=2.5,
                markeredgecolor=colors[i])
    
    # 图表设置
    plt.xlabel('Cache Capacity', fontsize=14, fontweight='bold')
    plt.ylabel('Hit Rate (%)', fontsize=14, fontweight='bold')
    plt.title('Cache Hit Rate vs Capacity', 
              fontsize=16, fontweight='bold', pad=20)
    
    # 使用对数刻度让数据分布更清晰
    plt.xscale('log')
    
    # 设置y轴范围，从0开始
    plt.ylim(0, max(df['hit_rate']) * 1.1)
    
    # 网格和图例
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=11)
    
    # 检测并报告重叠情况
    overlap_info = []
    for hit_rates_key, libs_groups in overlap_groups.items():
        if len(libs_groups) > 1:
            lib_names = [lib for lib, _ in libs_groups]
            hit_rate_str = f"{libs_groups[0][1]['hit_rate'].iloc[0]:.2f}%"
            overlap_info.append(f"• {', '.join(lib_names)}: {hit_rate_str}")
    
    plt.tight_layout()
    
    # 保存图片
    output_file = 'zipf_hitrate_simulator_comparison.png'
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f'Simulator命中率折线图已保存为 {output_file}')
    
    # 打印数据摘要
    print(f'\n=== 数据摘要 ===')
    print(f'测试库数量: {len(df["lib"].unique())}')
    print(f'容量级别: {sorted(df["capacity"].unique())}')
    print(f'命中率范围: {df["hit_rate"].min():.2f}% - {df["hit_rate"].max():.2f}%')
    
    # 报告重叠情况
    if len(overlap_groups) < len(df["lib"].unique()):
        for hit_rates_key, libs_groups in overlap_groups.items():
            if len(libs_groups) > 1:
                lib_names = [lib for lib, _ in libs_groups]
                hit_rate_str = f"{libs_groups[0][1]['hit_rate'].iloc[0]:.2f}%"
                print(f'   重叠组: {", ".join(lib_names)} (命中率: {hit_rate_str})')
    
    # 按算法类型分组显示
    lru_like = ['otter', 'golang-lru', 'ristretto']
    fifo_like = ['bigcache', 'fastcache', 'go-cache', 'sync-map', 'freecache']
    
    print(f'\n=== 算法表现分析 ===')
    for lib, group in df.groupby('lib'):
        min_cap = group['capacity'].min()
        max_cap = group['capacity'].max()
        min_hr = group.loc[group['capacity'] == min_cap, 'hit_rate'].iloc[0]
        max_hr = group.loc[group['capacity'] == max_cap, 'hit_rate'].iloc[0]
        improvement = max_hr - min_hr
        
        algo_type = 'LRU/LFU' if lib in lru_like else 'FIFO/无限制'
        print(f'{lib:12s} ({algo_type:8s}): {min_hr:6.2f}% → {max_hr:6.2f}% (Δ{improvement:+6.2f}%)')

if __name__ == '__main__':
    import sys
    import os
    
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg == 'perf':
            draw_perf()
        elif arg == 'memory':
            draw_memory_bar()  # 显示所有容量的对比
        elif arg == 'memory_baseline':
            draw_memory_baseline_syncmap()  # 显示以SyncMap为基准的对比
        elif arg == 'cpu_eff':
            draw_cpu_efficiency()
        elif arg == 'gc':
            draw_gc()
        elif arg == 'hitrate_simulator':
            draw_hitrate_simulator('hitrate_simulator_clean.json')
        else:
            print(f'Unknown command: {arg}')
            print('Try: python3 draw.py [command] or python3 draw.py [file.json]')
            print('command: perf, memory, memory_baseline, gc, cpu_eff, hitrate_simulator')
    else:
        print('command not found')