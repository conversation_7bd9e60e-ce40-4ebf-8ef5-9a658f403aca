# SLS Engineering Campus

本指南旨在帮助SLS Developer深入学习和掌握SLS开发中常用的技术和工具。通过各个模块的学习，你将能够在实际项目中应用这些知识。

## 目录

### Go语言基础与框架
- **语法基础**: 了解Go语言的基本语法和编程范式。
- **Chassis**: 学习如何使用Chassis框架构建微服务。
- **Gorm**: 掌握Gorm进行数据库操作。
- **协程池**: 理解协程池的实现和应用场景。

### 研发经验与规范
- **Server开发规范**: 介绍服务端开发的最佳实践。
- **脚本工具规范**: 提供编写和维护脚本工具的标准和规范。

### 性能优化
- **pprof**: 使用pprof进行性能分析和优化。
- **GC调优**: 掌握垃圾回收机制的调优策略。

### 数据结构与算法
- **Hash算法**: 深入理解和实现常用的哈希算法。
- **压缩算法**: 学习数据压缩的基本原理和常见算法。

### 数据存储与缓存
- **MySQL**: 掌握MySQL的使用和优化技巧。
- **Redis/Codis**: 了解Redis和Codis的缓存机制。
- **Local Cache**: 实现和优化本地缓存策略。

### 通信与消息队列
- **gRPC**: 使用gRPC进行高效的RPC通信。
- **Kafka**: 学习Kafka的消息队列机制及应用场景。
- **HTTP**: 了解HTTP协议的工作原理。

### 可观测性
- **Prometheus**: 使用Prometheus进行监控和指标采集。
- **日志系统**: 构建和优化日志系统以提高可观测性。

### 大数据处理
- **ElasticSearch**: 掌握ElasticSearch的搜索和分析功能。
- **Flink**: 学习Flink进行实时数据流处理。
