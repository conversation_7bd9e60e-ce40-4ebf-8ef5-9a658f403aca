package localcache

import (
	"fmt"
	"testing"
	"time"

	fastcache "github.com/VictoriaMetrics/fastcache"
	bigcache "github.com/allegro/bigcache/v3"
	freecache "github.com/coocood/freecache"
	ristretto "github.com/dgraph-io/ristretto"
	golru "github.com/hashicorp/golang-lru/v2/simplelru"
	otter "github.com/maypok86/otter"
	gocache "github.com/patrickmn/go-cache"
)

func TestGoCacheBasic(t *testing.T) {
	// GoCache: 简单内存缓存，支持过期
	c := gocache.New(5*time.Minute, 10*time.Minute)
	c.Set("foo", "bar", gocache.DefaultExpiration)
	v, found := c.Get("foo")
	fmt.Println("GoCache:", v, found)
}

func TestBigCacheBasic(t *testing.T) {
	// BigCache: 高性能分片缓存
	cfg := bigcache.DefaultConfig(10 * time.Minute)
	bc, _ := bigcache.NewBigCache(cfg)
	bc.Set("foo", []byte("bar"))
	v, _ := bc.Get("foo")
	fmt.Println("BigCache:", string(v))
}

func TestFreeCacheBasic(t *testing.T) {
	// FreeCache: 零GC高性能缓存
	fc := freecache.NewCache(1024 * 1024)
	fc.Set([]byte("foo"), []byte("bar"), 60)
	v, _ := fc.Get([]byte("foo"))
	fmt.Println("FreeCache:", string(v))
}

func TestRistrettoBasic(t *testing.T) {
	// Ristretto: 智能淘汰高性能缓存
	cache, _ := ristretto.NewCache(&ristretto.Config{
		NumCounters: 1e4, MaxCost: 1 << 20, BufferItems: 64,
	})
	cache.Set("foo", "bar", 1)
	time.Sleep(10 * time.Millisecond) // 等待异步写入
	v, found := cache.Get("foo")
	fmt.Println("Ristretto:", v, found)
}

func TestFastCacheBasic(t *testing.T) {
	// FastCache: 极致内存效率
	fc := fastcache.New(1024 * 1024)
	fc.Set([]byte("foo"), []byte("bar"))
	v := fc.Get(nil, []byte("foo"))
	fmt.Println("FastCache:", string(v))
}

func TestGoLRUBasic(t *testing.T) {
	// GoLRU: 标准 LRU 算法
	lru, _ := golru.NewLRU[string, string](128, nil)
	lru.Add("foo", "bar")
	v, ok := lru.Get("foo")
	fmt.Println("GoLRU:", v, ok)
}

func TestOtterBasic(t *testing.T) {
	// Otter: 现代 TinyLFU 高性能缓存
	cache, _ := otter.MustBuilder[string, string](128).Build()
	cache.Set("foo", "bar")
	v, ok := cache.Get("foo")
	fmt.Println("Otter:", v, ok)
}
