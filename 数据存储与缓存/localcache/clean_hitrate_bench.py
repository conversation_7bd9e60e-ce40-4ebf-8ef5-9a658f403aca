import re
import json

lines = open('hitrate_bench_lines.txt', encoding='utf-8').readlines()
result = []
for line in lines:
    # 例: BenchmarkCacheHitRate/bigcache_cap1000-12 ...
    m = re.match(
        r'.*BenchmarkCacheHitRate/([A-Za-z0-9_.\-]+?)(?:_cap)?(\d+)?-?\d*\s+\d+\s+[\d.]+ ns/op\s+([\d.]+) hit_rate\(%\)',
        line
    )
    if m:
        lib_full = m.group(1)
        cap = m.group(2)
        hitrate = float(m.group(3))
        # 进一步拆分库名和容量
        # 支持 bigcache_cap1000、sync.Map_cap1000、go-cache_cap1000 等
        lib_match = re.match(r'([A-Za-z0-9.\-]+?)(?:[_-]cap)?(\d+)?$', lib_full)
        if lib_match:
            lib = lib_match.group(1)
            if not cap and lib_match.group(2):
                cap = lib_match.group(2)
        else:
            lib = lib_full
        result.append({
            "lib": lib,
            "capacity": int(cap) if cap else None,
            "hit_rate": hitrate
        })

with open('hitrate_bench_clean.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=2)
print(f"清洗完成，输出 hitrate_bench_clean.json，共 {len(result)} 条记录。")