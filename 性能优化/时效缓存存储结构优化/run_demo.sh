#!/bin/bash

# Redis键优化实验环境演示脚本

echo "==============================================="
echo "        Redis键优化实验环境演示"
echo "==============================================="

# 检查是否在Docker环境中
if [ -n "$REDIS_HOST" ]; then
    REDIS_CMD="redis-cli -h $REDIS_HOST -p ${REDIS_PORT:-6379}"
    echo "🐳 检测到Docker环境，使用Redis主机: $REDIS_HOST"
else
    REDIS_CMD="redis-cli"
    echo "💻 使用本地Redis"
fi

# 检查Redis是否运行
echo "1. 检查Redis连接..."
if ! $REDIS_CMD ping > /dev/null 2>&1; then
    echo "❌ Redis未运行，请先启动Redis服务"
    if [ -z "$REDIS_HOST" ]; then
        echo "   本地启动: redis-server"
        echo "   或使用Docker: make up"
    else
        echo "   使用Docker: make up"
    fi
    exit 1
fi
echo "✅ Redis连接正常"

# 清理Redis数据
echo "2. 清理Redis数据..."
$REDIS_CMD flushall > /dev/null 2>&1
echo "✅ Redis数据已清理"

# 安装依赖
echo "3. 安装Go依赖..."
go mod tidy > /dev/null 2>&1
echo "✅ 依赖安装完成"

# 运行基础示例
echo "4. 运行基础编码器示例..."
cd examples
go run basic_usage.go
echo ""

# 运行单元测试
echo "5. 运行单元测试..."
cd ../tests
go test -v -timeout 30s > test_results.log 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 单元测试通过"
    echo "   详细结果: tests/test_results.log"
else
    echo "❌ 单元测试失败，查看详细日志: tests/test_results.log"
fi

# 运行性能基准测试
echo "6. 运行性能基准测试..."
go test -bench=BenchmarkEncoders -benchtime=1s -timeout 60s > benchmark_results.log 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 基准测试完成"
    echo "   详细结果: tests/benchmark_results.log"
else
    echo "❌ 基准测试失败"
fi

# 运行内存分析（小样本）
echo "7. 运行内存分析（小样本演示）..."
cd ../benchmarks
cat > memory_config.json << EOF
{
  "sample_sizes": [1000, 5000],
  "redis_host": "localhost:6379",
  "output_dir": ".",
  "generate_charts": false,
  "detailed_report": true
}
EOF

# 修改内存分析器为小样本
sed -i.bak 's/SampleSizes:.*\[\]/SampleSizes: []int{1000, 5000}/g' memory_analyzer.go

timeout 120s go run memory_analyzer.go > memory_analysis.log 2>&1 &
MEMORY_PID=$!

echo "   内存分析运行中(最多2分钟)..."
wait $MEMORY_PID 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ 内存分析完成"
    echo "   详细结果: benchmarks/memory_analysis_*.json"
    echo "   日志文件: benchmarks/memory_analysis.log"
else
    echo "⚠️  内存分析超时或失败（可手动运行）"
fi

# 恢复原始文件
mv memory_analyzer.go.bak memory_analyzer.go 2>/dev/null

# 运行Redis集成示例
echo "8. 运行Redis集成示例..."
cd ../examples
timeout 60s go run redis_integration.go > redis_integration.log 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis集成示例完成"
    echo "   详细结果: examples/redis_integration.log"
else
    echo "❌ Redis集成示例失败，查看日志: examples/redis_integration.log"
fi

# 展示结果摘要
echo ""
echo "==============================================="
echo "                  演示完成"
echo "==============================================="
echo ""
echo "📁 生成的文件："
echo "   - tests/test_results.log          # 单元测试结果"
echo "   - tests/benchmark_results.log     # 性能基准结果" 
echo "   - benchmarks/memory_analysis_*.json # 内存分析报告"
echo "   - examples/redis_integration.log  # 集成示例日志"
echo ""
echo "🚀 下一步："
echo "   1. 查看详细的内存分析报告了解优化效果"
echo "   2. 尝试运行完整基准测试: cd benchmarks && go run redis_benchmark.go"
echo "   3. 测试数据迁移功能: cd migration && go run migration_tool.go dryrun"
echo ""
echo "📖 更多信息请查看 README.md"
echo ""

# 显示简要性能对比（如果有结果文件）
cd ..
if [ -f "benchmarks/memory_analysis.log" ]; then
    echo "💡 内存优化效果预览："
    echo "   (查看完整报告获取详细数据)"
    grep -A 5 "键长度对比" benchmarks/memory_analysis.log 2>/dev/null || true
fi

echo "演示脚本执行完毕！"
