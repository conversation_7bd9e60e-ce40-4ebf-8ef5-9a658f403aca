package main

import (
	"fmt"
	"time"
)

func main() {
	fmt.Println("=== Item Card 页面批量查询演示 ===\n")
	
	// 初始化数据
	fmt.Println("1. 初始化物流服务数据...")
	data := generateDecoupledData()
	nativeBitVector := NewNativeBitVector(data)
	invertedIndexMap := NewInvertedIndexMap(data)
	fmt.Printf("   - 物流商数量: %d\n", Num3PLs)
	fmt.Printf("   - 地址总数: %d\n", NumLocations)
	fmt.Printf("   - 数据结构构建完成\n\n")
	
	// 生成批量请求
	fmt.Println("2. 生成 Item Card 页面请求...")
	requests := generateBatchRequests(1)
	req := requests[0]
	
	fmt.Printf("   - 买家位置: %d\n", req.BuyerID)
	fmt.Printf("   - 页面商品数: %d\n", len(req.Items))
	fmt.Printf("   - 每个商品的物流选项: %d\n", Num3PLsPerItem)
	fmt.Printf("   - 总查询数: %d\n\n", len(req.Items)*Num3PLsPerItem)
	
	// 性能测试
	fmt.Println("3. 性能对比测试...")
	
	// NativeBitVector 测试
	start := time.Now()
	results1 := processBatchRequest(nativeBitVector, req)
	duration1 := time.Since(start)
	
	// InvertedIndexMap 测试
	start = time.Now()
	results2 := processBatchRequestMap(invertedIndexMap, req)
	duration2 := time.Since(start)
	
	fmt.Printf("   - NativeBitVector: %v\n", duration1)
	fmt.Printf("   - InvertedIndexMap: %v\n", duration2)
	fmt.Printf("   - 性能提升: %.1f%%\n\n", float64(duration2-duration1)/float64(duration2)*100)
	
	// 结果展示
	fmt.Println("4. 查询结果展示 (前5个商品):")
	fmt.Println("   商品ID | 卖家位置 | 3PL服务可达性")
	fmt.Println("   -------|----------|------------------")
	
	for i := 0; i < 5 && i < len(req.Items); i++ {
		item := req.Items[i]
		fmt.Printf("   %6d | %8d | ", item.ItemID, item.SellerID)
		
		for j, tplID := range item.TplIDs {
			if results1[i][j] {
				fmt.Printf("3PL%d:✅ ", tplID)
			} else {
				fmt.Printf("3PL%d:❌ ", tplID)
			}
		}
		fmt.Println()
	}
	
	// 统计信息
	fmt.Println("\n5. 统计信息:")
	totalQueries := len(req.Items) * Num3PLsPerItem
	serviceableCount := 0
	
	for i := range results1 {
		for j := range results1[i] {
			if results1[i][j] {
				serviceableCount++
			}
		}
	}
	
	fmt.Printf("   - 总查询数: %d\n", totalQueries)
	fmt.Printf("   - 可服务查询: %d\n", serviceableCount)
	fmt.Printf("   - 服务覆盖率: %.1f%%\n", float64(serviceableCount)/float64(totalQueries)*100)
	
	// 验证一致性
	consistent := true
	for i := range results1 {
		for j := range results1[i] {
			if results1[i][j] != results2[i][j] {
				consistent = false
				break
			}
		}
		if !consistent {
			break
		}
	}
	
	if consistent {
		fmt.Println("   - 两种实现结果一致 ✅")
	} else {
		fmt.Println("   - 两种实现结果不一致 ❌")
	}
	
	fmt.Println("\n=== 演示完成 ===")
}
