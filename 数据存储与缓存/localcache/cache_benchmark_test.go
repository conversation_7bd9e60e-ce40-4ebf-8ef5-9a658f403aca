package localcache

import (
	"context"
	"fmt"
	"math/rand"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"testing"
	"time"

	"github.com/VictoriaMetrics/fastcache"
	"github.com/allegro/bigcache/v3"
	"github.com/coocood/freecache"
	"github.com/dgraph-io/ristretto"
	lru "github.com/hashicorp/golang-lru"
	lruv2 "github.com/hashicorp/golang-lru/v2"
	"github.com/maypok86/otter"
	gocache "github.com/patrickmn/go-cache"
)

const (
	// 测试数据量
	numKeys = 100000
	// key和value的长度
	keyLength   = 30
	valueLength = 30
)

var (
	// 预生成的测试数据
	testKeys   []string
	testValues [][]byte
)

// 初始化测试数据
func init() {
	rand.Seed(time.Now().UnixNano())

	// 生成测试键
	testKeys = make([]string, numKeys)
	for i := 0; i < numKeys; i++ {
		testKeys[i] = generateRandomString(keyLength)
	}

	// 生成测试值
	testValues = make([][]byte, numKeys)
	for i := 0; i < numKeys; i++ {
		testValues[i] = []byte(generateRandomString(valueLength))
	}
}

// 生成指定长度的随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// ===== 读写性能测试 =====
func BenchmarkReadWritePerformance(b *testing.B) {
	ratios := []struct {
		name      string
		readRatio float64
	}{
		{"100%_Read", 1.0},
		{"75%_Read_25%_Write", 0.75},
		{"50%_Read_50%_Write", 0.5},
		{"25%_Read_75%_Write", 0.25},
		{"100%_Write", 0.0},
	}

	concurrencyLevels := []int{3000}

	// 初始化所有缓存
	goCache := gocache.New(5*time.Minute, 10*time.Minute)
	bigCacheConfig := bigcache.Config{
		Shards:             1024,
		LifeWindow:         5 * time.Minute,
		CleanWindow:        10 * time.Minute,
		MaxEntriesInWindow: 1000 * 10 * 60,
		MaxEntrySize:       500,
		StatsEnabled:       false,
		Verbose:            false,
		HardMaxCacheSize:   0,
		Logger:             nil,
	}
	bigCache, _ := bigcache.NewBigCache(bigCacheConfig)
	freeCache := freecache.NewCache(numKeys * 100)
	ristrettoCache, _ := ristretto.NewCache(&ristretto.Config{
		NumCounters: 1e7,
		MaxCost:     1e7,
		BufferItems: 64,
	})
	fastCache := fastcache.New(numKeys * 100)
	goLRU, _ := lru.New(numKeys)
	otterCache, _ := otter.MustBuilder[string, []byte](numKeys).Build()
	defer otterCache.Close()

	// 预热所有缓存
	for i := 0; i < numKeys; i++ {
		goCache.Set(testKeys[i], testValues[i], gocache.DefaultExpiration)
		_ = bigCache.Set(testKeys[i], testValues[i])
		_ = freeCache.Set([]byte(testKeys[i]), testValues[i], 0)
		_ = ristrettoCache.Set(testKeys[i], testValues[i], 1)
		fastCache.Set([]byte(testKeys[i]), testValues[i])
		goLRU.Add(testKeys[i], testValues[i])
		otterCache.Set(testKeys[i], testValues[i])
	}
	// sync.Map 预热
	var syncMap sync.Map
	for i := 0; i < numKeys; i++ {
		syncMap.Store(testKeys[i], testValues[i])
	}
	ristrettoCache.Wait()

	for _, ratio := range ratios {
		for _, concurrency := range concurrencyLevels {
			// GoCache
			b.Run(fmt.Sprintf("GoCache_%s_%d", ratio.name, concurrency), func(b *testing.B) {
				b.SetParallelism(concurrency)
				b.ResetTimer()
				b.RunParallel(func(pb *testing.PB) {
					localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
					for pb.Next() {
						if localRand.Float64() < ratio.readRatio {
							key := testKeys[localRand.Intn(numKeys)]
							_, _ = goCache.Get(key)
						} else {
							key := testKeys[localRand.Intn(numKeys)]
							value := testValues[localRand.Intn(numKeys)]
							goCache.Set(key, value, gocache.DefaultExpiration)
						}
					}
				})
			})

			// BigCache
			b.Run(fmt.Sprintf("BigCache_%s_%d", ratio.name, concurrency), func(b *testing.B) {
				b.SetParallelism(concurrency)
				b.ResetTimer()
				b.RunParallel(func(pb *testing.PB) {
					localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
					for pb.Next() {
						if localRand.Float64() < ratio.readRatio {
							key := testKeys[localRand.Intn(numKeys)]
							_, _ = bigCache.Get(key)
						} else {
							key := testKeys[localRand.Intn(numKeys)]
							value := testValues[localRand.Intn(numKeys)]
							_ = bigCache.Set(key, value)
						}
					}
				})
			})

			// FreeCache
			b.Run(fmt.Sprintf("FreeCache_%s_%d", ratio.name, concurrency), func(b *testing.B) {
				b.SetParallelism(concurrency)
				b.ResetTimer()
				b.RunParallel(func(pb *testing.PB) {
					localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
					for pb.Next() {
						if localRand.Float64() < ratio.readRatio {
							key := testKeys[localRand.Intn(numKeys)]
							_, _ = freeCache.Get([]byte(key))
						} else {
							key := testKeys[localRand.Intn(numKeys)]
							value := testValues[localRand.Intn(numKeys)]
							_ = freeCache.Set([]byte(key), value, 0)
						}
					}
				})
			})

			// Ristretto
			b.Run(fmt.Sprintf("Ristretto_%s_%d", ratio.name, concurrency), func(b *testing.B) {
				b.SetParallelism(concurrency)
				b.ResetTimer()
				b.RunParallel(func(pb *testing.PB) {
					localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
					for pb.Next() {
						if localRand.Float64() < ratio.readRatio {
							key := testKeys[localRand.Intn(numKeys)]
							_, _ = ristrettoCache.Get(key)
						} else {
							key := testKeys[localRand.Intn(numKeys)]
							value := testValues[localRand.Intn(numKeys)]
							_ = ristrettoCache.Set(key, value, 1)
						}
					}
				})
			})

			// FastCache
			b.Run(fmt.Sprintf("FastCache_%s_%d", ratio.name, concurrency), func(b *testing.B) {
				b.SetParallelism(concurrency)
				b.ResetTimer()
				b.RunParallel(func(pb *testing.PB) {
					localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
					for pb.Next() {
						if localRand.Float64() < ratio.readRatio {
							key := testKeys[localRand.Intn(numKeys)]
							_ = fastCache.Get(nil, []byte(key))
						} else {
							key := testKeys[localRand.Intn(numKeys)]
							value := testValues[localRand.Intn(numKeys)]
							fastCache.Set([]byte(key), value)
						}
					}
				})
			})

			// GoLRU
			b.Run(fmt.Sprintf("GoLRU_%s_%d", ratio.name, concurrency), func(b *testing.B) {
				b.SetParallelism(concurrency)
				b.ResetTimer()
				b.RunParallel(func(pb *testing.PB) {
					localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
					for pb.Next() {
						if localRand.Float64() < ratio.readRatio {
							key := testKeys[localRand.Intn(numKeys)]
							_, _ = goLRU.Get(key)
						} else {
							key := testKeys[localRand.Intn(numKeys)]
							value := testValues[localRand.Intn(numKeys)]
							goLRU.Add(key, value)
						}
					}
				})
			})

			// Otter
			b.Run(fmt.Sprintf("Otter_%s_%d", ratio.name, concurrency), func(b *testing.B) {
				b.SetParallelism(concurrency)
				b.ResetTimer()
				b.RunParallel(func(pb *testing.PB) {
					localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
					for pb.Next() {
						if localRand.Float64() < ratio.readRatio {
							key := testKeys[localRand.Intn(numKeys)]
							_, _ = otterCache.Get(key)
						} else {
							key := testKeys[localRand.Intn(numKeys)]
							value := testValues[localRand.Intn(numKeys)]
							otterCache.Set(key, value)
						}
					}
				})
			})
			// sync.Map
			b.Run(fmt.Sprintf("SyncMap_%s_%d", ratio.name, concurrency), func(b *testing.B) {
				b.SetParallelism(concurrency)
				b.ResetTimer()
				b.RunParallel(func(pb *testing.PB) {
					localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
					for pb.Next() {
						if localRand.Float64() < ratio.readRatio {
							key := testKeys[localRand.Intn(numKeys)]
							_, _ = syncMap.Load(key)
						} else {
							key := testKeys[localRand.Intn(numKeys)]
							value := testValues[localRand.Intn(numKeys)]
							syncMap.Store(key, value)
						}
					}
				})
			})
		}
	}
}

// ===== 内存使用测试 =====
func BenchmarkMemoryUsage(b *testing.B) {
	capacities := []int{10000, 100000, 1000000}

	for _, cap := range capacities {
		// 统一生成key/value
		keys := make([]string, cap)
		values := make([][]byte, cap)
		for i := 0; i < cap; i++ {
			keys[i] = fmt.Sprintf("key%d", i)
			values[i] = []byte(fmt.Sprintf("value%d", i))
		}

		b.Run(fmt.Sprintf("GoCache_%d", cap), func(b *testing.B) {
			// 强制GC，获得干净的内存基线
			runtime.GC()

			var o runtime.MemStats
			runtime.ReadMemStats(&o)

			cache := gocache.New(5*time.Minute, 10*time.Minute)
			for i := 0; i < cap; i++ {
				cache.Set(keys[i], values[i], gocache.DefaultExpiration)
			}

			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			b.ReportMetric(float64(m.Alloc-o.Alloc)/1024/1024, "MB")
			b.ReportMetric(float64(m.Alloc-o.Alloc)/float64(cap), "B/entry")
		})

		b.Run(fmt.Sprintf("BigCache_%d", cap), func(b *testing.B) {
			runtime.GC()

			var o runtime.MemStats
			runtime.ReadMemStats(&o)

			config := bigcache.DefaultConfig(10 * time.Minute)

			// 动态分片数 - 根据容量调整，避免固定1024分片的浪费
			if cap <= 10000 {
				config.Shards = 16
			} else if cap <= 100000 {
				config.Shards = 64
			} else {
				config.Shards = 256
			}

			// 设置合理的内存限制和容量控制
			config.MaxEntriesInWindow = cap
			config.MaxEntrySize = 500
			estimatedMemoryMB := (cap * 120) / (1024 * 1024) // 120B per entry estimate
			if estimatedMemoryMB < 1 {
				estimatedMemoryMB = 1
			}
			config.HardMaxCacheSize = estimatedMemoryMB

			cache, _ := bigcache.New(context.Background(), config)
			defer cache.Close()

			for i := 0; i < cap; i++ {
				cache.Set(keys[i], values[i])
			}

			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			b.ReportMetric(float64(m.Alloc-o.Alloc)/1024/1024, "MB")
			b.ReportMetric(float64(m.Alloc-o.Alloc)/float64(cap), "B/entry")
		})

		b.Run(fmt.Sprintf("FreeCache_%d", cap), func(b *testing.B) {
			runtime.GC()

			var o runtime.MemStats
			runtime.ReadMemStats(&o)

			// 根据容量动态调整内存分配，使用更合理的估算
			memorySize := cap * 120 // 120B per entry (key + value + overhead)
			if memorySize < 1024*1024 {
				memorySize = 1024 * 1024 // 最小1MB
			}

			cache := freecache.NewCache(memorySize)

			for i := 0; i < cap; i++ {
				cache.Set([]byte(keys[i]), values[i], 300)
			}

			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			b.ReportMetric(float64(m.Alloc-o.Alloc)/1024/1024, "MB")
			b.ReportMetric(float64(m.Alloc-o.Alloc)/float64(cap), "B/entry")
		})

		b.Run(fmt.Sprintf("Ristretto_%d", cap), func(b *testing.B) {
			runtime.GC()

			var o runtime.MemStats
			runtime.ReadMemStats(&o)

			config := &ristretto.Config{
				NumCounters: int64(cap) * 10,
				MaxCost:     int64(cap),
				BufferItems: 64,
			}
			cache, _ := ristretto.NewCache(config)
			defer cache.Close()

			for i := 0; i < cap; i++ {
				cache.Set(keys[i], values[i], int64(len(values[i])))
			}
			cache.Wait() // 等待异步操作完成

			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			b.ReportMetric(float64(m.Alloc-o.Alloc)/1024/1024, "MB")
			b.ReportMetric(float64(m.Alloc-o.Alloc)/float64(cap), "B/entry")
		})

		b.Run(fmt.Sprintf("FastCache_%d", cap), func(b *testing.B) {
			runtime.GC()

			var o runtime.MemStats
			runtime.ReadMemStats(&o)

			// 使用合理的内存分配，与其他缓存库保持一致
			memorySize := cap * 120 // 120B per entry
			if memorySize < 1024*1024 {
				memorySize = 1024 * 1024
			}

			cache := fastcache.New(memorySize)
			defer cache.Reset()

			for i := 0; i < cap; i++ {
				cache.Set([]byte(keys[i]), values[i])
			}

			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			b.ReportMetric(float64(m.Alloc-o.Alloc)/1024/1024, "MB")
			b.ReportMetric(float64(m.Alloc-o.Alloc)/float64(cap), "B/entry")
		})

		b.Run(fmt.Sprintf("GoLRU_%d", cap), func(b *testing.B) {
			runtime.GC()

			var o runtime.MemStats
			runtime.ReadMemStats(&o)

			cache, _ := lru.New(cap)

			for i := 0; i < cap; i++ {
				cache.Add(keys[i], values[i])
			}

			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			b.ReportMetric(float64(m.Alloc-o.Alloc)/1024/1024, "MB")
			b.ReportMetric(float64(m.Alloc-o.Alloc)/float64(cap), "B/entry")
		})

		b.Run(fmt.Sprintf("Otter_%d", cap), func(b *testing.B) {
			runtime.GC()

			var o runtime.MemStats
			runtime.ReadMemStats(&o)

			cache, _ := otter.MustBuilder[string, []byte](cap).Build()
			defer cache.Close()

			for i := 0; i < cap; i++ {
				cache.Set(keys[i], values[i])
			}

			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			b.ReportMetric(float64(m.Alloc-o.Alloc)/1024/1024, "MB")
			b.ReportMetric(float64(m.Alloc-o.Alloc)/float64(cap), "B/entry")
		})

		b.Run(fmt.Sprintf("SyncMap_%d", cap), func(b *testing.B) {
			runtime.GC()

			var o runtime.MemStats
			runtime.ReadMemStats(&o)

			var sm sync.Map

			for i := 0; i < cap; i++ {
				sm.Store(keys[i], values[i])
			}

			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			b.ReportMetric(float64(m.Alloc-o.Alloc)/1024/1024, "MB")
			b.ReportMetric(float64(m.Alloc-o.Alloc)/float64(cap), "B/entry")
		})
	}
}

// ===== 并发饱和度测试 =====
func BenchmarkCacheConcurrencySaturation(b *testing.B) {
	concurrencyLevels := []int{1, 2, 4, 8, 16, 32, 64, 128}
	keySpace := 100_000
	readRatio := 0.8
	duration := 10 * time.Second

	clients := CreateHitRateTestClients(100000)

	type result struct {
		concurrency int
		qps         float64
		cpuAvg      float64
		cpuMax      float64
	}

	for _, c := range clients {
		b.Run(c.Name(), func(b *testing.B) {
			for _, conc := range concurrencyLevels {
				var wg sync.WaitGroup
				var counter int64
				stop := make(chan struct{})
				cpuUsage := make([]float64, 0, 32)
				// CPU monitor goroutine
				go func() {
					for {
						select {
						case <-stop:
							return
						default:
							u := measureCPU(500 * time.Millisecond)
							cpuUsage = append(cpuUsage, u)
						}
					}
				}()
				for i := 0; i < conc; i++ {
					wg.Add(1)
					go func() {
						r := rand.New(rand.NewSource(time.Now().UnixNano()))
						for {
							select {
							case <-stop:
								wg.Done()
								return
							default:
								key := r.Intn(keySpace)
								if r.Float64() < readRatio {
									c.Get(strconv.Itoa(key))
								} else {
									c.Set(strconv.Itoa(key), strconv.Itoa(key))
								}
								atomic.AddInt64(&counter, 1)
							}
						}
					}()
				}
				t0 := time.Now()
				time.Sleep(duration)
				close(stop)
				wg.Wait()
				elapsed := time.Since(t0).Seconds()
				opCount := atomic.LoadInt64(&counter)
				qps := float64(opCount) / elapsed
				cpuAvg := 0.0
				cpuMax := 0.0
				for _, u := range cpuUsage {
					cpuAvg += u
				}
				cpuAvg /= float64(len(cpuUsage))
				cpuMax = cpuAvg

				b.Logf("[%s] concurrency=%d QPS=%.0f CPU_avg=%.1f%% CPU_peak=%.1f%%", c.Name(), conc, qps, cpuAvg, cpuMax)
			}
		})
	}
}

// 近似测量进程CPU利用率（单核百分比），适用于类Unix系统
func measureCPU(d time.Duration) float64 {
	// 记录进程总CPU时间
	var ru1, ru2 syscall.Rusage
	sycallGetrusage := func(ru *syscall.Rusage) {
		sycall := syscall.Getrusage
		_ = sycall(syscall.RUSAGE_SELF, ru)
	}
	sycallGetrusage(&ru1)
	start := time.Now()

	time.Sleep(d)
	sycallGetrusage(&ru2)
	elapsed := time.Since(start).Seconds()
	user := float64(ru2.Utime.Sec-ru1.Utime.Sec) + float64(ru2.Utime.Usec-ru1.Utime.Usec)/1e6
	sys := float64(ru2.Stime.Sec-ru1.Stime.Sec) + float64(ru2.Stime.Usec-ru1.Stime.Usec)/1e6
	cpu := user + sys
	return cpu / elapsed * 100
}

// ===== GC 性能测试 =====
func BenchmarkCacheGC(b *testing.B) {
	type cacheCase struct {
		name  string
		setup func(keys []string, value []byte) (cleanup func())
	}
	const (
		numEntries = 2_000_000
		valueSize  = 100
		repeats    = 50
	)
	value := make([]byte, valueSize)
	for i := range value {
		value[i] = byte(i)
	}
	keys := make([]string, numEntries)
	for i := 0; i < numEntries; i++ {
		keys[i] = fmt.Sprintf("key-%d", i)
	}
	cases := []cacheCase{
		{
			name: "GoCache",
			setup: func(keys []string, value []byte) func() {
				cache := gocache.New(5*time.Minute, 10*time.Minute)
				for i := 0; i < numEntries; i++ {
					cache.Set(keys[i], value, gocache.DefaultExpiration)
				}
				return func() {}
			},
		},
		{
			name: "BigCache",
			setup: func(keys []string, value []byte) func() {
				config := bigcache.DefaultConfig(10 * time.Minute)
				config.MaxEntrySize = valueSize
				config.MaxEntriesInWindow = numEntries
				cache, _ := bigcache.New(context.Background(), config)
				for i := 0; i < numEntries; i++ {
					cache.Set(keys[i], value)
				}
				return func() { cache.Close() }
			},
		},
		{
			name: "FreeCache",
			setup: func(keys []string, value []byte) func() {
				cache := freecache.NewCache(numEntries * valueSize * 2)
				for i := 0; i < numEntries; i++ {
					cache.Set([]byte(keys[i]), value, 0)
				}
				return func() {}
			},
		},
		{
			name: "Ristretto",
			setup: func(keys []string, value []byte) func() {
				cache, _ := ristretto.NewCache(&ristretto.Config{
					NumCounters: numEntries * 10,
					MaxCost:     int64(numEntries * valueSize * 2),
					BufferItems: 64,
				})
				for i := 0; i < numEntries; i++ {
					cache.Set(keys[i], value, int64(valueSize))
				}
				return func() { cache.Close() }
			},
		},
		{
			name: "FastCache",
			setup: func(keys []string, value []byte) func() {
				cache := fastcache.New(numEntries * valueSize * 2)
				for i := 0; i < numEntries; i++ {
					cache.Set([]byte(keys[i]), value)
				}
				return func() { cache.Reset() }
			},
		},
		{
			name: "GoLRU",
			setup: func(keys []string, value []byte) func() {
				cache, _ := lruv2.New[string, []byte](numEntries)
				for i := 0; i < numEntries; i++ {
					cache.Add(keys[i], value)
				}
				return func() {}
			},
		},
		{
			name: "Otter",
			setup: func(keys []string, value []byte) func() {
				cache, _ := otter.MustBuilder[string, []byte](numEntries).Build()
				for i := 0; i < numEntries; i++ {
					cache.Set(keys[i], value)
				}
				return func() { cache.Close() }
			},
		},
		{
			name: "SyncMap",
			setup: func(keys []string, value []byte) func() {
				var sm sync.Map
				for i := 0; i < numEntries; i++ {
					sm.Store(keys[i], value)
				}
				return func() {}
			},
		},
	}
	for _, cc := range cases {
		b.Run(cc.name, func(b *testing.B) {
			cleanup := cc.setup(keys, value)
			defer cleanup()
			runtime.GC()
			var m1, m2 runtime.MemStats
			runtime.ReadMemStats(&m1)
			pauseBefore := m1.PauseTotalNs
			for i := 0; i < repeats; i++ {
				runtime.GC()
			}
			runtime.ReadMemStats(&m2)
			pauseAfter := m2.PauseTotalNs
			pauseMs := float64(pauseAfter-pauseBefore) / 1e6
			b.ReportMetric(pauseMs, "GC_pause_ms")
			b.ReportMetric(float64(m2.NumGC-m1.NumGC), "GC_cycles")
		})
	}
}

// ===== Cache Hit Rate Simulator Benchmark =====

func BenchmarkCacheHitRateSimulator(b *testing.B) {
	// Test configuration
	capacities := []int{500, 1000, 2000, 5000, 10000}

	// Fixed Zipf parameters - optimized for faster testing
	zipfS := 1.0001
	zipfV := 10.0
	imax := uint64(50000000) // Fixed key space
	limit := uint64(100000)  // 100K operations (reduced for faster testing)
	seed := int64(12345)     // Fixed seed for reproducibility

	// Create trace generator
	generator := NewZipfTraceGenerator(zipfS, zipfV, imax, limit, seed)

	// Create policies
	policies := []Policy{
		&BigCachePolicy{},
		&FreeCachePolicy{},
		&RistrettoPolicy{},
		&GolangLRUPolicy{},
		&GoCachePolicy{},
		&OtterPolicy{},
		&FastCachePolicy{},
		&SyncMapPolicy{},
	}

	// Create and run simulator
	simulator := NewCacheSimulator(capacities, policies, generator)
	results, err := simulator.Simulate()
	if err != nil {
		b.Fatalf("Simulation failed: %v", err)
	}

	// Report results
	b.Logf("=== Cache Hit Rate Simulation Results ===")
	b.Logf("Parameters: zipf(s=%.4f, v=%.1f, imax=%d), operations=%d", zipfS, zipfV, imax, limit)
	b.Logf("%-12s %8s %12s", "Cache", "Capacity", "Hit Rate (%)")
	b.Logf("%s", strings.Repeat("-", 35))

	for _, result := range results {
		b.Logf("%-12s %8d %12.2f", result.Name(), result.Capacity(), result.HitRatio())
		// Also report as benchmark metrics for easier parsing
		b.Run(fmt.Sprintf("%s_cap%d", result.Name(), result.Capacity()), func(b *testing.B) {
			b.ReportMetric(result.HitRatio(), "hit_rate(%)")
		})
	}
}
