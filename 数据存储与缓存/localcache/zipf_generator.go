package localcache

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/VictoriaMetrics/fastcache"
	"github.com/allegro/bigcache/v3"
	"github.com/coocood/freecache"
	"github.com/dgraph-io/ristretto"
	lru "github.com/hashicorp/golang-lru"
	"github.com/maypok86/otter"
	gocache "github.com/patrickmn/go-cache"
)

// // ZipfGenerator 实现了 Zipf 分布的生成器
// type ZipfGenerator struct {
// 	zipf      *rand.Zipf
// 	accessCnt uint64
// 	limit     uint64
// }

// // NewZipfGenerator 创建一个新的 Zipf 生成器
// func NewZipfGenerator(s, v float64, imax uint64, limit uint64) *ZipfGenerator {
// 	r := rand.New(rand.NewSource(rand.Int63()))
// 	return &ZipfGenerator{
// 		zipf:  rand.NewZipf(r, s, v, imax),
// 		limit: limit,
// 	}
// }

// // NewZipfGeneratorWithSeed 创建一个使用固定种子的 Zipf 生成器
// func NewZipfGeneratorWithSeed(s, v float64, imax uint64, limit uint64, seed int64) *ZipfGenerator {
// 	r := rand.New(rand.NewSource(seed))
// 	return &ZipfGenerator{
// 		zipf:  rand.NewZipf(r, s, v, imax),
// 		limit: limit,
// 	}
// }

// // Next 返回下一个 Zipf 分布的值
// func (g *ZipfGenerator) Next() (uint64, bool) {
// 	if g.accessCnt >= g.limit {
// 		return 0, false
// 	}
// 	g.accessCnt++
// 	return g.zipf.Uint64(), true
// }

// // Reset 重置生成器状态
// func (g *ZipfGenerator) Reset() {
// 	g.accessCnt = 0
// }

// // HitRateTracker 跟踪缓存命中率
// type HitRateTracker struct {
// 	hits   uint64
// 	misses uint64
// }

// // RecordHit 记录一次缓存命中
// func (t *HitRateTracker) RecordHit() {
// 	t.hits++
// }

// // RecordMiss 记录一次缓存未命中
// func (t *HitRateTracker) RecordMiss() {
// 	t.misses++
// }

// // GetHitRate 获取命中率（百分比）
// func (t *HitRateTracker) GetHitRate() float64 {
// 	total := t.hits + t.misses
// 	if total == 0 {
// 		return 0
// 	}
// 	return float64(t.hits) / float64(total) * 100
// }

// // GetTotalAccess 获取总访问次数
// func (t *HitRateTracker) GetTotalAccess() uint64 {
// 	return t.hits + t.misses
// }

// // Reset 重置统计数据
// func (t *HitRateTracker) Reset() {
// 	t.hits = 0
// 	t.misses = 0
// }

// ZipfGenerator 生成Zipf分布的key（线程安全）
type ZipfGenerator struct {
	zipf      *rand.Zipf
	accessCnt uint64
	limit     uint64
	mu        sync.Mutex // 保护zipf调用
}

func NewZipfGenerator(s, v float64, imax, limit uint64) *ZipfGenerator {
	// 参数校验
	if s <= 1.0 || v <= 0 || imax == 0 {
		panic(fmt.Sprintf("invalid Zipf params: s=%.2f v=%.2f imax=%d", s, v, imax))
	}

	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	zipf := rand.NewZipf(r, s, v, imax)
	if zipf == nil {
		panic("failed to create Zipf distribution")
	}

	return &ZipfGenerator{
		zipf:  zipf,
		limit: limit,
	}
}

// NewZipfGeneratorWithSeed 创建使用固定种子的Zipf生成器
func NewZipfGeneratorWithSeed(s, v float64, imax, limit uint64, seed int64) *ZipfGenerator {
	// 参数校验
	if s <= 1.0 || v <= 0 || imax == 0 {
		panic(fmt.Sprintf("invalid Zipf params: s=%.2f v=%.2f imax=%d", s, v, imax))
	}

	r := rand.New(rand.NewSource(seed))
	zipf := rand.NewZipf(r, s, v, imax)
	if zipf == nil {
		panic("failed to create Zipf distribution")
	}

	return &ZipfGenerator{
		zipf:  zipf,
		limit: limit,
	}
}

func (g *ZipfGenerator) Next() (string, bool) {
	if g == nil || g.zipf == nil {
		return "", false
	}
	if g.accessCnt >= g.limit {
		return "", false
	}

	g.mu.Lock()
	defer g.mu.Unlock()

	g.accessCnt++
	return strconv.FormatUint(g.zipf.Uint64(), 10), true
}

func (g *ZipfGenerator) Reset() {
	g.accessCnt = 0
}

// HitRateTracker 命中率统计（线程安全）
type HitRateTracker struct {
	hits   atomic.Uint64
	misses atomic.Uint64
}

func (t *HitRateTracker) RecordHit()  { t.hits.Add(1) }
func (t *HitRateTracker) RecordMiss() { t.misses.Add(1) }

func (t *HitRateTracker) HitRate() float64 {
	h, m := t.hits.Load(), t.misses.Load()
	if total := h + m; total > 0 {
		return float64(h) / float64(total) * 100
	}
	return 0
}

// CreateHitRateTestClients 创建所有缓存客户端用于命中率测试
func CreateHitRateTestClients(capacity int) []HitRateTestClient {
	clients := make([]HitRateTestClient, 0)

	// 统一约定：capacity = 最大key个数（所有缓存库按此标准）

	// 1. BigCache (带容量控制的包装器)
	bigCacheConfig := bigcache.DefaultConfig(10 * time.Minute)
	bigCacheConfig.HardMaxCacheSize = 512            // 512MB内存上限
	bigCacheConfig.MaxEntriesInWindow = capacity * 2 // 设置为容量的2倍作为缓冲
	bigCacheConfig.MaxEntrySize = 200                // 单个条目最大200字节
	if bc, err := bigcache.New(context.Background(), bigCacheConfig); err == nil {
		clients = append(clients, &CapacityControlledBigCacheClient{
			client:   bc,
			capacity: capacity,
			keys:     make(map[string]bool),
			keyList:  make([]string, 0, capacity),
		})
	}

	// 2. FreeCache (估算合理的内存大小)
	// 考虑key+value+overhead，平均每个条目约150字节
	memorySize := capacity * 150
	if memorySize < 1024*1024 { // 最小1MB
		memorySize = 1024 * 1024
	}
	fc := freecache.NewCache(memorySize)
	clients = append(clients, &FreeCacheHitRateClient{client: fc})

	// 3. Ristretto (按条目数限制)
	if rc, err := ristretto.NewCache(&ristretto.Config{
		NumCounters: int64(capacity) * 10, // 计数器数量=容量x10，提高命中率
		MaxCost:     int64(capacity),      // 最大条目数
		BufferItems: 64,
	}); err == nil {
		clients = append(clients, &RistrettoHitRateClient{client: rc})
	}

	// 4. Golang-LRU (原生支持条目数限制)
	if lc, err := lru.New(capacity); err == nil {
		clients = append(clients, &GolangLRUHitRateClient{client: lc})
	}

	// 5. Go-Cache (带容量控制的包装器)
	gc := gocache.New(5*time.Minute, 10*time.Minute)
	clients = append(clients, &CapacityControlledGoCacheClient{
		client:   gc,
		capacity: capacity,
		keys:     make(map[string]bool),
		keyList:  make([]string, 0, capacity),
	})

	// 6. Otter (原生支持条目数限制)
	oc, _ := otter.MustBuilder[string, string](capacity).Build()
	clients = append(clients, &OtterHitRateClient{client: &oc})

	// 7. FastCache (严格的内存限制)
	// 为了测试容量限制，故意分配较小的内存以触发eviction
	// 极小的内存分配，强制触发频繁eviction
	fcMemorySize := capacity * 16 // 每个entry只有16字节，强制eviction
	if fcMemorySize < 8*1024 {    // 最小8KB
		fcMemorySize = 8 * 1024
	}
	fcCache := fastcache.New(fcMemorySize)
	clients = append(clients, &FastCacheHitRateClient{
		client: fcCache,
	})

	// 8. sync.Map (带容量控制的包装器)
	clients = append(clients, &CapacityControlledSyncMapClient{
		capacity: capacity,
		keys:     make(map[string]bool),
		keyList:  make([]string, 0, capacity),
	})

	return clients
}

// CapacityControlledBigCacheClient BigCache的容量控制包装器
type CapacityControlledBigCacheClient struct {
	client   *bigcache.BigCache
	capacity int
	keys     map[string]bool
	keyList  []string
	mu       sync.RWMutex
	once     sync.Once
}

func (c *CapacityControlledBigCacheClient) Get(key string) (string, bool) {
	_, err := c.client.Get(key)
	if err != nil {
		return "", false
	}
	return key, true
}

func (c *CapacityControlledBigCacheClient) Set(key string, value string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果key已存在，直接更新
	if c.keys[key] {
		_ = c.client.Set(key, []byte(value))
		return
	}

	// 如果达到容量限制，先删除最旧的key (FIFO)
	if len(c.keyList) >= c.capacity && c.capacity > 0 {
		oldestKey := c.keyList[0]
		c.keyList = c.keyList[1:]
		delete(c.keys, oldestKey)
		_ = c.client.Delete(oldestKey)
	}

	// 添加新key
	c.keys[key] = true
	c.keyList = append(c.keyList, key)
	_ = c.client.Set(key, []byte(value))
}

func (c *CapacityControlledBigCacheClient) Name() string {
	return "bigcache"
}

func (c *CapacityControlledBigCacheClient) Close() {
	c.once.Do(func() {
		_ = c.client.Close()
	})
}

// CapacityControlledGoCacheClient GoCache的容量控制包装器
type CapacityControlledGoCacheClient struct {
	client   *gocache.Cache
	capacity int
	keys     map[string]bool
	keyList  []string
	mu       sync.RWMutex
}

func (c *CapacityControlledGoCacheClient) Get(key string) (string, bool) {
	val, found := c.client.Get(key)
	if !found {
		return "", false
	}
	return val.(string), true
}

func (c *CapacityControlledGoCacheClient) Set(key string, value string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果key已存在，直接更新
	if c.keys[key] {
		c.client.Set(key, value, gocache.NoExpiration)
		return
	}

	// 如果达到容量限制，先删除最旧的key (FIFO)
	if len(c.keyList) >= c.capacity && c.capacity > 0 {
		oldestKey := c.keyList[0]
		c.keyList = c.keyList[1:]
		delete(c.keys, oldestKey)
		c.client.Delete(oldestKey)
	}

	// 添加新key
	c.keys[key] = true
	c.keyList = append(c.keyList, key)
	c.client.Set(key, value, gocache.NoExpiration)
}

func (c *CapacityControlledGoCacheClient) Name() string {
	return "go-cache"
}

func (c *CapacityControlledGoCacheClient) Close() {}

// CapacityControlledSyncMapClient sync.Map的容量控制包装器
type CapacityControlledSyncMapClient struct {
	m        sync.Map
	capacity int
	keys     map[string]bool
	keyList  []string
	mu       sync.RWMutex
}

func (c *CapacityControlledSyncMapClient) Get(key string) (string, bool) {
	val, ok := c.m.Load(key)
	if !ok {
		return "", false
	}
	return val.(string), true
}

func (c *CapacityControlledSyncMapClient) Set(key string, value string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果key已存在，直接更新（不需要改变keyList）
	if c.keys[key] {
		c.m.Store(key, value)
		return
	}

	// 如果达到容量限制，先删除最旧的key (FIFO)
	if len(c.keyList) >= c.capacity && c.capacity > 0 {
		oldestKey := c.keyList[0]
		c.keyList = c.keyList[1:]
		delete(c.keys, oldestKey)
		c.m.Delete(oldestKey)
	}

	// 添加新key
	c.keys[key] = true
	c.keyList = append(c.keyList, key)
	c.m.Store(key, value)
}

func (c *CapacityControlledSyncMapClient) Name() string {
	return "sync-map"
}

func (c *CapacityControlledSyncMapClient) Close() {}

// FastCache的客户端实现（移除容量字段，依赖内存限制）
type FastCacheHitRateClient struct {
	client *fastcache.Cache
}

func (c *FastCacheHitRateClient) Get(key string) (string, bool) {
	val := c.client.Get(nil, []byte(key))
	if val == nil {
		return "", false
	}
	return string(val), true
}

func (c *FastCacheHitRateClient) Set(key string, value string) {
	c.client.Set([]byte(key), []byte(value))
}

func (c *FastCacheHitRateClient) Name() string {
	return "fastcache"
}

func (c *FastCacheHitRateClient) Close() {
	// fastcache无需显式关闭
}

// 以下是各个缓存的命中率测试客户端实现（保留原有的其他实现）

type FreeCacheHitRateClient struct {
	client *freecache.Cache
}

func (c *FreeCacheHitRateClient) Get(key string) (string, bool) {
	val, err := c.client.Get([]byte(key))
	if err != nil {
		return "", false
	}
	return string(val), true
}

func (c *FreeCacheHitRateClient) Set(key string, value string) {
	_ = c.client.Set([]byte(key), []byte(value), 0)
}

func (c *FreeCacheHitRateClient) Name() string {
	return "freecache"
}

func (c *FreeCacheHitRateClient) Close() {
	c.client.Clear()
}

type RistrettoHitRateClient struct {
	client *ristretto.Cache
}

func (c *RistrettoHitRateClient) Get(key string) (string, bool) {
	val, found := c.client.Get(key)
	if !found {
		return "", false
	}
	return val.(string), true
}

func (c *RistrettoHitRateClient) Set(key string, value string) {
	c.client.Set(key, value, 1)
}

func (c *RistrettoHitRateClient) Name() string {
	return "ristretto"
}

func (c *RistrettoHitRateClient) Close() {
	c.client.Close()
}

type GolangLRUHitRateClient struct {
	client *lru.Cache
}

func (c *GolangLRUHitRateClient) Get(key string) (string, bool) {
	val, found := c.client.Get(key)
	if !found {
		return "", false
	}
	return val.(string), true
}

func (c *GolangLRUHitRateClient) Set(key string, value string) {
	c.client.Add(key, value)
}

func (c *GolangLRUHitRateClient) Name() string {
	return "golang-lru"
}

func (c *GolangLRUHitRateClient) Close() {}

type OtterHitRateClient struct {
	client *otter.Cache[string, string]
}

func (c *OtterHitRateClient) Get(key string) (string, bool) {
	return c.client.Get(key)
}

func (c *OtterHitRateClient) Set(key string, value string) {
	c.client.Set(key, value)
}

func (c *OtterHitRateClient) Name() string {
	return "otter"
}

func (c *OtterHitRateClient) Close() {
	c.client.Close()
}
