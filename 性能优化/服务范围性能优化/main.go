package main

import (
	"fmt"
	"runtime"
	"runtime/debug"
	"time"
)

const (
	// Define a fixed number of queries for our custom benchmark
	numQueries = 1_000_000
)

func main() {
	fmt.Println("--- Custom Benchmark Suite ---")

	// --- 1. Data Generation ---
	fmt.Println("\nGenerating decoupled data source... (This should be fast)")
	startTime := time.Now()
	decoupledData := generateDecoupledData()
	fmt.Printf("Data generation complete. Took: %v\n", time.Since(startTime))

	// We still need some queries to use for testing
	testQueries := generateQueries(10000)

	// --- 2. Run Benchmarks for each implementation ---

	// Test NativeBitVector (raw bit vector, branchless query)
	runBenchmarkFor("NativeBitVector", func() interface{} {
		return NewNativeBitVector(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*NativeBitVector).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test DecoupledSetsRoaring (native model, should be fast)
	runBenchmarkFor("DecoupledSetsRoaring", func() interface{} {
		return NewDecoupledSetsRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*DecoupledSetsRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test InvertedIndexMap (logically same as Decoupled, different data layout with Go maps)
	runBenchmarkFor("InvertedIndexMap", func() interface{} {
		return NewInvertedIndexMap(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*InvertedIndexMap).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test PartitionedBitmapRoaring (advanced layout optimization)
	runBenchmarkFor("PartitionedBitmapRoaring", func() interface{} {
		return NewPartitionedBitmapRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*PartitionedBitmapRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test NestedMapRoaring (will be slow due to internal Cartesian product)
	runBenchmarkFor("NestedMapRoaring", func() interface{} {
		return NewNestedMapRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*NestedMapRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test FlattenIndexRoaring (will be slow due to internal Cartesian product)
	runBenchmarkFor("FlattenIndexRoaring", func() interface{} {
		return NewFlattenIndexRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*FlattenIndexRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	fmt.Println("\n--- Benchmark Suite Complete ---")
}

// runBenchmarkFor is a helper function to test a given implementation
func runBenchmarkFor(
	name string,
	buildFunc func() interface{},
	queryFunc func(instance interface{}, tpl, origin, dest uint32) bool,
	queries []ServicePair,
) {
	fmt.Printf("\n--- Testing: %s ---\n", name)

	// --- Measure Build Time & Memory ---
	runtime.GC() // Clean up before measurement
	var memBefore, memAfter runtime.MemStats
	runtime.ReadMemStats(&memBefore)

	buildStartTime := time.Now()
	instance := buildFunc()
	buildDuration := time.Since(buildStartTime)

	runtime.GC() // Clean up after build to get accurate memory reading
	runtime.ReadMemStats(&memAfter)

	// Check for underflow before calculating. Use HeapAlloc for more accurate measurement of data structures.
	var memUsed float64
	if memAfter.HeapAlloc > memBefore.HeapAlloc {
		memUsed = float64(memAfter.HeapAlloc-memBefore.HeapAlloc) / 1024 / 1024
	} else {
		memUsed = 0 // Measurement was unstable, report 0
	}

	fmt.Printf("Build Time: %v\n", buildDuration)
	fmt.Printf("Memory Used by Structure: %.2f MB\n", memUsed)

	// --- Measure Query Time ---
	queryStartTime := time.Now()
	for i := 0; i < numQueries; i++ {
		q := queries[i%len(queries)]
		_ = queryFunc(instance, q.TplID, q.OriginID, q.DestID)
	}
	queryDuration := time.Since(queryStartTime)
	avgQueryTime := float64(queryDuration.Nanoseconds()) / float64(numQueries)

	fmt.Printf("Query Time (%d queries): %v\n", numQueries, queryDuration)
	fmt.Printf("Average Query Time: %.2f ns/op\n", avgQueryTime)

	// Aggressively clean up memory before the next test
	instance = nil
	debug.FreeOSMemory()
}
