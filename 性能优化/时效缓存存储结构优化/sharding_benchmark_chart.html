
<!DOCTYPE html>
<html>
<head>
    <title>Sharding Benchmark Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div style="width: 80%; margin: auto;">
        <canvas id="benchmarkChart"></canvas>
    </div>
    <script>
        const labels = [50, 100, 150, 200, 250, 300, 350, 400];
        const memoryData = [82.8, 78.9, 77.0, 77.1, 76.1, 76.2, 148.8, 143.4];
        const keyCountData = [19200, 9700, 6500, 4900, 4000, 3200, 2900, 2700];
        const readOpsData = [320589, 230968, 199626, 162401, 156995, 127624, 253492, 253889];

        const data = {
            labels: labels,
            datasets: [
                {
                    label: 'Memory Usage (MB)',
                    data: memoryData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                    yAxisID: 'y-memory',
                },
                {
                    label: 'Key Count',
                    data: keyCountData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    yAxisID: 'y-ops-keys',
                },
                {
                    label: 'Read OPS',
                    data: readOpsData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.5)',
                    yAxisID: 'y-ops-keys',
                }
            ]
        };

        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                stacked: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Sharding Count vs. Performance & Memory'
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Shard Count'
                        }
                    },
                    'y-memory': {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Memory Usage (MB)'
                        }
                    },
                    'y-ops-keys': {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Key Count / Read OPS'
                        },
                        grid: {
                            drawOnChartArea: false, // only draw grid for left axis
                        },
                    },
                }
            },
        };

        new Chart(
            document.getElementById('benchmarkChart'),
            config
        );
    </script>
</body>
</html>
