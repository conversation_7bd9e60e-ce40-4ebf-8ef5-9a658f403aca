package main

import (
	"math/rand"
	"time"
)

const (
	Num3PLs       = 10
	NumLocations  = 10000
	OriginDensity = 0.9 // 90% of origins are serviceable
	DestDensity   = 0.9 // 90% of destinations are serviceable for a given origin

	// Item Card page constants
	NumItemsPerPage = 25 // Number of items shown on one Item Card page
	Num3PLsPerItem  = 5  // Number of 3PLs available for each item
)

// TplCoverage holds the decoupled sets of serviceable origins and destinations for a single TPL.
// This reflects the new understanding of the "real world" data model.
type TplCoverage struct {
	Origins []uint32
	Dests   []uint32
}

// generateDecoupledData is the single source of truth for data generation.
// It creates a map of TPL IDs to their respective coverage sets.
func generateDecoupledData() map[uint32]TplCoverage {
	rand.Seed(time.Now().UnixNano())

	data := make(map[uint32]TplCoverage, Num3PLs)

	numServiceableOrigins := int(float64(NumLocations) * OriginDensity)
	numServiceableDests := int(float64(NumLocations) * DestDensity)

	// Create a template of all possible locations to sample from
	allLocationIDs := make([]uint32, NumLocations)
	for i := 0; i < NumLocations; i++ {
		allLocationIDs[i] = uint32(i)
	}

	for tplID := uint32(0); tplID < Num3PLs; tplID++ {
		// Randomly select 90% of origins for this 3PL
		rand.Shuffle(len(allLocationIDs), func(i, j int) {
			allLocationIDs[i], allLocationIDs[j] = allLocationIDs[j], allLocationIDs[i]
		})
		serviceableOrigins := make([]uint32, numServiceableOrigins)
		copy(serviceableOrigins, allLocationIDs[:numServiceableOrigins])

		// Randomly select 90% of destinations for this 3PL
		rand.Shuffle(len(allLocationIDs), func(i, j int) {
			allLocationIDs[i], allLocationIDs[j] = allLocationIDs[j], allLocationIDs[i]
		})
		serviceableDests := make([]uint32, numServiceableDests)
		copy(serviceableDests, allLocationIDs[:numServiceableDests])

		data[tplID] = TplCoverage{
			Origins: serviceableOrigins,
			Dests:   serviceableDests,
		}
	}

	return data
}

// ServicePair represents a single serviceability query
type ServicePair struct {
	TplID    uint32
	OriginID uint32
	DestID   uint32
}

// generateQueries generates individual queries for testing
func generateQueries(num int) []ServicePair {
	rand.Seed(time.Now().UnixNano())
	queries := make([]ServicePair, num)
	for i := 0; i < num; i++ {
		queries[i] = ServicePair{
			TplID:    rand.Uint32() % Num3PLs,
			OriginID: rand.Uint32() % NumLocations,
			DestID:   rand.Uint32() % NumLocations,
		}
	}
	return queries
}

// generateBatchQueries generates batch queries simulating Item Card page scenarios
// Each batch has the same DestID (buyer) but different OriginIDs (sellers) and TPLs
func generateBatchQueries(numBatches int) [][]ServicePair {
	rand.Seed(time.Now().UnixNano())
	batches := make([][]ServicePair, numBatches)

	for i := 0; i < numBatches; i++ {
		// Same buyer for all queries in this batch
		buyerID := rand.Uint32() % NumLocations

		// Generate queries for this batch (25 items * 5 TPLs each = 125 queries)
		batchSize := NumItemsPerPage * Num3PLsPerItem
		batch := make([]ServicePair, batchSize)

		queryIdx := 0
		for j := 0; j < NumItemsPerPage; j++ {
			// Different seller for each item
			sellerID := rand.Uint32() % NumLocations

			// Random 5 TPLs for this item
			selectedTpls := rand.Perm(int(Num3PLs))[:Num3PLsPerItem]
			for _, tplIdx := range selectedTpls {
				batch[queryIdx] = ServicePair{
					TplID:    uint32(tplIdx),
					OriginID: sellerID,
					DestID:   buyerID,
				}
				queryIdx++
			}
		}

		batches[i] = batch
	}

	return batches
}
