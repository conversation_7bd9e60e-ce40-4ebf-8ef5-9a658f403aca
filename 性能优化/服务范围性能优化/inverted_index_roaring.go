package main

// InvertedIndexMap implements the inverted index strategy using Go native maps.
// It maps each location to a map of TPLs that service it.
type InvertedIndexMap struct {
	originToTpls map[uint32]map[uint32]bool
	destToTpls   map[uint32]map[uint32]bool
}

// NewInvertedIndexMap builds the inverted index from the decoupled data source.
func NewInvertedIndexMap(data map[uint32]TplCoverage) *InvertedIndexMap {
	i := &InvertedIndexMap{
		originToTpls: make(map[uint32]map[uint32]bool),
		destToTpls:   make(map[uint32]map[uint32]bool),
	}

	// Iterate through each TPL and its coverage
	for tplID, coverage := range data {
		// Populate the origin-to-TPLs map
		for _, originID := range coverage.Origins {
			if _, ok := i.originToTpls[originID]; !ok {
				i.originToTpls[originID] = make(map[uint32]bool)
			}
			i.originToTpls[originID][tplID] = true
		}

		// Populate the dest-to-TPLs map
		for _, destID := range coverage.Dests {
			if _, ok := i.destToTpls[destID]; !ok {
				i.destToTpls[destID] = make(map[uint32]bool)
			}
			i.destToTpls[destID][tplID] = true
		}
	}
	return i
}

// IsServiceable checks for serviceability using the inverted index.
func (i *InvertedIndexMap) IsServiceable(tpl, origin, dest uint32) bool {
	originTpls, ok := i.originToTpls[origin]
	if !ok || !originTpls[tpl] {
		return false
	}

	destTpls, ok := i.destToTpls[dest]
	if !ok || !destTpls[tpl] {
		return false
	}

	return true
}
