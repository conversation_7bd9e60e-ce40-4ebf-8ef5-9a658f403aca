# Go语言压测工具指南

## 🎯 压测工具概览

Go语言生态中有丰富的压测工具，从内置的基准测试到专业的压测平台，可以满足不同场景的需求。

### 主要特点和应用场景

- **性能基准测试**
  - 适用于函数级性能对比、算法优化、内存分析
  - 工具：Go testing包（内置）
  ```go
  func BenchmarkStringConcat(b *testing.B) {
      for i := 0; i < b.N; i++ {
          _ = "hello" + "world"
      }
  }

  // 运行基准测试
  go test -bench=. -benchmem
  // 运行特定测试
  go test -bench=BenchmarkStringConcat -benchtime=10s
  // 生成CPU分析文件
  go test -bench=. -cpuprofile=cpu.prof
  ```

- **HTTP/API压测**
  - 适用于REST API测试、负载容量评估、性能验证
  - 常用命令示例：
  ```bash
  # 使用hey进行快速API测试
  hey -n 1000 -c 10 http://localhost:8080/api
  # 带认证的API测试
  hey -n 2000 -c 50 -H "Authorization: Bearer token" http://localhost:8080/api
  # 压测POST请求
  hey -n 100 -c 10 -m POST -d '{"key":"value"}' http://localhost:8080/api

  # 使用vegeta进行持续负载测试
  # 简单GET请求
  echo "GET http://localhost:8080/api" | vegeta attack -duration=30s -rate=100 | vegeta report
  # 自定义请求（targets.txt）
  cat > targets.txt << EOF
  POST http://localhost:8080/api
  Content-Type: application/json
  @/path/to/payload.json
  EOF
  vegeta attack -targets=targets.txt -duration=5m -rate=50 | tee results.bin | vegeta report
  # 生成图形报告
  vegeta plot results.bin > plot.html
  ```

- **微服务和gRPC测试**
  - 适用于服务网格、微服务架构、gRPC接口测试
  ```bash
  # 使用Fortio进行gRPC压测
  fortio load -grpc -c 4 -qps 50 localhost:8080
  # HTTP/REST测试
  fortio load -c 8 -qps 100 -t 60s http://localhost:8080/api
  # 生成详细报告
  fortio report data.json
  ```

- **复杂业务场景**
  - 适用于端到端测试、用户行为模拟、业务流程验证
  
  **k6示例:**
  ```javascript
  // k6测试脚本示例 (test.js)
  import http from 'k6/http';
  import { check, sleep } from 'k6';

  export let options = {
    stages: [
      { duration: '30s', target: 20 }, // 逐步增加到20个用户
      { duration: '1m30s', target: 20 }, // 保持20个用户
      { duration: '30s', target: 0 }, // 逐步减少到0
    ],
  };

  export default function() {
    let res = http.get('http://localhost:8080/api');
    check(res, { 'status is 200': (r) => r.status === 200 });
    sleep(1);
  }

  // 运行测试
  k6 run test.js
  // 带性能阈值的测试
  k6 run --vus 10 --duration 30s test.js
  ```

  **JMeter使用示例:**
  ```bash
  # 启动JMeter GUI（首次使用需要创建测试计划）
  jmeter

  # 命令行模式运行测试
  jmeter -n -t test-plan.jmx -l results.jtl

  # 生成HTML报告
  jmeter -n -t test-plan.jmx -l results.jtl -e -o ./report

  # 分布式测试（多机器压测）
  jmeter -n -t test-plan.jmx -R host1,host2 -l results.jtl

  # 通过配置文件运行
  jmeter -n -t test-plan.jmx -p config.properties -l results.jtl

  # 实时监控（使用Backend Listener）
  jmeter -n -t test-plan.jmx -l results.jtl -Jbackend_url=http://grafana:3000
  ```

### 选型建议

| 场景 | 推荐工具 | 适用情况 |
|------|---------|----------|
| 代码优化 | testing | 函数级性能测试、基准对比 |
| API性能 | Vegeta/hey | HTTP接口压测、负载测试 |
| 微服务 | Fortio | gRPC、服务网格测试 |
| 业务流程 | k6/JMeter | 复杂场景、端到端测试 |
| 开发调试 | hey | 快速验证、简单压测 |

**核心建议**: 从简单工具开始，根据需求复杂度逐步升级工具链。根据实际场景选择合适的工具，不必拘泥于单一方案。