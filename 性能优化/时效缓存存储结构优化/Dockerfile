# 使用官方Go镜像作为基础镜像
FROM golang:1.21-alpine AS base

# 设置工作目录
WORKDIR /workspace

# 安装必要工具
RUN apk add --no-cache \
    git \
    redis \
    bash \
    curl \
    make \
    jq

# 设置Go环境变量
ENV GO111MODULE=on \
    GOPROXY=https://goproxy.cn,direct \
    GOSUMDB=off

# 复制go.mod和go.sum
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 开发环境镜像
FROM base AS dev

# 安装开发工具
RUN go install -a github.com/cweill/gotests/gotests@latest && \
    go install -a github.com/fatih/gomodifytags@latest && \
    go install -a github.com/josharian/impl@latest && \
    go install -a github.com/haya14busa/goplay/cmd/goplay@latest && \
    go install -a github.com/go-delve/delve/cmd/dlv@latest && \
    go install -a honnef.co/go/tools/cmd/staticcheck@latest && \
    go install -a golang.org/x/tools/gopls@latest

# 复制项目文件
COPY . .

# 设置入口点
CMD ["bash"]

# 生产环境镜像
FROM base AS prod

# 复制项目文件
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /app/redis-benchmark ./benchmarks/redis_benchmark.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /app/memory-analyzer ./benchmarks/memory_analyzer.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /app/migration-tool ./migration/migration_tool.go

# 最终运行镜像
FROM alpine:latest AS runtime

RUN apk --no-cache add ca-certificates redis bash

WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=prod /app/ .
COPY --from=prod /workspace/run_demo.sh .

# 设置执行权限
RUN chmod +x run_demo.sh

# 默认命令
CMD ["./run_demo.sh"]
