package data

import (
	"testing"
	"time"

	"github.com/Pallinder/go-randomdata"
	"github.com/brianvoe/gofakeit/v6"
	bxfaker "github.com/bxcodec/faker/v4"
	"github.com/corpix/uarand"
	gofaker "github.com/go-faker/faker/v4"
	"github.com/icrowley/fake"
	jaswdrfaker "github.com/jaswdr/faker"
	"github.com/stretchr/testify/assert"
)

type Person struct {
	Name      string
	Email     string
	Phone     string
	Address   Address
	Company   string
	Age       int
	CreatedAt string
}

type Address struct {
	Street  string
	City    string
	State   string
	Zip     string
	Country string
}

// Struct for bxcodec/faker testing
type User struct {
	ID         int    `faker:"boundary_start=1, boundary_end=1000"`
	Username   string `faker:"username"`
	Email      string `faker:"email"`
	FirstName  string `faker:"first_name"`
	LastName   string `faker:"last_name"`
	Phone      string `faker:"phone_number"`
	Password   string `faker:"password"`
	CreditCard string `faker:"cc_number"`
	IPv4       string `faker:"ipv4"`
	IPv6       string `faker:"ipv6"`
	Latitude   string `faker:"lat"`
	Longitude  string `faker:"long"`
	UnixTime   int64  `faker:"unix_time"`
	Date       string `faker:"date"`
	Time       string `faker:"time"`
	MonthName  string `faker:"month_name"`
	Year       string `faker:"year"`
	DayOfWeek  string `faker:"day_of_week"`
	Timezone   string `faker:"timezone"`
	Word       string `faker:"word"`
	Sentence   string `faker:"sentence"`
	Paragraph  string `faker:"paragraph"`
}

func TestFakerStruct(t *testing.T) {
	gofakeit.Seed(42)

	t.Run("test struct generation", func(t *testing.T) {
		person := Person{
			Name:    gofakeit.Name(),
			Email:   gofakeit.Email(),
			Phone:   gofakeit.Phone(),
			Company: gofakeit.Company(),
			Age:     gofakeit.Number(18, 80),
			Address: Address{
				Street:  gofakeit.Street(),
				City:    gofakeit.City(),
				State:   gofakeit.State(),
				Zip:     gofakeit.Zip(),
				Country: gofakeit.Country(),
			},
			CreatedAt: gofakeit.Date().Format("2006-01-02 15:04:05"),
		}

		// Assertions
		assert.NotEmpty(t, person.Name)
		assert.Contains(t, person.Email, "@")
		assert.NotEmpty(t, person.Phone)
		assert.NotEmpty(t, person.Company)
		assert.NotEmpty(t, person.Address.Street)
		assert.NotEmpty(t, person.Address.City)
		assert.GreaterOrEqual(t, person.Age, 18)
		assert.LessOrEqual(t, person.Age, 80)

		t.Logf("Generated Person: %+v", person)
	})
}

func TestFakerCustomData(t *testing.T) {
	gofakeit.Seed(42)

	t.Run("test custom data generation", func(t *testing.T) {
		// Generate data from custom sets
		fruit := gofakeit.RandomString([]string{"apple", "banana", "orange", "grape"})
		car := gofakeit.Car()
		color := gofakeit.Color()
		date := gofakeit.Date()
		ipv4 := gofakeit.IPv4Address()
		password := gofakeit.Password(true, true, true, true, false, 10)

		// Assertions
		assert.Contains(t, []string{"apple", "banana", "orange", "grape"}, fruit)
		assert.NotEmpty(t, car.Brand)
		assert.NotEmpty(t, color)
		assert.NotNil(t, date)
		assert.NotEmpty(t, ipv4)
		assert.Len(t, password, 10)

		t.Logf("Generated custom data:\nFruit: %s\nCar: %+v\nColor: %s\nDate: %v\nIPv4: %s\nPassword: %s",
			fruit, car, color, date, ipv4, password)
	})
}

// Test bxcodec/faker library
func TestBxcodecFaker(t *testing.T) {
	t.Run("test bxcodec faker with struct tags", func(t *testing.T) {
		var user User
		err := bxfaker.FakeData(&user)
		assert.NoError(t, err)

		// Assertions
		assert.NotEmpty(t, user.Username)
		assert.Contains(t, user.Email, "@")
		assert.NotEmpty(t, user.FirstName)
		assert.NotEmpty(t, user.LastName)
		assert.NotEmpty(t, user.Phone)
		assert.NotEmpty(t, user.CreditCard)
		assert.NotEmpty(t, user.IPv4)
		assert.NotEmpty(t, user.Word)
		assert.Greater(t, user.ID, 0)
		assert.LessOrEqual(t, user.ID, 1000)

		t.Logf("Generated User with bxcodec/faker: %+v", user)
	})

	t.Run("test bxcodec faker individual functions", func(t *testing.T) {
		// Individual faker functions
		username := bxfaker.Username()
		email := bxfaker.Email()
		word := bxfaker.Word()
		sentence := bxfaker.Sentence()

		assert.NotEmpty(t, username)
		assert.Contains(t, email, "@")
		assert.NotEmpty(t, word)
		assert.NotEmpty(t, sentence)

		t.Logf("Individual bxcodec faker data:\nUsername: %s\nEmail: %s\nWord: %s\nSentence: %s",
			username, email, word, sentence)
	})
}

// Test icrowley/fake library
func TestIcrowleyFake(t *testing.T) {
	t.Run("test icrowley fake library", func(t *testing.T) {
		// Basic data generation
		name := fake.FullName()
		email := fake.EmailAddress()
		phone := fake.Phone()
		company := fake.Company()
		address := fake.StreetAddress()
		city := fake.City()
		state := fake.State()
		zip := fake.Zip()

		// Internet related
		domainName := fake.DomainName()
		ipv4 := fake.IPv4()
		userAgent := fake.UserAgent()

		// Text generation
		word := fake.Word()
		sentence := fake.Sentence()
		paragraph := fake.Paragraph()

		// Assertions
		assert.NotEmpty(t, name)
		assert.NotEmpty(t, email)
		assert.NotEmpty(t, phone)
		assert.NotEmpty(t, company)
		assert.NotEmpty(t, address)
		assert.NotEmpty(t, city)
		assert.NotEmpty(t, state)
		assert.NotEmpty(t, zip)
		assert.NotEmpty(t, domainName)
		assert.NotEmpty(t, ipv4)
		assert.NotEmpty(t, userAgent)
		assert.NotEmpty(t, word)
		assert.NotEmpty(t, sentence)
		assert.NotEmpty(t, paragraph)

		t.Logf("icrowley/fake generated data:\nName: %s\nEmail: %s\nPhone: %s\nCompany: %s\nAddress: %s\nCity: %s\nState: %s\nZip: %s\nDomain: %s\nIPv4: %s\nUserAgent: %s\nWord: %s\nSentence: %s\nParagraph: %s",
			name, email, phone, company, address, city, state, zip, domainName, ipv4, userAgent, word, sentence, paragraph)
	})
}

// Test Pallinder/go-randomdata library
func TestRandomData(t *testing.T) {
	t.Run("test go-randomdata library", func(t *testing.T) {
		// Names and personal info
		firstName := randomdata.FirstName(randomdata.RandomGender)
		lastName := randomdata.LastName()
		fullName := randomdata.FullName(randomdata.RandomGender)
		email := randomdata.Email()

		// Location data
		city := randomdata.City()
		state := randomdata.State(randomdata.Large)
		country := randomdata.Country(randomdata.FullCountry)
		postalCode := randomdata.PostalCode("US")

		// Numbers and dates
		number := randomdata.Number(1, 1000)
		decimal := randomdata.Decimal(1, 10, 2)

		// Boolean and choices
		boolean := randomdata.Boolean()

		// Paragraphs and sentences
		paragraph := randomdata.Paragraph()

		// Phone numbers
		phoneNumber := randomdata.PhoneNumber()

		// Assertions
		assert.NotEmpty(t, firstName)
		assert.NotEmpty(t, lastName)
		assert.NotEmpty(t, fullName)
		assert.Contains(t, email, "@")
		assert.NotEmpty(t, city)
		assert.NotEmpty(t, state)
		assert.NotEmpty(t, country)
		assert.NotEmpty(t, postalCode)
		assert.GreaterOrEqual(t, number, 1)
		assert.LessOrEqual(t, number, 1000)
		assert.Greater(t, decimal, 1.0)
		assert.NotEmpty(t, paragraph)
		assert.NotEmpty(t, phoneNumber)

		t.Logf("go-randomdata generated:\nFirstName: %s\nLastName: %s\nFullName: %s\nEmail: %s\nCity: %s\nState: %s\nCountry: %s\nPostalCode: %s\nNumber: %d\nDecimal: %.2f\nBoolean: %t\nParagraph: %s\nPhone: %s",
			firstName, lastName, fullName, email, city, state, country, postalCode, number, decimal, boolean, paragraph, phoneNumber)
	})
}

// Test go-faker/faker library
func TestGoFaker(t *testing.T) {
	t.Run("test go-faker library", func(t *testing.T) {
		// Basic personal data
		name := gofaker.Name()
		email := gofaker.Email()
		username := gofaker.Username()

		// Address data
		address := gofaker.GetRealAddress()

		// Payment data
		creditCard := gofaker.CCNumber()

		// Internet data
		domainName := gofaker.DomainName()
		ipv4 := gofaker.IPv4()

		// Text data
		word := gofaker.Word()
		sentence := gofaker.Sentence()

		// Assertions
		assert.NotEmpty(t, name)
		assert.Contains(t, email, "@")
		assert.NotEmpty(t, username)
		assert.NotEmpty(t, address.Address)
		assert.NotEmpty(t, creditCard)
		assert.NotEmpty(t, domainName)
		assert.NotEmpty(t, ipv4)
		assert.NotEmpty(t, word)
		assert.NotEmpty(t, sentence)

		t.Logf("go-faker generated:\nName: %s\nEmail: %s\nUsername: %s\nAddress: %+v\nCreditCard: %s\nDomain: %s\nIPv4: %s\nWord: %s\nSentence: %s",
			name, email, username, address, creditCard, domainName, ipv4, word, sentence)
	})
}

// Test jaswdr/faker library
func TestJaswdrFaker(t *testing.T) {
	t.Run("test jaswdr faker library", func(t *testing.T) {
		fake := jaswdrfaker.New()

		// Person data
		person := fake.Person()
		name := person.Name()
		firstName := person.FirstName()
		lastName := person.LastName()

		// Contact data
		email := fake.Internet().Email()
		phone := person.Contact().Phone

		// Address data
		address := fake.Address()
		street := address.StreetName()
		city := address.City()
		country := address.Country()

		// Company data
		company := fake.Company().Name()

		// Time data
		dateTime := fake.Time().Time(time.Now())

		// Lorem data
		word := fake.Lorem().Word()
		sentence := fake.Lorem().Sentence(10)

		// Internet data
		ipv4 := fake.Internet().Ipv4()
		domain := fake.Internet().Domain()

		// Assertions
		assert.NotEmpty(t, name)
		assert.NotEmpty(t, firstName)
		assert.NotEmpty(t, lastName)
		assert.Contains(t, email, "@")
		assert.NotEmpty(t, phone)
		assert.NotEmpty(t, street)
		assert.NotEmpty(t, city)
		assert.NotEmpty(t, country)
		assert.NotEmpty(t, company)
		assert.NotNil(t, dateTime)
		assert.NotEmpty(t, word)
		assert.NotEmpty(t, sentence)
		assert.NotEmpty(t, ipv4)
		assert.NotEmpty(t, domain)

		t.Logf("jaswdr/faker generated:\nName: %s\nFirstName: %s\nLastName: %s\nEmail: %s\nPhone: %s\nStreet: %s\nCity: %s\nCountry: %s\nCompany: %s\nDateTime: %s\nWord: %s\nSentence: %s\nIPv4: %s\nDomain: %s",
			name, firstName, lastName, email, phone, street, city, country, company, dateTime.Format("2006-01-02 15:04:05"), word, sentence, ipv4, domain)
	})
}

// Test corpix/uarand library for user agents
func TestUarand(t *testing.T) {
	t.Run("test uarand user agent generation", func(t *testing.T) {
		// Generate random user agents
		randomUA := uarand.GetRandom()

		// The uarand library only provides GetRandom() without parameters
		// for specific browser types, we just generate multiple random ones
		randomUA2 := uarand.GetRandom()
		randomUA3 := uarand.GetRandom()
		randomUA4 := uarand.GetRandom()

		// Assertions
		assert.NotEmpty(t, randomUA)
		assert.NotEmpty(t, randomUA2)
		assert.NotEmpty(t, randomUA3)
		assert.NotEmpty(t, randomUA4)

		t.Logf("uarand generated user agents:\nRandom1: %s\nRandom2: %s\nRandom3: %s\nRandom4: %s",
			randomUA, randomUA2, randomUA3, randomUA4)
	})
}
