package main

import (
	"fmt"
	"testing"
)

func TestBatchRequestGeneration(t *testing.T) {
	// Generate a small batch request for testing
	requests := generateBatchRequests(2)
	
	if len(requests) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 requests, got %d", len(requests))
	}
	
	for i, req := range requests {
		t.Logf("Request %d: BuyerID=%d", i, req.BuyerID)
		
		if len(req.Items) != NumItemsPerPage {
			t.<PERSON>("Expected %d items per request, got %d", NumItemsPerPage, len(req.Items))
		}
		
		for j, item := range req.Items {
			if len(item.TplIDs) != Num3PLsPerItem {
				t.<PERSON><PERSON>("Expected %d TPLs per item, got %d", Num3PLsPerItem, len(item.TplIDs))
			}
			
			// Log first few items for verification
			if j < 3 {
				t.Logf("  Item %d: ItemID=%d, SellerID=%d, TPLs=%v", 
					j, item.ItemID, item.SellerID, item.TplIDs)
			}
		}
	}
}

func TestBatchProcessing(t *testing.T) {
	// Initialize data
	data := generateDecoupledData()
	nativeBitVector := NewNativeBitVector(data)
	invertedIndexMap := NewInvertedIndexMap(data)
	
	// Generate a test batch request
	requests := generateBatchRequests(1)
	req := requests[0]
	
	// Process with both implementations
	results1 := processBatchRequestBench(nativeBitVector, req)
	results2 := processBatchRequestMapBench(invertedIndexMap, req)
	
	// Verify results are the same
	if len(results1) != len(results2) {
		t.Errorf("Results length mismatch: %d vs %d", len(results1), len(results2))
	}
	
	for i := range results1 {
		if len(results1[i]) != len(results2[i]) {
			t.Errorf("Item %d results length mismatch: %d vs %d", i, len(results1[i]), len(results2[i]))
		}
		
		for j := range results1[i] {
			if results1[i][j] != results2[i][j] {
				t.Errorf("Item %d, TPL %d result mismatch: %v vs %v", i, j, results1[i][j], results2[i][j])
			}
		}
	}
	
	t.Logf("Batch request processed successfully:")
	t.Logf("  BuyerID: %d", req.BuyerID)
	t.Logf("  Items: %d", len(req.Items))
	t.Logf("  Total queries: %d", len(req.Items) * Num3PLsPerItem)
}

func ExampleBatchRequest() {
	// Generate sample data
	data := generateDecoupledData()
	nativeBitVector := NewNativeBitVector(data)
	
	// Generate a batch request
	requests := generateBatchRequests(1)
	req := requests[0]
	
	fmt.Printf("Item Card Page Request:\n")
	fmt.Printf("Buyer Location: %d\n", req.BuyerID)
	fmt.Printf("Items on page: %d\n", len(req.Items))
	
	// Process the batch request
	results := processBatchRequestBench(nativeBitVector, req)
	
	// Show results for first few items
	for i := 0; i < 3 && i < len(req.Items); i++ {
		item := req.Items[i]
		fmt.Printf("\nItem %d (Seller: %d):\n", item.ItemID, item.SellerID)
		for j, tplID := range item.TplIDs {
			serviceable := results[i][j]
			status := "❌"
			if serviceable {
				status = "✅"
			}
			fmt.Printf("  3PL %d: %s\n", tplID, status)
		}
	}
	
	// Output:
	// Item Card Page Request:
	// Buyer Location: [some number]
	// Items on page: 25
	//
	// Item [some number] (Seller: [some number]):
	//   3PL [some number]: ✅
	//   3PL [some number]: ❌
	//   ...
}
