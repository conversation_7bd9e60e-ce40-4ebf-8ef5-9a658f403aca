package main

import (
	"fmt"
	"redis-key-optimization/encoders"
	"strings"
	"time"
)

func main() {
	fmt.Println("HashMap存储结构优化示例")
	fmt.Println(strings.Repeat("=", 70))

	// 创建测试数据（模拟300亿真实分布场景）
	keyData := &encoders.KeyData{
		ShopID:      123456789,                                    // 店铺ID (100万个不同店铺中的一个)
		SellerLoc:   12345,                                        // 卖家位置 (5万个不同地址中的一个)
		BuyerLoc:    23456,                                        // 买家位置 (5万个不同地址中的一个)
		ChannelID:   80025,                                        // 渠道ID (100个不同渠道中的一个)
		Date:        time.Date(2025, 1, 17, 0, 0, 0, 0, time.UTC), // 日期
		WindowStart: 0,                                            // 窗口开始（会被覆盖）
		WindowSize:  4,                                            // 固定4小时窗口
	}

	// 6个固定时间窗口 (每天24小时分成6个4小时段)
	timeWindows := []struct {
		start, size uint8
		desc        string
	}{
		{0, 4, "0点-4点"},
		{4, 4, "4点-8点"},
		{8, 4, "8点-12点"},
		{12, 4, "12点-16点"},
		{16, 4, "16点-20点"},
		{20, 4, "20点-24点"},
	}

	// 模拟值数据 (小值场景，min,max都小于10)
	valueData := []struct {
		min, max uint32
		desc     string
	}{
		{1, 2, "窗口1数据"},
		{3, 5, "窗口2数据"},
		{2, 4, "窗口3数据"},
		{6, 8, "窗口4数据"},
		{4, 9, "窗口5数据"},
		{1, 3, "窗口6数据"},
	}

	fmt.Printf("原始数据场景 (模拟300亿真实分布):\n")
	fmt.Printf("  店铺ID: %d (100万个不同店铺)\n", keyData.ShopID)
	fmt.Printf("  位置: %d_%d (5万个不同地址)\n", keyData.SellerLoc, keyData.BuyerLoc)
	fmt.Printf("  渠道: %d (100个不同渠道)\n", keyData.ChannelID)
	fmt.Printf("  日期: %s (365天范围)\n", keyData.Date.Format("20060102"))
	fmt.Printf("  时间窗口: 6个固定4小时段\n")

	fmt.Printf("\n" + strings.Repeat("=", 70) + "\n")
	fmt.Println("HashMap优化方案对比:")
	fmt.Println(strings.Repeat("-", 70))

	// 获取所有HashMap优化器
	optimizers := encoders.GetAllHashMapOptimizers()

	for _, optimizer := range optimizers {
		fmt.Printf("\n【%s】\n", optimizer.GetName())
		fmt.Printf("描述: %s\n", optimizer.GetDescription())

		// 优化Key
		optimizedKey := optimizer.OptimizeKey(keyData)
		fmt.Printf("Key: %s (长度: %d)\n", optimizedKey, len(optimizedKey))

		// 显示所有Field和Value
		fmt.Printf("Fields:\n")
		totalFieldLength := 0
		totalValueLength := 0

		for i, window := range timeWindows {
			optimizedField := optimizer.OptimizeField(window.start, window.size)
			optimizedValue := optimizer.OptimizeValue(valueData[i].min, valueData[i].max)

			totalFieldLength += len(optimizedField)
			totalValueLength += len(optimizedValue)

			fmt.Printf("  %s: %s → %s (Field:%d字符, Value:%d字符)\n",
				window.desc,
				fmt.Sprintf("%d_%d", window.start, window.start+window.size),
				optimizedField,
				len(optimizedField), len(optimizedValue))
			fmt.Printf("    Value: %d,%d → %s\n",
				valueData[i].min, valueData[i].max, optimizedValue)
		}

		// 计算总存储
		avgFieldLength := float64(totalFieldLength) / 6.0
		avgValueLength := float64(totalValueLength) / 6.0
		totalStorage := len(optimizedKey) + totalFieldLength + totalValueLength

		fmt.Printf("存储统计:\n")
		fmt.Printf("  Key长度: %d字符\n", len(optimizedKey))
		fmt.Printf("  平均Field长度: %.1f字符\n", avgFieldLength)
		fmt.Printf("  平均Value长度: %.1f字符\n", avgValueLength)
		fmt.Printf("  总存储: %d字符\n", totalStorage)

		fmt.Printf(strings.Repeat("-", 50) + "\n")
	}

	// 详细对比分析
	fmt.Printf("\n" + strings.Repeat("=", 70) + "\n")
	fmt.Println("优化效果详细分析:")
	fmt.Println(strings.Repeat("=", 70))

	original := &encoders.OriginalHashMapOptimizer{}
	compact := &encoders.Base62HashMapOptimizer{}
	// ultra := &encoders.UltraCompactHashMapOptimizer{} // 已移除 - 对小值场景是负优化

	// Key对比
	origKey := original.OptimizeKey(keyData)
	compactKey := compact.OptimizeKey(keyData)

	fmt.Printf("\nKey优化对比:\n")
	fmt.Printf("Original:      %s (%d字符)\n", origKey, len(origKey))
	fmt.Printf("Compact:       %s (%d字符, %.1f%%)\n",
		compactKey, len(compactKey),
		float64(len(origKey)-len(compactKey))/float64(len(origKey))*100)

	// Field对比
	fmt.Printf("\nField优化对比 (以0-4小时窗口为例):\n")
	origField := original.OptimizeField(0, 4)
	compactField := compact.OptimizeField(0, 4)

	fmt.Printf("Original:      %s (%d字符)\n", origField, len(origField))
	fmt.Printf("Compact:       %s (%d字符, %.1f%%)\n",
		compactField, len(compactField),
		float64(len(origField)-len(compactField))/float64(len(origField))*100)

	// Value对比 (小值场景)
	fmt.Printf("\nValue优化对比 (小值场景):\n")
	smallMin1, smallMax1 := uint32(1), uint32(2)
	smallMin2, smallMax2 := uint32(6), uint32(8)

	fmt.Printf("小值1 (%d,%d):\n", smallMin1, smallMax1)
	origSmall1 := original.OptimizeValue(smallMin1, smallMax1)
	compactSmall1 := compact.OptimizeValue(smallMin1, smallMax1)
	fmt.Printf("  Original:      %s (%d字符)\n", origSmall1, len(origSmall1))
	fmt.Printf("  Compact:       %s (%d字符)\n", compactSmall1, len(compactSmall1))

	fmt.Printf("小值2 (%d,%d):\n", smallMin2, smallMax2)
	origSmall2 := original.OptimizeValue(smallMin2, smallMax2)
	compactSmall2 := compact.OptimizeValue(smallMin2, smallMax2)
	fmt.Printf("  Original:      %s (%d字符)\n", origSmall2, len(origSmall2))
	fmt.Printf("  Compact:       %s (%d字符)\n", compactSmall2, len(compactSmall2))

	// 编解码正确性验证
	fmt.Printf("\n" + strings.Repeat("=", 70) + "\n")
	fmt.Println("编解码正确性验证:")
	fmt.Println(strings.Repeat("-", 70))

	for _, optimizer := range optimizers {
		fmt.Printf("\n验证 %s:\n", optimizer.GetName())

		// 验证Key
		optimizedKey := optimizer.OptimizeKey(keyData)
		decodedKey, err := optimizer.RestoreKey(optimizedKey)
		keyOK := err == nil && decodedKey.ShopID == keyData.ShopID &&
			decodedKey.ChannelID == keyData.ChannelID

		// 验证Field
		optimizedField := optimizer.OptimizeField(0, 4)
		start, size, err := optimizer.RestoreField(optimizedField)
		fieldOK := err == nil && start == 0 && size == 4

		// 验证Value
		optimizedValue := optimizer.OptimizeValue(123, 456)
		min, max, err := optimizer.RestoreValue(optimizedValue)
		valueOK := err == nil && min == 123 && max == 456

		status := "✅"
		if !keyOK || !fieldOK || !valueOK {
			status = "❌"
		}

		fmt.Printf("  %s Key:%v, Field:%v, Value:%v\n",
			status, keyOK, fieldOK, valueOK)
	}

	// 实际应用建议
	fmt.Printf("\n" + strings.Repeat("=", 70) + "\n")
	fmt.Println("实际应用建议:")
	fmt.Println(strings.Repeat("=", 70))

	fmt.Printf("1. 【强烈推荐】CompactHashMap方案 (小值场景最优):\n")
	fmt.Printf("   - Field优化效果显著: '0_4' → '0' (节省66%%)\n")
	fmt.Printf("   - Key适度压缩，保持可读性\n")
	fmt.Printf("   - 小值保持文本格式(最优选择)\n")
	fmt.Printf("   - 平衡了存储效率和维护性\n\n")

	fmt.Printf("2. 小值场景关键优化点排序:\n")
	fmt.Printf("   ① Field优化: 0_4→0 (影响最大，节省66%%)\n")
	fmt.Printf("   ② Key压缩: Base62/二进制位打包 (更短)\n")
	fmt.Printf("   ③ Value策略: 小值必须保持文本格式\n")
	fmt.Printf("   ④ 确保Redis使用listpack编码\n\n")

	fmt.Printf("3. 小值场景特别注意:\n")
	fmt.Printf("   - 避免过度优化Value (文本已经最优)\n")
	fmt.Printf("   - 重点关注Field和Key的压缩\n")
	fmt.Printf("   - 专注于真正有效的优化策略\n")
}
