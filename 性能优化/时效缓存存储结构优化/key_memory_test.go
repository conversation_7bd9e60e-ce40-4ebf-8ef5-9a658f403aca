package main

import (
	"context"
	"encoding/binary"
	"strconv"
	"testing"

	"github.com/go-redis/redis/v8"
)

// compressAggregatedKeyHex replicates the previous hex-string key encoding for comparison.
func compressAggregatedKeyHex(d TestData) string {
	var shopNorm uint64
	if d.ShopID >= 100000000 {
		shopNorm = d.ShopID - 100000000
	} else {
		shopNorm = d.ShopID
	}
	shop := shopNorm & ((1 << 30) - 1)
	seller := uint64(d.<PERSON>ller<PERSON>oc) & ((1 << 16) - 1)
	buyer := uint64(d.BuyerLoc) & ((1 << 16) - 1)
	packed := (shop << 32) | (seller << 16) | buyer
	return strconv.FormatUint(packed, 16)
}

func compressAggregatedKeyBin(d TestData) string {
	var shopNorm uint64
	if d.ShopID >= 100000000 {
		shopNorm = d.ShopID - 100000000
	} else {
		shopNorm = d.ShopID
	}
	shop := shopNorm & ((1 << 30) - 1)
	seller := uint64(d.<PERSON><PERSON><PERSON><PERSON>) & ((1 << 16) - 1)
	buyer := uint64(d.BuyerLoc) & ((1 << 16) - 1)
	packed := (shop << 32) | (seller << 16) | buyer
	var buf [8]byte
	binary.BigEndian.PutUint64(buf[:], packed)
	return string(buf[:])
}

// writeGrouped writes dataset using the given key encoder and the same fixed 56b value layout as KVBinaryBitPackStrategy.
func writeGrouped(ctx context.Context, rdb *redis.Client, testData []TestData, keyEncoder func(TestData) string) (int, error) {
	groups := make(map[string]map[uint8]*[12]uint8)
	for _, d := range testData {
		key := keyEncoder(d)
		m := groups[key]
		if m == nil {
			m = make(map[uint8]*[12]uint8)
			groups[key] = m
		}
		w := uint8(uint32(d.Date.Weekday()) & 0x7)
		c := uint8((d.ChannelID - 8000) & 0x1F)
		wc := (w << 5) | c
		arr := m[wc]
		if arr == nil {
			arr = new([12]uint8)
			m[wc] = arr
		}
		idx := int((d.WindowStart / 4) & 0x7) // 0..5
		if idx < 6 {
			arr[idx*2] = uint8(d.Min & 0xF)
			arr[idx*2+1] = uint8(d.Max & 0xF)
		}
	}

	pipe := rdb.Pipeline()
	const batchSize = 1000
	i := 0
	for key, entries := range groups {
		buf := packWC6EntriesToBytes(entries)
		pipe.Set(ctx, key, buf, 0)
		i++
		if i%batchSize == 0 {
			if _, err := pipe.Exec(ctx); err != nil {
				return 0, err
			}
			pipe = rdb.Pipeline()
		}
	}
	if len(groups)%batchSize != 0 {
		if _, err := pipe.Exec(ctx); err != nil {
			return 0, err
		}
	}
	return len(groups), nil
}

func TestCompareBinaryVsHexKeyMemory(t *testing.T) {
	ctx := context.Background()
	rdb := redis.NewClient(&redis.Options{Addr: "localhost:6379", DB: 0})
	defer rdb.Close()

	if err := rdb.Ping(ctx).Err(); err != nil {
		t.Skipf("Redis not available: %v", err)
	}

	// Use the same synthetic data generator as benchmark.
	// Choose a moderate sample size to complete quickly but be representative.
	sampleSize := 20000
	testData := generateTestData(sampleSize)
	t.Logf("Generated %d records for %d logical keys", len(testData), sampleSize)

	// Run HEX key case
	rdb.FlushAll(ctx)
	base := getBenchmarkRedisMemory(ctx, rdb)
	keysHex, err := writeGrouped(ctx, rdb, testData, compressAggregatedKeyHex)
	if err != nil {
		t.Fatalf("write HEX failed: %v", err)
	}
	memHex := getBenchmarkRedisMemory(ctx, rdb) - base
	t.Logf("HEX: keys=%d, memory=%d bytes", keysHex, memHex)

	// Run BIN key case
	rdb.FlushAll(ctx)
	base = getBenchmarkRedisMemory(ctx, rdb)
	keysBin, err := writeGrouped(ctx, rdb, testData, compressAggregatedKeyBin)
	if err != nil {
		t.Fatalf("write BIN failed: %v", err)
	}
	memBin := getBenchmarkRedisMemory(ctx, rdb) - base
	t.Logf("BIN: keys=%d, memory=%d bytes", keysBin, memBin)

	// Report delta (do not assert, just compare and print)
	diff := int64(memHex - memBin)
	t.Logf("Delta (HEX - BIN) = %d bytes, per-key ≈ %.2f bytes", diff, float64(diff)/float64(keysBin))
}

func TestCompareEncodedKeyLength(t *testing.T) {
	// 直接比较编码后字符串长度（字节数），不访问 Redis
	sampleSize := 20000
	testData := generateTestData(sampleSize)

	type agg struct {
		shop   uint64
		seller uint16
		buyer  uint16
	}

	seen := make(map[agg]struct{})
	hexHist := make(map[int]int)
	var (
		hexMin = 1<<31 - 1
		hexMax = 0
		hexSum = 0
		n      = 0
	)

	for _, d := range testData {
		a := agg{shop: d.ShopID, seller: d.SellerLoc, buyer: d.BuyerLoc}
		if _, ok := seen[a]; ok {
			continue
		}
		seen[a] = struct{}{}

		hexKey := compressAggregatedKeyHex(d)
		binKey := compressAggregatedKeyBin(d)

		hl := len(hexKey)
		bl := len(binKey)
		if bl != 8 {
			t.Fatalf("binary key length expected 8, got %d", bl)
		}
		hexHist[hl]++
		if hl < hexMin {
			hexMin = hl
		}
		if hl > hexMax {
			hexMax = hl
		}
		hexSum += hl
		n++
	}

	if n == 0 {
		t.Fatalf("no unique aggregated keys found")
	}

	avg := float64(hexSum) / float64(n)
	t.Logf("Unique aggregated keys: %d", n)
	t.Logf("Hex length: min=%d, avg=%.2f, max=%d", hexMin, avg, hexMax)
	t.Logf("Hex length histogram: %v", hexHist)
	t.Logf("Binary length: always 8 bytes")
}
