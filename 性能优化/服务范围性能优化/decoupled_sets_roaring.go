package main

import "github.com/RoaringBitmap/roaring"

// DecoupledSetsRoaring stores origins and destinations in two independent (decoupled) bitmaps for each TPL.
type DecoupledSetsRoaring struct {
	data map[uint32]*OriginDestBitmaps
}

// OriginDestBitmaps holds the two separate bitmaps for origins and destinations.
type OriginDestBitmaps struct {
	Origins *roaring.Bitmap
	Dests   *roaring.Bitmap
}

// NewDecoupledSetsRoaring now efficiently consumes the decoupled data.
func NewDecoupledSetsRoaring(data map[uint32]TplCoverage) *DecoupledSetsRoaring {
	s := &DecoupledSetsRoaring{
		data: make(map[uint32]*OriginDestBitmaps),
	}

	for tplID, coverage := range data {
		originBitmap := roaring.New()
		originBitmap.AddMany(coverage.Origins)

		destBitmap := roaring.New()
		destBitmap.AddMany(coverage.Dests)

		s.data[tplID] = &OriginDestBitmaps{
			Origins: originBitmap,
			Dests:   destBitmap,
		}
	}
	return s
}

// IsServiceable checks if an origin and a destination are serviceable for a given TPL.
func (s *DecoupledSetsRoaring) IsServiceable(tpl, origin, dest uint32) bool {
	if bitmaps, ok := s.data[tpl]; ok {
		return bitmaps.Origins.Contains(origin) && bitmaps.Dests.Contains(dest)
	}
	return false
}
