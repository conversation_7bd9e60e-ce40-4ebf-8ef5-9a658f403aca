package encoders

import (
	"encoding/binary"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// KeyData 表示原始键数据结构
type KeyData struct {
	ShopID      uint64    // 店铺ID (8-9位，如890848129)
	SellerLoc   uint16    // 卖家位置 (4位，如1734)
	BuyerLoc    uint16    // 买家位置 (4位，如1734)
	ChannelID   uint32    // 渠道ID (5位，如80012)
	Date        time.Time // 日期 (如20250817)
	WindowStart uint8     // 时间窗开始小时 (0-23，如0表示0点)
	WindowSize  uint8     // 时间窗大小小时 (1-24，如4表示4小时窗口)
}

// ValueData 表示键对应的值数据结构
type ValueData struct {
	Min uint32 // 最小值
	Max uint32 // 最大值
}

const base62Chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func uint64ToBase62(num uint64) string {
	if num == 0 {
		return "0"
	}

	var result strings.Builder
	for num > 0 {
		result.WriteByte(base62Chars[num%62])
		num /= 62
	}

	// 反转字符串
	s := result.String()
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func base62ToUint64(s string) uint64 {
	result := uint64(0)
	base := uint64(1)

	for i := len(s) - 1; i >= 0; i-- {
		char := s[i]
		var val uint64
		switch {
		case char >= '0' && char <= '9':
			val = uint64(char - '0')
		case char >= 'a' && char <= 'z':
			val = uint64(char - 'a' + 10)
		case char >= 'A' && char <= 'Z':
			val = uint64(char - 'A' + 36)
		}
		result += val * base
		base *= 62
	}
	return result
}

// HashMapOptimizer HashMap存储优化器接口
type HashMapOptimizer interface {
	OptimizeKey(data *KeyData) string
	OptimizeField(windowStart, windowSize uint8) string
	OptimizeValue(min, max uint32) string

	RestoreKey(optimizedKey string) (*KeyData, error)
	RestoreField(optimizedField string) (windowStart, windowSize uint8, err error)
	RestoreValue(optimizedValue string) (min, max uint32, err error)

	GetName() string
	GetDescription() string
}

// OriginalHashMapOptimizer 原始HashMap格式（基准对比）
type OriginalHashMapOptimizer struct{}

func (o *OriginalHashMapOptimizer) GetName() string {
	return "OriginalHashMap"
}

func (o *OriginalHashMapOptimizer) GetDescription() string {
	return "原始HashMap: Key=shop_seller_buyer_channel_date, Field=start_end, Value=min,max"
}

func (o *OriginalHashMapOptimizer) OptimizeKey(data *KeyData) string {
	dateStr := data.Date.Format("20060102")
	return fmt.Sprintf("%d_%d_%d_%d_%s",
		data.ShopID, data.SellerLoc, data.BuyerLoc, data.ChannelID, dateStr)
}

func (o *OriginalHashMapOptimizer) OptimizeField(windowStart, windowSize uint8) string {
	windowEnd := windowStart + windowSize
	return fmt.Sprintf("%d_%d", windowStart, windowEnd)
}

func (o *OriginalHashMapOptimizer) OptimizeValue(min, max uint32) string {
	return fmt.Sprintf("%d,%d", min, max)
}

func (o *OriginalHashMapOptimizer) RestoreKey(optimizedKey string) (*KeyData, error) {
	parts := strings.Split(optimizedKey, "_")
	if len(parts) != 5 {
		return nil, fmt.Errorf("invalid key format")
	}

	shopID, _ := strconv.ParseUint(parts[0], 10, 64)
	sellerLoc, _ := strconv.ParseUint(parts[1], 10, 16)
	buyerLoc, _ := strconv.ParseUint(parts[2], 10, 16)
	channelID, _ := strconv.ParseUint(parts[3], 10, 32)
	date, _ := time.Parse("20060102", parts[4])

	return &KeyData{
		ShopID:    shopID,
		SellerLoc: uint16(sellerLoc),
		BuyerLoc:  uint16(buyerLoc),
		ChannelID: uint32(channelID),
		Date:      date,
	}, nil
}

func (o *OriginalHashMapOptimizer) RestoreField(optimizedField string) (uint8, uint8, error) {
	parts := strings.Split(optimizedField, "_")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("invalid field format")
	}

	start, _ := strconv.ParseUint(parts[0], 10, 8)
	end, _ := strconv.ParseUint(parts[1], 10, 8)

	return uint8(start), uint8(end) - uint8(start), nil
}

func (o *OriginalHashMapOptimizer) RestoreValue(optimizedValue string) (uint32, uint32, error) {
	parts := strings.Split(optimizedValue, ",")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("invalid value format")
	}

	min, _ := strconv.ParseUint(parts[0], 10, 32)
	max, _ := strconv.ParseUint(parts[1], 10, 32)

	return uint32(min), uint32(max), nil
}

// Base62HashMapOptimizer 紧凑HashMap优化器
type Base62HashMapOptimizer struct{}

func (o *Base62HashMapOptimizer) GetName() string {
	return "Base62HashMap"
}

func (o *Base62HashMapOptimizer) GetDescription() string {
	return "Base62键压缩: Key用Base62, Field用4小时窗口索引, Value自适应"
}

func (o *Base62HashMapOptimizer) OptimizeKey(data *KeyData) string {
	// 使用Base62编码主要字段
	shopB62 := uint64ToBase62(data.ShopID)
	sellerB62 := uint64ToBase62(uint64(data.SellerLoc))
	buyerB62 := uint64ToBase62(uint64(data.BuyerLoc))
	channelB62 := uint64ToBase62(uint64(data.ChannelID))

	// 日期压缩为 weekday (0-6)
	weekday := uint64(data.Date.Weekday())
	dateB62 := uint64ToBase62(weekday)

	return fmt.Sprintf("%s_%s_%s_%s_%s", shopB62, sellerB62, buyerB62, channelB62, dateB62)
}

func (o *Base62HashMapOptimizer) OptimizeField(windowStart, windowSize uint8) string {
	// 固定4小时窗口：0-4→0, 4-8→1, 8-12→2, 12-16→3, 16-20→4, 20-24→5
	windowIndex := windowStart / 4
	return fmt.Sprintf("%d", windowIndex)
}

func (o *Base62HashMapOptimizer) OptimizeValue(min, max uint32) string {
	return fmt.Sprintf("%d,%d", min, max) // 文本格式
}

func (o *Base62HashMapOptimizer) RestoreKey(optimizedKey string) (*KeyData, error) {
	parts := strings.Split(optimizedKey, "_")
	if len(parts) != 5 {
		return nil, fmt.Errorf("invalid key format")
	}

	shopID := base62ToUint64(parts[0])
	sellerLoc := uint16(base62ToUint64(parts[1]))
	buyerLoc := uint16(base62ToUint64(parts[2]))
	channelID := uint32(base62ToUint64(parts[3]))
	weekdayCode := base62ToUint64(parts[4]) % 7

	epoch := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	diff := int(time.Weekday(weekdayCode)) - int(epoch.Weekday())
	if diff < 0 {
		diff += 7
	}
	date := epoch.AddDate(0, 0, diff)

	return &KeyData{
		ShopID:    shopID,
		SellerLoc: sellerLoc,
		BuyerLoc:  buyerLoc,
		ChannelID: channelID,
		Date:      date,
	}, nil
}

func (o *Base62HashMapOptimizer) RestoreField(optimizedField string) (uint8, uint8, error) {
	windowIndex, err := strconv.ParseUint(optimizedField, 10, 8)
	if err != nil {
		return 0, 0, err
	}

	windowStart := uint8(windowIndex) * 4
	return windowStart, 4, nil // 固定4小时窗口
}

func (o *Base62HashMapOptimizer) RestoreValue(optimizedValue string) (uint32, uint32, error) {
	if strings.HasPrefix(optimizedValue, "b:") {
		// 二进制格式
		hexStr := optimizedValue[2:]
		if len(hexStr) != 16 { // 8字节 = 16个十六进制字符
			return 0, 0, fmt.Errorf("invalid binary value length")
		}

		buf := make([]byte, 8)
		for i := 0; i < 16; i += 2 {
			val, err := strconv.ParseUint(hexStr[i:i+2], 16, 8)
			if err != nil {
				return 0, 0, err
			}
			buf[i/2] = byte(val)
		}

		min := binary.BigEndian.Uint32(buf[0:4])
		max := binary.BigEndian.Uint32(buf[4:8])
		return min, max, nil
	}

	// 文本格式
	parts := strings.Split(optimizedValue, ",")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("invalid text value format")
	}

	min, _ := strconv.ParseUint(parts[0], 10, 32)
	max, _ := strconv.ParseUint(parts[1], 10, 32)

	return uint32(min), uint32(max), nil
}

// UltraCompactHashMapOptimizer 极致紧凑HashMap优化器
type UltraCompactHashMapOptimizer struct{}

func (o *UltraCompactHashMapOptimizer) GetName() string {
	return "UltraCompactHashMap"
}

// BinaryPackedHashMapOptimizer 二进制位打包 + Base64URL 的 HashMap 优化器（仅优化 Key，Field/Value 复用现有方案）
type BinaryPackedHashMapOptimizer struct{}

func (o *BinaryPackedHashMapOptimizer) GetName() string { return "BinaryPackedHashMap" }
func (o *BinaryPackedHashMapOptimizer) GetDescription() string {
	return "二进制打包Key(16进制)，Field用单字节索引，Value自适应"
}

func (o *BinaryPackedHashMapOptimizer) OptimizeKey(data *KeyData) string {
	// 位宽调整：ShopID 30b、Seller 16b、Buyer 16b、Channel(20种) 5b、Weekday 3b → 共70b
	// 使用9字节编码（72b），高2bit留空，采用自定义9字节大端布局
	shop := data.ShopID & ((1 << 30) - 1)
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyer := uint64(data.BuyerLoc) & ((1 << 16) - 1)
	var chanNorm uint64
	if data.ChannelID >= 8000 && data.ChannelID < 8020 {
		chanNorm = uint64(data.ChannelID-8000) & ((1 << 5) - 1)
	} else {
		chanNorm = uint64(data.ChannelID) & ((1 << 5) - 1)
	}
	weekday := uint64(data.Date.Weekday()) & 0x7

	// 将70bit拆为高6bit和低64bit
	high6 := uint8((shop >> 24) & 0x3F) // ShopID高6位
	shopLow24 := uint64(shop & ((1 << 24) - 1))

	// 低64位布局（从高到低）：shopLow24(24) | seller(16) | buyer(16) | chan(5) | weekday(3)
	var low uint64
	low |= shopLow24 << 40
	low |= seller << 24
	low |= buyer << 8
	low |= chanNorm << 3
	low |= weekday

	// 将70位数据转换为16进制字符串
	// 高6位单独处理
	high6Str := strconv.FormatUint(uint64(high6), 16)
	// 低64位转换
	lowStr := strconv.FormatUint(low, 16)
	// 组合：高6位 + ":" + 低64位，确保唯一性
	return high6Str + ":" + lowStr
}

func (o *BinaryPackedHashMapOptimizer) OptimizeField(windowStart, windowSize uint8) string {
	return (&Base62HashMapOptimizer{}).OptimizeField(windowStart, windowSize)
}

func (o *BinaryPackedHashMapOptimizer) OptimizeValue(min, max uint32) string {
	return (&Base62HashMapOptimizer{}).OptimizeValue(min, max)
}

func (o *BinaryPackedHashMapOptimizer) RestoreKey(optimizedKey string) (*KeyData, error) {
	// 仅用于结构验证：反解回占位日期（与 weekday 匹配），业务不依赖具体年月日
	// 解析 "high6:low64" 格式
	parts := strings.Split(optimizedKey, ":")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid hex key format: %s", optimizedKey)
	}

	high6, err := strconv.ParseUint(parts[0], 16, 8)
	if err != nil {
		return nil, fmt.Errorf("invalid high6 hex: %v", err)
	}

	low, err := strconv.ParseUint(parts[1], 16, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid low64 hex: %v", err)
	}

	weekday := low & 0x7
	chanNorm := (low >> 3) & ((1 << 5) - 1)
	buyer := (low >> 8) & ((1 << 16) - 1)
	seller := (low >> 24) & ((1 << 16) - 1)
	shopLow24 := (low >> 40) & ((1 << 24) - 1)
	shop := (high6 << 24) | shopLow24

	epoch := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	diff := int(time.Weekday(weekday)) - int(epoch.Weekday())
	if diff < 0 {
		diff += 7
	}
	date := epoch.AddDate(0, 0, diff)

	return &KeyData{
		ShopID:    shop,
		SellerLoc: uint16(seller),
		BuyerLoc:  uint16(buyer),
		ChannelID: uint32(chanNorm) + 8000,
		Date:      date,
	}, nil
}

func (o *BinaryPackedHashMapOptimizer) RestoreField(optimizedField string) (uint8, uint8, error) {
	return (&Base62HashMapOptimizer{}).RestoreField(optimizedField)
}

func (o *BinaryPackedHashMapOptimizer) RestoreValue(optimizedValue string) (uint32, uint32, error) {
	return (&Base62HashMapOptimizer{}).RestoreValue(optimizedValue)
}

func (o *UltraCompactHashMapOptimizer) GetDescription() string {
	return "极致紧凑HashMap: Key用二进制编码, Field用单字符, Value打包所有窗口"
}

func (o *UltraCompactHashMapOptimizer) OptimizeKey(data *KeyData) string {
	// 使用二进制编码，类似CompactBinaryEncoder
	var buf []byte

	// ShopID: 4字节
	shopBytes := make([]byte, 4)
	binary.BigEndian.PutUint32(shopBytes, uint32(data.ShopID))
	buf = append(buf, shopBytes...)

	// SellerLoc + BuyerLoc: 各2字节
	sellerBytes := make([]byte, 2)
	buyerBytes := make([]byte, 2)
	binary.BigEndian.PutUint16(sellerBytes, data.SellerLoc)
	binary.BigEndian.PutUint16(buyerBytes, data.BuyerLoc)
	buf = append(buf, sellerBytes...)
	buf = append(buf, buyerBytes...)

	// ChannelID: 3字节（最大1600万）
	channelBytes := make([]byte, 4)
	binary.BigEndian.PutUint32(channelBytes, data.ChannelID)
	buf = append(buf, channelBytes[1:]...) // 只取后3字节

	// Date: 2字节（从2020年开始的天数）
	epoch := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	days := uint16(data.Date.Sub(epoch).Hours() / 24)
	dateBytes := make([]byte, 2)
	binary.BigEndian.PutUint16(dateBytes, days)
	buf = append(buf, dateBytes...)

	return fmt.Sprintf("K:%x", buf)
}

func (o *UltraCompactHashMapOptimizer) OptimizeField(windowStart, windowSize uint8) string {
	// 使用单字符：0,1,2,3,4,5
	windowIndex := windowStart / 4
	return string(rune('0' + windowIndex))
}

func (o *UltraCompactHashMapOptimizer) OptimizeValue(min, max uint32) string {
	// 使用2字节存储每个值（假设不超过65535）
	if min > 65535 || max > 65535 {
		// 如果超出范围，降级到4字节存储
		buf := make([]byte, 8)
		binary.BigEndian.PutUint32(buf[0:4], min)
		binary.BigEndian.PutUint32(buf[4:8], max)
		return fmt.Sprintf("L:%x", buf)
	}

	buf := make([]byte, 4)
	binary.BigEndian.PutUint16(buf[0:2], uint16(min))
	binary.BigEndian.PutUint16(buf[2:4], uint16(max))
	return fmt.Sprintf("S:%x", buf)
}

func (o *UltraCompactHashMapOptimizer) RestoreKey(optimizedKey string) (*KeyData, error) {
	if !strings.HasPrefix(optimizedKey, "K:") {
		return nil, fmt.Errorf("invalid key prefix")
	}

	hexStr := optimizedKey[2:]
	buf := make([]byte, len(hexStr)/2)
	for i := 0; i < len(hexStr); i += 2 {
		val, err := strconv.ParseUint(hexStr[i:i+2], 16, 8)
		if err != nil {
			return nil, err
		}
		buf[i/2] = byte(val)
	}

	if len(buf) < 15 { // 4+2+2+3+2 = 13字节最少
		return nil, fmt.Errorf("invalid key length")
	}

	shopID := uint64(binary.BigEndian.Uint32(buf[0:4]))
	sellerLoc := binary.BigEndian.Uint16(buf[4:6])
	buyerLoc := binary.BigEndian.Uint16(buf[6:8])

	// ChannelID: 3字节转4字节
	channelBytes := make([]byte, 4)
	copy(channelBytes[1:], buf[8:11])
	channelID := binary.BigEndian.Uint32(channelBytes)

	days := binary.BigEndian.Uint16(buf[11:13])
	epoch := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	date := epoch.AddDate(0, 0, int(days))

	return &KeyData{
		ShopID:    shopID,
		SellerLoc: sellerLoc,
		BuyerLoc:  buyerLoc,
		ChannelID: channelID,
		Date:      date,
	}, nil
}

func (o *UltraCompactHashMapOptimizer) RestoreField(optimizedField string) (uint8, uint8, error) {
	if len(optimizedField) != 1 {
		return 0, 0, fmt.Errorf("invalid field length")
	}

	windowIndex := uint8(optimizedField[0] - '0')
	if windowIndex > 5 {
		return 0, 0, fmt.Errorf("invalid window index")
	}

	windowStart := windowIndex * 4
	return windowStart, 4, nil
}

func (o *UltraCompactHashMapOptimizer) RestoreValue(optimizedValue string) (uint32, uint32, error) {
	if len(optimizedValue) < 2 {
		return 0, 0, fmt.Errorf("invalid value format")
	}

	prefix := optimizedValue[:2]
	hexStr := optimizedValue[2:]

	if prefix == "S:" {
		// 2字节格式
		if len(hexStr) != 8 { // 4字节 = 8个十六进制字符
			return 0, 0, fmt.Errorf("invalid short value length")
		}

		buf := make([]byte, 4)
		for i := 0; i < 8; i += 2 {
			val, err := strconv.ParseUint(hexStr[i:i+2], 16, 8)
			if err != nil {
				return 0, 0, err
			}
			buf[i/2] = byte(val)
		}

		min := uint32(binary.BigEndian.Uint16(buf[0:2]))
		max := uint32(binary.BigEndian.Uint16(buf[2:4]))
		return min, max, nil
	}

	if prefix == "L:" {
		// 4字节格式
		if len(hexStr) != 16 { // 8字节 = 16个十六进制字符
			return 0, 0, fmt.Errorf("invalid long value length")
		}

		buf := make([]byte, 8)
		for i := 0; i < 16; i += 2 {
			val, err := strconv.ParseUint(hexStr[i:i+2], 16, 8)
			if err != nil {
				return 0, 0, err
			}
			buf[i/2] = byte(val)
		}

		min := binary.BigEndian.Uint32(buf[0:4])
		max := binary.BigEndian.Uint32(buf[4:8])
		return min, max, nil
	}

	return 0, 0, fmt.Errorf("unknown value prefix: %s", prefix)
}

// AggregationBinaryPackOptimizer 聚合场景专用二进制打包优化器
// 优化ShopID(30b)+SellerLoc(16b)+BuyerLoc(16b)=62位的聚合KEY
type AggregationBinaryPackOptimizer struct{}

func (o *AggregationBinaryPackOptimizer) GetName() string {
	return "AggregationBinaryPack"
}

func (o *AggregationBinaryPackOptimizer) GetDescription() string {
	return "聚合专用二进制打包: ShopID(30b)+SellerLoc(16b)+BuyerLoc(16b)=62位压缩"
}

func (o *AggregationBinaryPackOptimizer) OptimizeKey(data *KeyData) string {
	// 聚合KEY压缩：ShopID(30b) + SellerLoc(16b) + BuyerLoc(16b) = 62位
	// 使用8字节编码（64位），高2位留空

	// ShopID压缩：假设从100000000开始，映射为相对值 (30位支持1G店铺)
	var shopNorm uint64
	if data.ShopID >= 100000000 {
		shopNorm = data.ShopID - 100000000
	} else {
		shopNorm = data.ShopID
	}
	shop := shopNorm & ((1 << 30) - 1) // 30位掩码

	// SellerLoc和BuyerLoc直接使用 (各16位)
	seller := uint64(data.SellerLoc) & ((1 << 16) - 1)
	buyer := uint64(data.BuyerLoc) & ((1 << 16) - 1)

	// 62位打包: ShopID(30) + SellerLoc(16) + BuyerLoc(16)
	packed := (shop << 32) | (seller << 16) | buyer

	// 转换为16进制字符串
	return strconv.FormatUint(packed, 16)
}

func (o *AggregationBinaryPackOptimizer) OptimizeField(windowStart, windowSize uint8) string {
	// 使用单字节窗口索引: 0,4,8,12,16,20 -> 0,1,2,3,4,5
	windowIndex := windowStart / 4
	return fmt.Sprintf("%d", windowIndex)
}

func (o *AggregationBinaryPackOptimizer) OptimizeValue(min, max uint32) string {
	// 直接存储为逗号分隔的字符串（与原版保持一致）
	return fmt.Sprintf("%d,%d", min, max)
}

func (o *AggregationBinaryPackOptimizer) RestoreKey(optimizedKey string) (*KeyData, error) {
	// 16进制解码
	packed, err := strconv.ParseUint(optimizedKey, 16, 64)
	if err != nil {
		return nil, fmt.Errorf("hex decode error: %v", err)
	}

	// 提取各字段
	buyer := uint16(packed & ((1 << 16) - 1))
	seller := uint16((packed >> 16) & ((1 << 16) - 1))
	shopNorm := (packed >> 32) & ((1 << 30) - 1)

	// 恢复ShopID
	shopID := shopNorm + 100000000

	return &KeyData{
		ShopID:    shopID,
		SellerLoc: seller,
		BuyerLoc:  buyer,
		// 聚合KEY不包含以下字段
		ChannelID:   0,
		Date:        time.Time{},
		WindowStart: 0,
		WindowSize:  0,
	}, nil
}

func (o *AggregationBinaryPackOptimizer) RestoreField(optimizedField string) (windowStart, windowSize uint8, err error) {
	windowIndex, err := strconv.Atoi(optimizedField)
	if err != nil {
		return 0, 0, err
	}

	windowStart = uint8(windowIndex * 4)
	windowSize = 4
	return windowStart, windowSize, nil
}

func (o *AggregationBinaryPackOptimizer) RestoreValue(optimizedValue string) (min, max uint32, err error) {
	parts := strings.Split(optimizedValue, ",")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("invalid value format: %s", optimizedValue)
	}

	minVal, err := strconv.ParseUint(parts[0], 10, 32)
	if err != nil {
		return 0, 0, err
	}

	maxVal, err := strconv.ParseUint(parts[1], 10, 32)
	if err != nil {
		return 0, 0, err
	}

	return uint32(minVal), uint32(maxVal), nil
}

// 获取所有HashMap优化器
func GetAllHashMapOptimizers() []HashMapOptimizer {
	return []HashMapOptimizer{
		&OriginalHashMapOptimizer{},
		&Base62HashMapOptimizer{},
		&BinaryPackedHashMapOptimizer{},
		&AggregationBinaryPackOptimizer{},
	}
}
