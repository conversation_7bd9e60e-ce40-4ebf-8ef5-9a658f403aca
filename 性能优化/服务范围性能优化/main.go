package main

import (
	"fmt"
	"os"
	"runtime"
	"runtime/debug"
	"time"
)

const (
	// Define a fixed number of queries for our custom benchmark
	numQueries = 1_000_000
)

func main() {
	fmt.Println("--- Custom Benchmark Suite ---")

	// --- 1. Data Generation ---
	fmt.Println("\nGenerating decoupled data source... (This should be fast)")
	startTime := time.Now()
	decoupledData := generateDecoupledData()
	fmt.Printf("Data generation complete. Took: %v\n", time.Since(startTime))

	// Generate both individual queries and batch queries for testing
	testQueries := generateQueries(10000)
	batchQueries := generateBatchQueries(1000) // 1000 batches, each with 125 queries

	// --- 2. Run Benchmarks for each implementation ---

	// Test NativeBitVector (raw bit vector, branchless query)
	runBenchmarkFor("NativeBitVector", func() interface{} {
		return NewNativeBitVector(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*NativeBitVector).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test DecoupledSetsRoaring (native model, should be fast)
	runBenchmarkFor("DecoupledSetsRoaring", func() interface{} {
		return NewDecoupledSetsRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*DecoupledSetsRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test InvertedIndexMap (logically same as Decoupled, different data layout with Go maps)
	runBenchmarkFor("InvertedIndexMap", func() interface{} {
		return NewInvertedIndexMap(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*InvertedIndexMap).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test PartitionedBitmapRoaring (advanced layout optimization)
	runBenchmarkFor("PartitionedBitmapRoaring", func() interface{} {
		return NewPartitionedBitmapRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*PartitionedBitmapRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test NestedMapRoaring (will be slow due to internal Cartesian product)
	runBenchmarkFor("NestedMapRoaring", func() interface{} {
		return NewNestedMapRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*NestedMapRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test FlattenIndexRoaring (will be slow due to internal Cartesian product)
	runBenchmarkFor("FlattenIndexRoaring", func() interface{} {
		return NewFlattenIndexRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*FlattenIndexRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	fmt.Println("\n--- Batch Query Benchmarks ---")

	// Test batch queries with different implementations
	runBatchBenchmarkFor("NativeBitVector", func() interface{} {
		return NewNativeBitVector(decoupledData)
	}, func(instance interface{}, batch []ServicePair) []bool {
		return instance.(*NativeBitVector).BatchIsServiceable(batch)
	}, batchQueries)

	runBatchBenchmarkFor("InvertedIndexMap", func() interface{} {
		return NewInvertedIndexMap(decoupledData)
	}, func(instance interface{}, batch []ServicePair) []bool {
		return instance.(*InvertedIndexMap).BatchIsServiceable(batch)
	}, batchQueries)

	fmt.Println("\n--- Benchmark Suite Complete ---")
}

// runBenchmarkFor is a helper function to test a given implementation
func runBenchmarkFor(
	name string,
	buildFunc func() interface{},
	queryFunc func(instance interface{}, tpl, origin, dest uint32) bool,
	queries []ServicePair,
) {
	fmt.Printf("\n--- Testing: %s ---\n", name)

	// --- Measure Build Time & Memory ---
	runtime.GC() // Clean up before measurement
	var memBefore, memAfter runtime.MemStats
	runtime.ReadMemStats(&memBefore)

	buildStartTime := time.Now()
	instance := buildFunc()
	buildDuration := time.Since(buildStartTime)

	runtime.GC() // Clean up after build to get accurate memory reading
	runtime.ReadMemStats(&memAfter)

	// Check for underflow before calculating. Use HeapAlloc for more accurate measurement of data structures.
	var memUsed float64
	if memAfter.HeapAlloc > memBefore.HeapAlloc {
		memUsed = float64(memAfter.HeapAlloc-memBefore.HeapAlloc) / 1024 / 1024
	} else {
		memUsed = 0 // Measurement was unstable, report 0
	}

	fmt.Printf("Build Time: %v\n", buildDuration)
	fmt.Printf("Memory Used by Structure: %.2f MB\n", memUsed)

	// --- Measure Query Time ---
	queryStartTime := time.Now()
	for i := 0; i < numQueries; i++ {
		q := queries[i%len(queries)]
		_ = queryFunc(instance, q.TplID, q.OriginID, q.DestID)
	}
	queryDuration := time.Since(queryStartTime)
	avgQueryTime := float64(queryDuration.Nanoseconds()) / float64(numQueries)

	fmt.Printf("Query Time (%d queries): %v\n", numQueries, queryDuration)
	fmt.Printf("Average Query Time: %.2f ns/op\n", avgQueryTime)

	// Aggressively clean up memory before the next test
	instance = nil
	debug.FreeOSMemory()
}

// runBatchBenchmarkFor is a helper function to test batch query processing
func runBatchBenchmarkFor(
	name string,
	buildFunc func() interface{},
	batchFunc func(instance interface{}, batch []ServicePair) []bool,
	batches [][]ServicePair,
) {
	fmt.Printf("\n--- Batch Testing: %s ---\n", name)

	// --- Measure Build Time & Memory ---
	runtime.GC() // Clean up before measurement
	var memBefore, memAfter runtime.MemStats
	runtime.ReadMemStats(&memBefore)

	buildStartTime := time.Now()
	instance := buildFunc()
	buildDuration := time.Since(buildStartTime)

	runtime.GC() // Clean up after build to get accurate memory reading
	runtime.ReadMemStats(&memAfter)

	// Check for underflow before calculating
	var memUsed float64
	if memAfter.HeapAlloc > memBefore.HeapAlloc {
		memUsed = float64(memAfter.HeapAlloc-memBefore.HeapAlloc) / 1024 / 1024
	} else {
		memUsed = 0 // Measurement was unstable, report 0
	}

	fmt.Printf("Build Time: %v\n", buildDuration)
	fmt.Printf("Memory Used by Structure: %.2f MB\n", memUsed)

	// --- Measure Batch Processing Time ---
	totalQueries := 0
	for _, batch := range batches {
		totalQueries += len(batch)
	}

	batchStartTime := time.Now()
	for _, batch := range batches {
		_ = batchFunc(instance, batch)
	}
	batchDuration := time.Since(batchStartTime)
	avgQueryTime := float64(batchDuration.Nanoseconds()) / float64(totalQueries)

	fmt.Printf("Batch Processing Time (%d batches, %d total queries): %v\n", len(batches), totalQueries, batchDuration)
	fmt.Printf("Average Query Time: %.2f ns/op\n", avgQueryTime)
	fmt.Printf("Batches per second: %.0f batch/s\n", float64(len(batches))/batchDuration.Seconds())

	// Aggressively clean up memory before the next test
	instance = nil
	debug.FreeOSMemory()
}


