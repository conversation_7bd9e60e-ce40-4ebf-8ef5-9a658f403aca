package main

import "github.com/RoaringBitmap/roaring"

// InvertedIndexRoaring implements the inverted index strategy.
// It maps each location to a bitmap of TPLs that service it.
type InvertedIndexRoaring struct {
	originToTpls map[uint32]*roaring.Bitmap
	destToTpls   map[uint32]*roaring.Bitmap
}

// NewInvertedIndexRoaring builds the inverted index from the decoupled data source.
func NewInvertedIndexRoaring(data map[uint32]TplCoverage) *InvertedIndexRoaring {
	i := &InvertedIndexRoaring{
		originToTpls: make(map[uint32]*roaring.Bitmap),
		destToTpls:   make(map[uint32]*roaring.Bitmap),
	}

	// Iterate through each TPL and its coverage
	for tplID, coverage := range data {
		// Populate the origin-to-TPLs map
		for _, originID := range coverage.Origins {
			if _, ok := i.originToTpls[originID]; !ok {
				i.originToTpls[originID] = roaring.New()
			}
			i.originToTpls[originID].Add(tplID)
		}

		// Populate the dest-to-TPLs map
		for _, destID := range coverage.Dests {
			if _, ok := i.destToTpls[destID]; !ok {
				i.destToTpls[destID] = roaring.New()
			}
			i.destToTpls[destID].Add(tplID)
		}
	}
	return i
}

// IsServiceable checks for serviceability using the inverted index.
func (i *InvertedIndexRoaring) IsServiceable(tpl, origin, dest uint32) bool {
	originTpls, ok := i.originToTpls[origin]
	if !ok || !originTpls.Contains(tpl) {
		return false
	}

	destTpls, ok := i.destToTpls[dest]
	if !ok || !destTpls.Contains(tpl) {
		return false
	}

	return true
}
