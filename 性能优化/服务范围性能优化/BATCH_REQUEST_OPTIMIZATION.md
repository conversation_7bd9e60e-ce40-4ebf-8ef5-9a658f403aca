# 批量请求优化：模拟真实 Item Card 页面场景

## 改造背景

原有的测试数据生成逻辑使用单个 `ServicePair` 结构，不能很好地模拟真实的业务场景。在实际的电商系统中，用户在 Item Card 页面会看到多个商品，每个商品来自不同的卖家，需要批量查询物流服务可达性。

## 业务场景分析

### 真实场景特点
- **Item Card 页面**：一个买家（Buyer）浏览商品列表页面
- **批量查询**：页面显示 25 个商品，每个商品属于不同的卖家（Seller）
- **相同目的地**：所有查询的 DestID（买家位置）都相同
- **多个物流选项**：每个商品有 5 个可选的 3PL 物流商

### 数据结构设计

<augment_code_snippet path="性能优化/服务范围性能优化/data.go" mode="EXCERPT">
````go
// ItemRequest represents a single item in the batch request
type ItemRequest struct {
	ItemID   uint32   // Item identifier
	SellerID uint32   // Origin (Seller location)
	TplIDs   []uint32 // Available 3PLs for this item
}

// BatchServiceabilityRequest represents a batch request from Item Card page
type BatchServiceabilityRequest struct {
	BuyerID uint32        // Destination (Buyer location) - same for all items
	Items   []ItemRequest // List of items to check
}
````
</augment_code_snippet>

## 实现改进

### 1. 数据生成逻辑

<augment_code_snippet path="性能优化/服务范围性能优化/data.go" mode="EXCERPT">
````go
const (
	NumItemsPerPage = 25 // Number of items shown on one Item Card page
	Num3PLsPerItem  = 5  // Number of 3PLs available for each item
)

func generateBatchRequests(numRequests int) []BatchServiceabilityRequest {
	// Each request represents one Buyer viewing an Item Card page
	buyerID := rand.Uint32() % NumLocations
	
	// Generate 25 items for this page
	items := make([]ItemRequest, NumItemsPerPage)
	for j := 0; j < NumItemsPerPage; j++ {
		sellerID := rand.Uint32() % NumLocations
		// Randomly select 5 3PLs for this item
		availableTpls := make([]uint32, Num3PLsPerItem)
		// ...
	}
}
````
</augment_code_snippet>

### 2. 批量处理函数

<augment_code_snippet path="性能优化/服务范围性能优化/main.go" mode="EXCERPT">
````go
func processBatchRequest(instance *NativeBitVector, req BatchServiceabilityRequest) [][]bool {
	results := make([][]bool, len(req.Items))
	
	for i, item := range req.Items {
		itemResults := make([]bool, len(item.TplIDs))
		for j, tplID := range item.TplIDs {
			itemResults[j] = instance.IsServiceable(tplID, item.SellerID, req.BuyerID)
		}
		results[i] = itemResults
	}
	
	return results
}
````
</augment_code_snippet>

## 性能测试结果

### 批量请求基准测试

```
BenchmarkBatchNativeBitVector-16     	  311433	      3516 ns/op	     773 B/op	      26 allocs/op
BenchmarkBatchInvertedIndexMap-16    	  110064	     11494 ns/op	     773 B/op	      26 allocs/op
```

### 性能分析

**每个批量请求包含：**
- 25 个商品
- 每个商品 5 个 3PL 选项
- 总计 125 个查询

**NativeBitVector 性能：**
- 批量处理时间：3,516 ns
- 平均单次查询：28.1 ns (3516/125)
- 处理能力：284,000 批量请求/秒

**InvertedIndexMap 性能：**
- 批量处理时间：11,494 ns
- 平均单次查询：91.9 ns (11494/125)
- 处理能力：87,000 批量请求/秒

### 与单次查询对比

| 实现方式 | 单次查询 | 批量查询平均 | 性能提升 |
|---------|---------|-------------|---------|
| NativeBitVector | 33.6 ns | 28.1 ns | **16.4%** |
| InvertedIndexMap | 149.0 ns | 91.9 ns | **38.3%** |

## 业务价值

### 1. 真实场景模拟
- 准确反映 Item Card 页面的查询模式
- 相同买家位置，多个卖家位置的批量查询
- 符合实际业务流量特征

### 2. 性能优化机会
- 批量查询比单次查询有更好的缓存局部性
- 可以进一步优化为并行处理
- 为买家位置预计算结果的可能性

### 3. 系统设计指导
- 验证了不同数据结构在批量场景下的性能差异
- 为 API 设计提供了性能基准
- 指导缓存策略的制定

## 测试验证

### 功能测试
```go
func TestBatchProcessing(t *testing.T) {
	// 验证两种实现的结果一致性
	results1 := processBatchRequestBench(nativeBitVector, req)
	results2 := processBatchRequestMapBench(invertedIndexMap, req)
	// 结果完全一致 ✅
}
```

### 数据完整性
- 每个批量请求包含 25 个商品 ✅
- 每个商品有 5 个 3PL 选项 ✅
- 买家位置在同一批量请求中保持一致 ✅

## 结论

这次改造成功地将测试场景从简单的单次查询升级为真实的批量业务场景：

1. **业务贴合度提升**：准确模拟了 Item Card 页面的查询模式
2. **性能测试更准确**：批量查询展现了不同数据结构的真实性能差异
3. **优化方向明确**：NativeBitVector 在批量场景下表现更优，适合高并发场景
4. **扩展性良好**：为后续的并行处理、缓存优化等提供了基础

通过这次改造，我们不仅提升了测试的真实性，还发现了批量处理相比单次查询的性能优势，为系统架构设计提供了有价值的参考数据。
