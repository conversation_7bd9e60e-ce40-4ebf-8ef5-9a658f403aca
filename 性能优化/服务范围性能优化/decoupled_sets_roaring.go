package main

// DecoupledSetsMap stores origins and destinations in two independent (decoupled) maps for each TPL.
type DecoupledSetsMap struct {
	data map[uint32]*OriginDestMaps
}

// OriginDestMaps holds the two separate maps for origins and destinations.
type OriginDestMaps struct {
	Origins map[uint32]bool
	Dests   map[uint32]bool
}

// NewDecoupledSetsMap now efficiently consumes the decoupled data.
func NewDecoupledSetsMap(data map[uint32]TplCoverage) *DecoupledSetsMap {
	s := &DecoupledSetsMap{
		data: make(map[uint32]*OriginDestMaps),
	}

	for tplID, coverage := range data {
		originMap := make(map[uint32]bool, len(coverage.Origins))
		for _, originID := range coverage.Origins {
			originMap[originID] = true
		}

		destMap := make(map[uint32]bool, len(coverage.Dests))
		for _, destID := range coverage.Dests {
			destMap[destID] = true
		}

		s.data[tplID] = &OriginDestMaps{
			Origins: originMap,
			Dests:   destMap,
		}
	}
	return s
}

// IsServiceable checks if an origin and a destination are serviceable for a given TPL.
func (s *DecoupledSetsMap) IsServiceable(tpl, origin, dest uint32) bool {
	if maps, ok := s.data[tpl]; ok {
		return maps.Origins[origin] && maps.Dests[dest]
	}
	return false
}
