# NativeBitVector 方案详细设计文档

## 1. 方案概述

### 1.1. 目标

本文档旨在详细阐述 `NativeBitVector` 方案的设计与实现。该方案的目标是，在服务范围数据为“高密度”的特定场景下，实现一个内存占用最低、查询速度最快的服务范围查询解决方案。

### 1.2. 核心思想

方案的核心是**放弃通用库的抽象，回归硬件本身**。它通过以下两个关键技术点实现极致性能：

1.  **原生位图 (Plain Bit Vector)**: 使用Go语言原生的 `[]uint64` 切片作为位图，将服务范围数据编码为二进制位。这消除了所有第三方库的额外开销，实现了最紧凑的内存布局。
2.  **无分支查询 (Branchless Querying)**: 查询算法完全由纯粹的位运算构成，避免了 `if` 或 `&&` 等逻辑分支，从而消除了CPU分支预测失败带来的性能惩罚，使得查询延迟不仅低，而且极其稳定。

### 1.3. 适用场景

- **数据高度稠密**: 当覆盖率较高（例如 >30%）时，本方案的内存效率最高。
- **性能要求极致**: 适用于对单次查询延迟和内存占用有严苛要求的场景。

---

## 2. 数据结构定义

方案的核心数据结构定义如下：

```go
// NativeBitVector 使用一个原生 []uint64 切片作为位图。
type NativeBitVector struct {
	data map[uint32][]uint64
}
```

- `data map[uint32][]uint64`: 这是一个Go的map，用于存储每个物流商（TPL）的数据。
    - **键 (Key)**: `uint32` 类型的 `TplID`。
    - **值 (Value)**: `[]uint64` 类型的原生位图，包含了该TPL所有出发地和目的地的服务范围信息。

### 2.1. 关键常量

位图的大小由以下常量决定：

```go
const (
    // 地址ID的最大取值，例如 10000 或 20000
    NumLocations = 10000 

	// 每个位图需要存储出发地和目的地两部分信息
	// 因此总位数 = 2 * NumLocations
	// 每个uint64可以存储64位，所需uint64的数量为 ceil(总位数 / 64)
	bitVectorSize = (2*NumLocations + 63) / 64
)
```

例如，当 `NumLocations` 为10000时，`bitVectorSize` 为 `(20000 + 63) / 64 = 313`。

---

## 3. 索引空间设计

我们为每个TPL创建的位图，其索引空间被划分为两个连续的分区：

- **出发地区域 (Origin Partition)**
    - **地址范围**: `OriginID` in `[0, 9999]`
    - **位图索引**: `BitIndex = OriginID`

- **目的地区域 (Destination Partition)**
    - **地址范围**: `DestID` in `[0, 9999]`
    - **位图索引**: `BitIndex = DestID + NumLocations` (例如, `DestID + 10000`)

**索引空间示意图:**

```text
+-------------------------------------------------------------------------------------------------+
|                                   []uint64 Vector (Total 313 elements)                          |
|                                                                                                 |
|  Element 0                                                               Element 312            |
|  [uint64] [uint64] [uint64] ............................................. [uint64] [uint64]      |
+-------------------------------------------------------------------------------------------------+
  |                                                                                           |
  |                                                                                           |
+--------------------------------------------------+------------------------------------------+
|             Origin Partition                     |          Destination Partition           |
| (Covers bit indices 0 to 9999)                   | (Covers bit indices 10000 to 19999)      |
|                                                  |                                          |
| e.g., Origin ID `X` maps to bit index `X`        | e.g., Dest ID `Y` maps to bit index `Y + 10000` |
+--------------------------------------------------+------------------------------------------+
```

--- 

## 4. 构建逻辑详解 (`NewNativeBitVector`)

构建过程接收“解耦的”数据源 `map[uint32]TplCoverage`，并为每个TPL生成一个填充好的原生位图。

```go
// NewNativeBitVector 从解耦的数据源构建位图结构
func NewNativeBitVector(data map[uint32]TplCoverage) *NativeBitVector {
	n := &NativeBitVector{
		data: make(map[uint32][]uint64),
	}

	// 遍历每个TPL的数据
	for tplID, coverage := range data {
		// 为该TPL创建一个大小固定的空位图
		vector := make([]uint64, bitVectorSize)

		// 1. 设置所有出发地的位
		for _, originID := range coverage.Origins {
			wordIndex := originID / 64 // 计算在切片中的索引
			bitIndex := originID % 64  // 计算在uint64中的位索引
			// 使用“按位或”操作，将对应位置为1，且不影响其他位
			vector[wordIndex] |= (1 << bitIndex)
		}

		// 2. 设置所有目的地的位（使用偏移量）
		for _, destID := range coverage.Dests {
			index := destID + NumLocations // 计算在整个位图空间中的绝对索引
			wordIndex := index / 64
			bitIndex := index % 64
			vector[wordIndex] |= (1 << bitIndex)
		}

		n.data[tplID] = vector
	}

	return n
}
```

--- 

## 5. 查询逻辑详解 (`IsServiceable`)

查询过程通过纯粹的位运算，高效地检查出发地和目的地对应的位是否都为1。

```go
// IsServiceable 使用无分支逻辑在原生位图上执行查询
func (n *NativeBitVector) IsServiceable(tpl, origin, dest uint32) bool {
	// 1. 根据TPL ID获取对应的位图
	if vector, ok := n.data[tpl]; ok {

		// 2. 计算Origin的坐标
		originWordIndex := origin / 64
		originBitIndex := origin % 64

		// 3. 计算Dest的坐标（包含偏移量）
		destIndex := dest + NumLocations
		destWordIndex := destIndex / 64
		destBitIndex := destIndex % 64

		// 4. 提取Origin对应的位值（结果为0或1）
		//   a. vector[originWordIndex] -> 获取对应的uint64
		//   b. >> originBitIndex      -> 将目标位移到最右边
		//   c. & 1                    -> 屏蔽其他所有位，只保留目标位的值
		checkOrigin := (vector[originWordIndex] >> originBitIndex) & 1

		// 5. 提取Dest对应的位值（结果为0或1）
		checkDest := (vector[destWordIndex] >> destBitIndex) & 1

		// 6. 合并结果。只有在checkOrigin和checkDest都为1时，结果才为1
		return (checkOrigin & checkDest) == 1
	}

	// 如果TPL ID不存在，直接返回false
	return false
}
```

--- 

## 6. 部署与使用注意事项

1.  **数据密度**: 本方案为高密度数据（覆盖率 > 30%）设计。如果数据变为高度稀疏，`RoaringBitmap` 因其压缩能力，可能在内存上更具优势。
2.  **ID范围**: 本方案假设地址ID是固定的、从0开始的连续整数。如果ID范围非常大或不连续（如UUID），则不适合使用此方案。
3.  **并发安全**: 当前实现是只读安全的。如果在服务运行时需要动态修改服务范围（例如，添加/删除地址），则必须在 `data` map的读写操作周围增加并发控制（如 `sync.RWMutex`）。
