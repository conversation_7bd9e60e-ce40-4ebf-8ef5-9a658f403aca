# Redis 时效性缓存存储结构优化

本项目提供一个用于研究、评测和优化大规模时效性数据（如按分钟/小时聚合的指标）在 Redis 中存储方案的基准测试与开发环境。

## TL;DR
- **核心结论**: 聚合策略优先，再结合二进制压缩，可在保持`listpack`编码安全的同时，节省约 **60% ~ 80%** 的内存。
- **推荐策略**: `AggregationBinaryInt` (聚合 + 二进制Key + 整数Value)，在性能和压缩率之间取得了最佳平衡。
- **一键运行**: `make up && make demo`
- **一键评测**: `make benchmark`

## 目录结构
```
.
├── Makefile               # 自动化任务 (启动、测试、清理)
├── README.md              # 本文档
├── benchmark.go           # 所有优化策略实现与基准测试主程序
├── go.mod                 # Go模块依赖
├── docker-compose.yml     # Docker服务编排 (Redis, Grafana, etc.)
├── redis.conf             # Redis 配置文件
├── encoders/              # (示例) 优化器模块
├── examples/              # (示例) 优化方案调用示例
└── monitoring/            # 监控相关配置 (Prometheus, Grafana)
```

## 已实现优化策略 (共10种)

策略均在 `benchmark.go` 中实现，按优化递进程度分为五类：

1.  **基准策略**
    - `Original`: 原生字符串拼接，使用标准HashMap。

2.  **轻量级优化**
    - `ValueIntOptimized`: 仅对Value进行整数压缩。

3.  **Key编码优化 (不聚合)**
    - `Base62`: 对Key各部分进行Base62编码。
    - `BinaryPacked`: 将Key各部分打包成70位二进制，并用十六进制编码。
    - `Binary64Bit`: 将Key各部分极限压缩到60位二进制，并用十六进制编码。

4.  **结构层优化 (聚合)**
    - `ShopLocationAggregation`: 按店铺和地址聚合，大幅减少Key数量。
    - `AggregationBinaryPack`: 在聚合基础上，对Key进行二进制压缩。
    - `AggregationBinaryInt`: 在聚合基础上，对Key、Field、Value进行全面二进制与整数压缩。

5.  **KV结构与高级压缩**
    - `KVBinaryPBList`: 采用KV结构，Key为聚合二进制，Value为Protobuf序列化的数据列表。
    - `KVBinaryBitPack`: 采用KV结构，Key为8字节二进制，Value为自定义的位打包二进制序列，压缩率最高。

## 快速开始 (推荐 Docker)
```bash
# 启动 Redis 服务
make up

# 运行一个简单的优化示例
make demo
```

## 基准测试
```bash
# 运行标准规模的基准测试 (对比所有10种策略)
make benchmark

# 运行大数据量规模的测试 (耗时更长)
make benchmark-large
```
测试报告会自动生成在项目根目录，例如 `benchmark_report_*.md`。

## 推荐实践
- **优先聚合**: 对于多维度数据，优先按业务核心实体（如店铺、用户）进行聚合，这是减少Key数量、降低内存开销最有效的手段。
- **评估稀疏性**: 如果你的数据维度（如本文的Channel）是稀疏的，请重点评估**高级理论方案(Bitfield)**的适用性。
- **全面压缩**: 在聚合后，对Key、Field、Value进行全面的二进制打包和整数压缩，能极大压缩单Key的体积。
- **善用Pipeline**: 所有涉及多条命令的写入和读取，都应使用Pipeline来降低网络开-销。

## 监控与辅助命令
```bash
# 进入 Redis CLI
make redis-cli

# 清空 Redis 数据
make flush

# 停止并清理所有 Docker 容器
make down
```