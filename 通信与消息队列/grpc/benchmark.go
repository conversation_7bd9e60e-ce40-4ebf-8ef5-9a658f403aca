package main

import (
	"benchmark/pb/Flat"
	"bytes"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"time"

	"benchmark/pb"
	flatbuffers "github.com/google/flatbuffers/go"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// 用于序列化/反序列化的结构体
type Address struct {
	Street     string  `json:"street" bson:"street"`
	City       string  `json:"city" bson:"city"`
	State      string  `json:"state" bson:"state"`
	Country    string  `json:"country" bson:"country"`
	PostalCode string  `json:"postal_code" bson:"postal_code"`
	IsCurrent  bool    `json:"is_current" bson:"is_current"`
	Latitude   float32 `json:"latitude" bson:"latitude"`
	Longitude  float64 `json:"longitude" bson:"longitude"`
}

type Scores struct {
	Math      float64            `json:"math" bson:"math"`
	Physics   float64            `json:"physics" bson:"physics"`
	Chemistry float64            `json:"chemistry" bson:"chemistry"`
	Electives map[string]float64 `json:"electives" bson:"electives"`
}

type Person struct {
	Name            string            `json:"name" bson:"name"`
	Age             int32             `json:"age" bson:"age"`
	Email           string            `json:"email" bson:"email"`
	Addresses       []Address         `json:"addresses" bson:"addresses"`
	PhoneNumbers    []string          `json:"phone_numbers" bson:"phone_numbers"`
	IsStudent       bool              `json:"is_student" bson:"is_student"`
	Height          float32           `json:"height" bson:"height"`
	Weight          float64           `json:"weight" bson:"weight"`
	Avatar          []byte            `json:"avatar" bson:"avatar"`
	BirthDate       time.Time         `json:"birth_date" bson:"birth_date"`
	FavoriteNumbers []int64           `json:"favorite_numbers" bson:"favorite_numbers"`
	Metadata        map[string]string `json:"metadata" bson:"metadata"`
	AcademicScores  Scores            `json:"academic_scores" bson:"academic_scores"`
}

func main() {
	fmt.Println("开始性能测试...")
	jsonStats, gobStats, protobufStats, bsonStats, flatbuffersStats := measurePerformance(100000)

	fmt.Println("\n性能测试结果:")
	fmt.Printf("JSON 序列化: %v\n", jsonStats.marshalTime)
	fmt.Printf("JSON 反序列化: %v\n", jsonStats.unmarshalTime)
	fmt.Printf("GOB 序列化: %v\n", gobStats.marshalTime)
	fmt.Printf("GOB 反序列化: %v\n", gobStats.unmarshalTime)
	fmt.Printf("Protobuf 序列化: %v\n", protobufStats.marshalTime)
	fmt.Printf("Protobuf 反序列化: %v\n", protobufStats.unmarshalTime)
	fmt.Printf("BSON 序列化: %v\n", bsonStats.marshalTime)
	fmt.Printf("BSON 反序列化: %v\n", bsonStats.unmarshalTime)
	fmt.Printf("FlatBuffers 序列化: %v\n", flatbuffersStats.marshalTime)
	fmt.Printf("FlatBuffers 反序列化: %v\n", flatbuffersStats.unmarshalTime)
	fmt.Println("\n性能报告已生成: benchmark_report.md")
}

func createTestData() *Person {
	// 创建测试用的元数据
	metadata := make(map[string]string)
	metadata["source"] = "registration"
	metadata["created_by"] = "admin"
	metadata["department"] = "computer science"

	// 创建选修课分数
	electives := make(map[string]float64)
	electives["biology"] = 92.5
	electives["history"] = 88.0
	electives["literature"] = 95.0

	// 创建测试用头像（简单的字节数组）
	avatar := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52}

	return &Person{
		Name:  "张三",
		Age:   30,
		Email: "<EMAIL>",
		Addresses: []Address{
			{
				Street:     "北京路123号",
				City:       "上海",
				State:      "上海",
				Country:    "中国",
				PostalCode: "200001",
				IsCurrent:  true,
				Latitude:   31.2304,
				Longitude:  121.4737,
			},
			{
				Street:     "南京西路456号",
				City:       "上海",
				State:      "上海",
				Country:    "中国",
				PostalCode: "200002",
				IsCurrent:  false,
				Latitude:   31.2257,
				Longitude:  121.4692,
			},
		},
		PhoneNumbers:    []string{"13800138000", "13900139000"},
		IsStudent:       true,
		Height:          175.5,
		Weight:          68.5,
		Avatar:          avatar,
		BirthDate:       time.Date(1992, 5, 15, 0, 0, 0, 0, time.UTC),
		FavoriteNumbers: []int64{7, 42, 99, 128, 256, 1024},
		Metadata:        metadata,
		AcademicScores: Scores{
			Math:      95.5,
			Physics:   88.0,
			Chemistry: 92.5,
			Electives: electives,
		},
	}
}

// 创建Protocol Buffer测试数据
func createProtobufTestData() *pb.Person {
	// 创建测试用的元数据
	metadata := make(map[string]string)
	metadata["source"] = "registration"
	metadata["created_by"] = "admin"
	metadata["department"] = "computer science"

	// 创建选修课分数
	electives := make(map[string]float64)
	electives["biology"] = 92.5
	electives["history"] = 88.0
	electives["literature"] = 95.0

	// 创建测试用头像（简单的字节数组）
	avatar := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52}

	birthDate := timestamppb.New(time.Date(1992, 5, 15, 0, 0, 0, 0, time.UTC))

	return &pb.Person{
		Name:  "张三",
		Age:   30,
		Email: "<EMAIL>",
		Addresses: []*pb.Address{
			{
				Street:     "北京路123号",
				City:       "上海",
				State:      "上海",
				Country:    "中国",
				PostalCode: "200001",
				IsCurrent:  true,
				Latitude:   31.2304,
				Longitude:  121.4737,
			},
			{
				Street:     "南京西路456号",
				City:       "上海",
				State:      "上海",
				Country:    "中国",
				PostalCode: "200002",
				IsCurrent:  false,
				Latitude:   31.2257,
				Longitude:  121.4692,
			},
		},
		PhoneNumbers:    []string{"13800138000", "13900139000"},
		IsStudent:       true,
		Height:          175.5,
		Weight:          68.5,
		Avatar:          avatar,
		BirthDate:       birthDate,
		FavoriteNumbers: []int64{7, 42, 99, 128, 256, 1024},
		Metadata:        metadata,
		AcademicScores: &pb.Scores{
			Math:      95.5,
			Physics:   88.0,
			Chemistry: 92.5,
			Electives: electives,
		},
	}
}

// 使用JSON序列化
func marshalJSON(data *Person) ([]byte, error) {
	return json.Marshal(data)
}

// 使用JSON反序列化
func unmarshalJSON(data []byte) (*Person, error) {
	var p Person
	err := json.Unmarshal(data, &p)
	return &p, err
}

// 使用GOB序列化
func marshalGOB(data *Person) ([]byte, error) {
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	err := enc.Encode(data)
	return buf.Bytes(), err
}

// 使用GOB反序列化
func unmarshalGOB(data []byte) (*Person, error) {
	var p Person
	buf := bytes.NewBuffer(data)
	dec := gob.NewDecoder(buf)
	err := dec.Decode(&p)
	return &p, err
}

// 使用Protobuf序列化
func marshalProtobuf(data *pb.Person) ([]byte, error) {
	return proto.Marshal(data)
}

// 使用Protobuf反序列化
func unmarshalProtobuf(data []byte, p *pb.Person) error {
	return proto.Unmarshal(data, p)
}

// 使用BSON序列化
func marshalBSON(data *Person) ([]byte, error) {
	return bson.Marshal(data)
}

// 使用BSON反序列化
func unmarshalBSON(data []byte) (*Person, error) {
	var p Person
	err := bson.Unmarshal(data, &p)
	return &p, err
}

// createFlatbuffersPerson создает FlatBuffers Person из main.Person
func createFlatbuffersPerson(builder *flatbuffers.Builder, p *Person) flatbuffers.UOffsetT {
	name := builder.CreateString(p.Name)
	email := builder.CreateString(p.Email)

	// Addresses
	addressOffsets := make([]flatbuffers.UOffsetT, len(p.Addresses))
	for i, addr := range p.Addresses {
		street := builder.CreateString(addr.Street)
		city := builder.CreateString(addr.City)
		state := builder.CreateString(addr.State)
		country := builder.CreateString(addr.Country)
		postalCode := builder.CreateString(addr.PostalCode)
		Flat.AddressStart(builder)
		Flat.AddressAddStreet(builder, street)
		Flat.AddressAddCity(builder, city)
		Flat.AddressAddState(builder, state)
		Flat.AddressAddCountry(builder, country)
		Flat.AddressAddPostalCode(builder, postalCode)
		Flat.AddressAddIsCurrent(builder, addr.IsCurrent)
		Flat.AddressAddLatitude(builder, addr.Latitude)
		Flat.AddressAddLongitude(builder, addr.Longitude)
		addressOffsets[i] = Flat.AddressEnd(builder)
	}
	Flat.PersonStartAddressesVector(builder, len(addressOffsets))
	for i := len(addressOffsets) - 1; i >= 0; i-- {
		builder.PrependUOffsetT(addressOffsets[i])
	}
	addressesVec := builder.EndVector(len(addressOffsets))

	// PhoneNumbers
	phoneNumberOffsets := make([]flatbuffers.UOffsetT, len(p.PhoneNumbers))
	for i, pn := range p.PhoneNumbers {
		phoneNumberOffsets[i] = builder.CreateString(pn)
	}
	Flat.PersonStartPhoneNumbersVector(builder, len(phoneNumberOffsets))
	for i := len(phoneNumberOffsets) - 1; i >= 0; i-- {
		builder.PrependUOffsetT(phoneNumberOffsets[i])
	}
	phoneNumbersVec := builder.EndVector(len(phoneNumberOffsets))

	// Avatar
	avatarVec := builder.CreateByteVector(p.Avatar)

	// FavoriteNumbers
	Flat.PersonStartFavoriteNumbersVector(builder, len(p.FavoriteNumbers))
	for i := len(p.FavoriteNumbers) - 1; i >= 0; i-- {
		builder.PrependInt64(p.FavoriteNumbers[i])
	}
	favNumbersVec := builder.EndVector(len(p.FavoriteNumbers))

	// Metadata
	metadataEntryOffsets := make([]flatbuffers.UOffsetT, 0, len(p.Metadata))
	for k, v := range p.Metadata {
		key := builder.CreateString(k)
		value := builder.CreateString(v)
		Flat.MetadataEntryStart(builder)
		Flat.MetadataEntryAddKey(builder, key)
		Flat.MetadataEntryAddValue(builder, value)
		metadataEntryOffsets = append(metadataEntryOffsets, Flat.MetadataEntryEnd(builder))
	}
	Flat.PersonStartMetadataVector(builder, len(metadataEntryOffsets))
	for i := len(metadataEntryOffsets) - 1; i >= 0; i-- {
		builder.PrependUOffsetT(metadataEntryOffsets[i])
	}
	metadataVec := builder.EndVector(len(metadataEntryOffsets))

	// AcademicScores
	// Electives for AcademicScores
	electiveScoreOffsets := make([]flatbuffers.UOffsetT, 0, len(p.AcademicScores.Electives))
	for k, v := range p.AcademicScores.Electives {
		key := builder.CreateString(k)
		Flat.ElectiveScoreStart(builder)
		Flat.ElectiveScoreAddKey(builder, key)
		Flat.ElectiveScoreAddValue(builder, v)
		electiveScoreOffsets = append(electiveScoreOffsets, Flat.ElectiveScoreEnd(builder))
	}
	Flat.ScoresStartElectivesVector(builder, len(electiveScoreOffsets))
	for i := len(electiveScoreOffsets) - 1; i >= 0; i-- {
		builder.PrependUOffsetT(electiveScoreOffsets[i])
	}
	electivesVec := builder.EndVector(len(electiveScoreOffsets))

	Flat.ScoresStart(builder)
	Flat.ScoresAddMath(builder, p.AcademicScores.Math)
	Flat.ScoresAddPhysics(builder, p.AcademicScores.Physics)
	Flat.ScoresAddChemistry(builder, p.AcademicScores.Chemistry)
	Flat.ScoresAddElectives(builder, electivesVec)
	scoresOffset := Flat.ScoresEnd(builder)

	// Person
	Flat.PersonStart(builder)
	Flat.PersonAddName(builder, name)
	Flat.PersonAddAge(builder, p.Age)
	Flat.PersonAddEmail(builder, email)
	Flat.PersonAddAddresses(builder, addressesVec)
	Flat.PersonAddPhoneNumbers(builder, phoneNumbersVec)
	Flat.PersonAddIsStudent(builder, p.IsStudent)
	Flat.PersonAddHeight(builder, p.Height)
	Flat.PersonAddWeight(builder, p.Weight)
	Flat.PersonAddAvatar(builder, avatarVec)
	Flat.PersonAddBirthDate(builder, p.BirthDate.UnixNano())
	Flat.PersonAddFavoriteNumbers(builder, favNumbersVec)
	Flat.PersonAddMetadata(builder, metadataVec)
	Flat.PersonAddAcademicScores(builder, scoresOffset)
	return Flat.PersonEnd(builder)
}

// 使用FlatBuffers序列化
func marshalFlatbuffers(personData *Person) ([]byte, error) {
	builder := flatbuffers.NewBuilder(1024) // 初始缓冲区大小
	personOffset := createFlatbuffersPerson(builder, personData)
	builder.Finish(personOffset)
	return builder.FinishedBytes(), nil
}

// 使用FlatBuffers反序列化
func unmarshalFlatbuffers(data []byte, fbPerson *Flat.Person) error {
	// FlatBuffers 通常不需要显式的"反序列化"步骤来访问数据，
	// GetRootAsPerson 会完成这个工作。为了与其他测试保持一致，我们将使用此函数。
	// 在实际应用中，可以传递 `data` 并根据需要通过 `GetRootAsPerson` 进行操作。
	person := Flat.GetRootAsPerson(data, 0)
	// 如果基准测试需要模拟实际使用（例如，复制字段），则可以在此处进行。
	// 在这种情况下，*fbPerson 已经指向 `data` 中的数据。
	// 对于此基准测试，仅获取根对象就足够了。
	// 为了与返回新对象的其他 unmarshal 函数的签名保持一致，我们将进行赋值。
	*fbPerson = *person // 如果 fbPerson 不为 nil 且已解引用，这将导致复制。
	// 注意：GetRootAsPerson 不会创建深拷贝。它提供对 `data` 中数据的访问权限。
	// 对于性能测试来说，这是正常的。

	// 为了使其更类似于其他返回新对象的 unmarshal 函数：
	_ = Flat.GetRootAsPerson(data, 0) // 这个操作本身就是"反序列化"
	return nil
}

// 测量序列化/反序列化性能
func measurePerformance(iterations int) (jsonStats, gobStats, protobufStats, bsonStats, flatbuffersStats struct{ marshalTime, unmarshalTime time.Duration }) {
	person := createTestData()
	pbPerson := createProtobufTestData()
	var fbPerson Flat.Person // 用于 FlatBuffers 反序列化

	// JSON性能测试
	jsonStart := time.Now()
	var jsonData []byte
	for i := 0; i < iterations; i++ {
		var err error
		jsonData, err = marshalJSON(person)
		if err != nil {
			fmt.Printf("JSON marshal错误: %v\n", err)
			return
		}
	}
	jsonStats.marshalTime = time.Since(jsonStart) / time.Duration(iterations)

	jsonStartUnmarshal := time.Now()
	for i := 0; i < iterations; i++ {
		_, err := unmarshalJSON(jsonData)
		if err != nil {
			fmt.Printf("JSON unmarshal错误: %v\n", err)
			return
		}
	}
	jsonStats.unmarshalTime = time.Since(jsonStartUnmarshal) / time.Duration(iterations)

	// GOB性能测试
	gobStart := time.Now()
	var gobData []byte
	for i := 0; i < iterations; i++ {
		var err error
		gobData, err = marshalGOB(person)
		if err != nil {
			fmt.Printf("GOB marshal错误: %v\n", err)
			return
		}
	}
	gobStats.marshalTime = time.Since(gobStart) / time.Duration(iterations)

	gobStartUnmarshal := time.Now()
	for i := 0; i < iterations; i++ {
		_, err := unmarshalGOB(gobData)
		if err != nil {
			fmt.Printf("GOB unmarshal错误: %v\n", err)
			return
		}
	}
	gobStats.unmarshalTime = time.Since(gobStartUnmarshal) / time.Duration(iterations)

	// Protobuf性能测试
	pbStart := time.Now()
	var pbData []byte
	for i := 0; i < iterations; i++ {
		var err error
		pbData, err = marshalProtobuf(pbPerson)
		if err != nil {
			fmt.Printf("Protobuf marshal错误: %v\n", err)
			return
		}
	}
	protobufStats.marshalTime = time.Since(pbStart) / time.Duration(iterations)

	pbStartUnmarshal := time.Now()
	for i := 0; i < iterations; i++ {
		p := &pb.Person{} // Protobuf 反序列化需要结构体指针
		err := unmarshalProtobuf(pbData, p)
		if err != nil {
			fmt.Printf("Protobuf unmarshal错误: %v\n", err)
			return
		}
	}
	protobufStats.unmarshalTime = time.Since(pbStartUnmarshal) / time.Duration(iterations)

	// BSON性能测试
	bsonStart := time.Now()
	var bsonData []byte
	for i := 0; i < iterations; i++ {
		var err error
		bsonData, err = marshalBSON(person)
		if err != nil {
			fmt.Printf("BSON marshal错误: %v\n", err)
			return
		}
	}
	bsonStats.marshalTime = time.Since(bsonStart) / time.Duration(iterations)

	bsonStartUnmarshal := time.Now()
	for i := 0; i < iterations; i++ {
		_, err := unmarshalBSON(bsonData)
		if err != nil {
			fmt.Printf("BSON unmarshal错误: %v\n", err)
			return
		}
	}
	bsonStats.unmarshalTime = time.Since(bsonStartUnmarshal) / time.Duration(iterations)

	// FlatBuffers性能测试
	fbStart := time.Now()
	var fbData []byte
	// FlatBuffers builder 可以通过 Reset() 重用，但为简单起见，这里每次都创建一个新的。
	// builder := flatbuffers.NewBuilder(1024)
	for i := 0; i < iterations; i++ {
		var err error
		// builder.Reset() // 如果重用 builder
		fbData, err = marshalFlatbuffers(person) // marshalFlatbuffers 会创建自己的 builder
		if err != nil {
			fmt.Printf("FlatBuffers marshal错误: %v\n", err)
			return
		}
	}
	flatbuffersStats.marshalTime = time.Since(fbStart) / time.Duration(iterations)

	fbStartUnmarshal := time.Now()
	for i := 0; i < iterations; i++ {
		// fbPerson 在循环前初始化一次
		err := unmarshalFlatbuffers(fbData, &fbPerson) // 传递指针
		if err != nil {
			fmt.Printf("FlatBuffers unmarshal错误: %v\n", err)
			return
		}
	}
	flatbuffersStats.unmarshalTime = time.Since(fbStartUnmarshal) / time.Duration(iterations)

	// 获取基准测试结果
	benchOutput := runBenchmarkTest()

	// 输出数据大小比较
	jsonPretty, _ := json.MarshalIndent(person, "", "  ")

	fmt.Printf("JSON数据大小: %d 字节\n", len(jsonData))
	fmt.Printf("美化JSON数据大小: %d 字节\n", len(jsonPretty))
	fmt.Printf("GOB数据大小: %d 字节\n", len(gobData))
	fmt.Printf("Protobuf数据大小: %d 字节\n", len(pbData))
	fmt.Printf("BSON数据大小: %d 字节\n", len(bsonData))
	fmt.Printf("FlatBuffers数据大小: %d 字节\n", len(fbData))

	// 生成报告
	report := fmt.Sprintf(`# JSON、GOB、Protocol Buffer、BSON与FlatBuffers性能对比报告

## 数据结构说明

本测试使用了包含多种数据类型的复杂结构体，包括：
- 字符串、整数、浮点数(float32/float64)
- 布尔值
- 二进制数据([]byte)
- 日期时间
- 数组(repeated)
- Map类型
- 嵌套结构体

## 数据大小比较

| 格式            | 大小 (字节) |
|-----------------|------------|
| JSON            | %d         |
| 美化JSON        | %d         |
| GOB(二进制)     | %d         |
| Protocol Buffer | %d         |
| BSON            | %d         |
| FlatBuffers     | %d         |

## 序列化性能比较

| 格式            | 序列化时间 | 反序列化时间 |
|-----------------|------------|------------|
| JSON            | %v         | %v         |
| GOB(二进制)     | %v         | %v         |
| Protocol Buffer | %v         | %v         |
| BSON            | %v         | %v         |
| FlatBuffers     | %v         | %v         |

## 完整基准测试结果

%s

## JSON序列化数据示例

%s

## 结论

1. **在数据大小方面**：
   - Protocol Buffer数据大小为%d字节
   - JSON数据大小为%d字节
   - 格式化的JSON数据大小为%d字节
   - GOB数据大小为%d字节
   - BSON数据大小为%d字节
   - FlatBuffers数据大小为%d字节

2. **在序列化性能方面**：
   - Protocol Buffer序列化时间为%v，反序列化时间为%v
   - JSON序列化时间为%v，反序列化时间为%v
   - GOB序列化时间为%v，反序列化时间为%v
   - BSON序列化时间为%v，反序列化时间为%v
   - FlatBuffers序列化时间为%v，反序列化时间为%v

3. **各序列化格式的特点**：
   - **JSON**: 
     - 优点：可读性好，广泛支持，不需要预定义schema
     - 缺点：数据大小较大，性能一般
   - **Protocol Buffer**:
     - 优点：高性能，数据紧凑，强类型支持，跨语言
     - 缺点：需要预定义schema，可读性差
   - **GOB**:
     - 优点：Go语言原生支持，不需要额外依赖
     - 缺点：仅限Go语言使用，性能一般
   - **BSON**:
     - 优点：类似JSON的文档模型，支持更多数据类型 (如日期, 二进制)，MongoDB原生格式
     - 缺点：比Protocol Buffer/FlatBuffers体积大，性能通常介于JSON和二进制格式之间
   - **FlatBuffers**:
     - 优点：极高性能 (尤其反序列化，可直接访问数据无需解析)，数据紧凑，内存高效，跨语言，向前/向后兼容性好
     - 缺点：需要预定义schema，序列化代码相对复杂，可读性差

4. **随着数据复杂度增加的表现**：
   - 在包含多种数据类型的复杂结构中，Protocol Buffer和FlatBuffers依然保持其高效性
   - 复杂数据结构下，Protocol Buffer/FlatBuffers与JSON的性能差距更为明显
   - 如对象中包含嵌套结构、map等复杂类型时，Protocol Buffer和FlatBuffers的优势更为突出
`,
		len(jsonData), len(jsonPretty), len(gobData), len(pbData), len(bsonData), len(fbData),
		jsonStats.marshalTime, jsonStats.unmarshalTime,
		gobStats.marshalTime, gobStats.unmarshalTime,
		protobufStats.marshalTime, protobufStats.unmarshalTime,
		bsonStats.marshalTime, bsonStats.unmarshalTime,
		flatbuffersStats.marshalTime, flatbuffersStats.unmarshalTime,
		benchOutput,
		string(jsonPretty),
		len(pbData), len(jsonData), len(jsonPretty), len(gobData), len(bsonData), len(fbData),
		protobufStats.marshalTime, protobufStats.unmarshalTime,
		jsonStats.marshalTime, jsonStats.unmarshalTime,
		gobStats.marshalTime, gobStats.unmarshalTime,
		bsonStats.marshalTime, bsonStats.unmarshalTime,
		flatbuffersStats.marshalTime, flatbuffersStats.unmarshalTime)

	err := os.WriteFile("benchmark_report.md", []byte(report), 0644)
	if err != nil {
		fmt.Printf("写入报告错误: %v\n", err)
	}

	return jsonStats, gobStats, protobufStats, bsonStats, flatbuffersStats
}

// 运行基准测试并返回结果
func runBenchmarkTest() string {
	cmd := exec.Command("go", "test", "-bench=.", "-benchmem")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Sprintf("运行基准测试失败: %v\n%s", err, output)
	}

	// 美化输出
	result := "```\n" + string(output) + "```\n\n"
	result += "### 性能说明\n\n"
	result += "* ns/op: 每次操作的纳秒数（越低越好）\n"
	result += "* B/op: 每次操作分配的字节数（越低越好）\n"
	result += "* allocs/op: 每次操作的内存分配次数（越低越好）\n\n"
	result += "从基准测试结果可以看出各种序列化方法在处理复杂数据结构时的性能差异。"

	return result
}
