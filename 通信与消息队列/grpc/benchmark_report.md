# JSO<PERSON>、GOB、<PERSON> Buffer、BSON与FlatBuffers性能对比报告

## 数据结构说明

本测试使用了包含多种数据类型的复杂结构体，包括：
- 字符串、整数、浮点数(float32/float64)
- 布尔值
- 二进制数据([]byte)
- 日期时间
- 数组(repeated)
- Map类型
- 嵌套结构体

## 数据大小比较

| 格式            | 大小 (字节) |
|-----------------|------------|
| JSON            | 816         |
| 美化JSON        | 1143         |
| GOB(二进制)     | 940         |
| Protocol Buffer | 415         |
| BSON            | 890         |
| FlatBuffers     | 872         |

## 序列化性能比较

| 格式            | 序列化时间 | 反序列化时间 |
|-----------------|------------|------------|
| JSON            | 4.478µs         | 13.188µs         |
| GOB(二进制)     | 13.024µs         | 38.951µs         |
| Protocol Buffer | 3.477µs         | 4.345µs         |
| BSON            | 5.775µs         | 10.566µs         |
| FlatBuffers     | 2.039µs         | 4ns         |

## 完整基准测试结果

```
goos: darwin
goarch: amd64
pkg: benchmark
cpu: Intel(R) Core(TM) i9-9880H CPU @ 2.30GHz
BenchmarkJSONMarshal-16             	  275810	      4121 ns/op	    1369 B/op	      16 allocs/op
BenchmarkJSONUnmarshal-16           	   95463	     12741 ns/op	    1976 B/op	      52 allocs/op
BenchmarkGOBMarshal-16              	   88503	     14170 ns/op	    6856 B/op	     101 allocs/op
BenchmarkGOBUnmarshal-16            	   32307	     39891 ns/op	   15870 B/op	     413 allocs/op
BenchmarkProtobufMarshal-16         	  352410	      3309 ns/op	     752 B/op	      25 allocs/op
BenchmarkProtobufUnmarshal-16       	  279903	      5098 ns/op	    2056 B/op	      65 allocs/op
BenchmarkBSONMarshal-16             	  185037	      6107 ns/op	    1298 B/op	      18 allocs/op
BenchmarkBSONUnmarshal-16           	  109810	     10610 ns/op	    3658 B/op	     129 allocs/op
BenchmarkFlatbuffersMarshal-16      	  715017	      1837 ns/op	      48 B/op	       4 allocs/op
BenchmarkFlatbuffersUnmarshal-16    	1000000000	         0.5164 ns/op	       0 B/op	       0 allocs/op
PASS
ok  	benchmark	14.567s
```

### 性能说明

* ns/op: 每次操作的纳秒数（越低越好）
* B/op: 每次操作分配的字节数（越低越好）
* allocs/op: 每次操作的内存分配次数（越低越好）

从基准测试结果可以看出各种序列化方法在处理复杂数据结构时的性能差异。

## JSON序列化数据示例

{
  "name": "张三",
  "age": 30,
  "email": "<EMAIL>",
  "addresses": [
    {
      "street": "北京路123号",
      "city": "上海",
      "state": "上海",
      "country": "中国",
      "postal_code": "200001",
      "is_current": true,
      "latitude": 31.2304,
      "longitude": 121.4737
    },
    {
      "street": "南京西路456号",
      "city": "上海",
      "state": "上海",
      "country": "中国",
      "postal_code": "200002",
      "is_current": false,
      "latitude": 31.2257,
      "longitude": 121.4692
    }
  ],
  "phone_numbers": [
    "13800138000",
    "13900139000"
  ],
  "is_student": true,
  "height": 175.5,
  "weight": 68.5,
  "avatar": "iVBORw0KGgoAAAANSUhEUg==",
  "birth_date": "1992-05-15T00:00:00Z",
  "favorite_numbers": [
    7,
    42,
    99,
    128,
    256,
    1024
  ],
  "metadata": {
    "created_by": "admin",
    "department": "computer science",
    "source": "registration"
  },
  "academic_scores": {
    "math": 95.5,
    "physics": 88,
    "chemistry": 92.5,
    "electives": {
      "biology": 92.5,
      "history": 88,
      "literature": 95
    }
  }
}

## 结论

1. **在数据大小方面**：
   - Protocol Buffer数据大小为415字节
   - JSON数据大小为816字节
   - 格式化的JSON数据大小为1143字节
   - GOB数据大小为940字节
   - BSON数据大小为890字节
   - FlatBuffers数据大小为872字节

2. **在序列化性能方面**：
   - Protocol Buffer序列化时间为3.477µs，反序列化时间为4.345µs
   - JSON序列化时间为4.478µs，反序列化时间为13.188µs
   - GOB序列化时间为13.024µs，反序列化时间为38.951µs
   - BSON序列化时间为5.775µs，反序列化时间为10.566µs
   - FlatBuffers序列化时间为2.039µs，反序列化时间为4ns

3. **各序列化格式的特点**：
   - **JSON**: 
     - 优点：可读性好，广泛支持，不需要预定义schema
     - 缺点：数据大小较大，性能一般
   - **Protocol Buffer**:
     - 优点：高性能，数据紧凑，强类型支持，跨语言
     - 缺点：需要预定义schema，可读性差
   - **GOB**:
     - 优点：Go语言原生支持，不需要额外依赖
     - 缺点：仅限Go语言使用，性能一般
   - **BSON**:
     - 优点：类似JSON的文档模型，支持更多数据类型 (如日期, 二进制)，MongoDB原生格式
     - 缺点：比Protocol Buffer/FlatBuffers体积大，性能通常介于JSON和二进制格式之间
   - **FlatBuffers**:
     - 优点：极高性能 (尤其反序列化，可直接访问数据无需解析)，数据紧凑，内存高效，跨语言，向前/向后兼容性好
     - 缺点：需要预定义schema，序列化代码相对复杂，可读性差

4. **随着数据复杂度增加的表现**：
   - 在包含多种数据类型的复杂结构中，Protocol Buffer和FlatBuffers依然保持其高效性
   - 复杂数据结构下，Protocol Buffer/FlatBuffers与JSON的性能差距更为明显
   - 如对象中包含嵌套结构、map等复杂类型时，Protocol Buffer和FlatBuffers的优势更为突出
