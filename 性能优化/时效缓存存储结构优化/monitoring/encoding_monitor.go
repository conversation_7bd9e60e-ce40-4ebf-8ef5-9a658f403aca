package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"redis-key-optimization/encoders"

	"github.com/go-redis/redis/v8"
)

// MonitoringServer 监控服务器
type MonitoringServer struct {
	client  *redis.Client
	monitor *encoders.EncodingMonitor
}

func main() {
	fmt.Println("🔍 Redis编码监控系统启动")

	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   0,
	})
	defer rdb.Close()

	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Fatal("Redis连接失败:", err)
	}

	// 创建监控服务器
	server := &MonitoringServer{
		client:  rdb,
		monitor: encoders.NewEncodingMonitor(rdb),
	}

	// 启动定时监控
	go server.startPeriodicMonitoring(ctx)

	// 启动HTTP服务
	server.startHTTPServer()
}

func (s *MonitoringServer) startPeriodicMonitoring(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.performHealthCheck(ctx)
		case <-ctx.Done():
			return
		}
	}
}

func (s *MonitoringServer) performHealthCheck(ctx context.Context) {
	fmt.Printf("\n🔍 [%s] 执行编码健康检查\n", time.Now().Format("15:04:05"))

	// 检查不同类型的key
	patterns := []string{
		"shop:*",    // 店铺聚合key
		"time:*",    // 时间聚合key
		"*_*_*_*_*", // 原始HashMap key
	}

	for _, pattern := range patterns {
		stats, err := s.monitor.GetEncodingStats(ctx, pattern)
		if err != nil {
			log.Printf("获取统计信息失败 [%s]: %v", pattern, err)
			continue
		}

		s.analyzeAndAlert(pattern, stats)
	}
}

func (s *MonitoringServer) analyzeAndAlert(pattern string, stats encoders.EncodingStats) {
	fmt.Printf("📊 模式: %s\n", pattern)
	fmt.Printf("   总Key数: %d\n", stats.TotalKeys)
	fmt.Printf("   listpack: %d, hashtable: %d\n", stats.ListpackCount, stats.HashtableCount)
	fmt.Printf("   平均Field数: %.1f\n", stats.AvgFieldCount)
	fmt.Printf("   平均内存/Key: %.1fKB\n", stats.AvgMemoryPerKey/1024)

	// 告警检查
	if stats.HashtableCount > 0 {
		alertRatio := float64(stats.HashtableCount) / float64(stats.TotalKeys) * 100
		fmt.Printf("   ⚠️  编码告警: %.1f%% 的Key使用hashtable编码！\n", alertRatio)

		if alertRatio > 10 { // 超过10%使用hashtable
			s.sendAlert(AlertInfo{
				Level:      "WARNING",
				Pattern:    pattern,
				Message:    fmt.Sprintf("%.1f%% 的Key使用hashtable编码", alertRatio),
				TotalKeys:  stats.TotalKeys,
				UnsafeKeys: stats.HashtableCount,
				AvgFields:  stats.AvgFieldCount,
				Timestamp:  time.Now(),
			})
		}
	} else if stats.TotalKeys > 0 {
		fmt.Printf("   ✅ 编码健康: 所有Key使用listpack编码\n")
	}
}

func (s *MonitoringServer) sendAlert(alert AlertInfo) {
	fmt.Printf("\n🚨 编码告警触发!\n")
	fmt.Printf("   级别: %s\n", alert.Level)
	fmt.Printf("   模式: %s\n", alert.Pattern)
	fmt.Printf("   消息: %s\n", alert.Message)
	fmt.Printf("   时间: %s\n", alert.Timestamp.Format("2006-01-02 15:04:05"))

	// 这里可以集成实际的告警系统
	// 例如: 发送邮件、Slack通知、钉钉机器人等
}

func (s *MonitoringServer) startHTTPServer() {
	http.HandleFunc("/health", s.handleHealth)
	http.HandleFunc("/encoding/stats", s.handleEncodingStats)
	http.HandleFunc("/encoding/check", s.handleEncodingCheck)
	http.HandleFunc("/encoding/unsafe", s.handleUnsafeKeys)
	http.HandleFunc("/", s.handleDashboard)

	fmt.Println("🌐 监控面板启动: http://localhost:8080")
	fmt.Println("   - /health          - 健康检查")
	fmt.Println("   - /encoding/stats  - 编码统计")
	fmt.Println("   - /encoding/check  - 检查指定key")
	fmt.Println("   - /encoding/unsafe - 不安全的key列表")

	log.Fatal(http.ListenAndServe(":8080", nil))
}

func (s *MonitoringServer) handleHealth(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	// 检查Redis连接
	if err := s.client.Ping(ctx).Err(); err != nil {
		http.Error(w, "Redis连接失败", http.StatusServiceUnavailable)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
		"redis":     "connected",
	})
}

func (s *MonitoringServer) handleEncodingStats(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()
	pattern := r.URL.Query().Get("pattern")
	if pattern == "" {
		pattern = "*"
	}

	stats, err := s.monitor.GetEncodingStats(ctx, pattern)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

func (s *MonitoringServer) handleEncodingCheck(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()
	key := r.URL.Query().Get("key")
	if key == "" {
		http.Error(w, "需要提供key参数", http.StatusBadRequest)
		return
	}

	info, err := s.monitor.CheckKeyEncoding(ctx, key)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(info)
}

func (s *MonitoringServer) handleUnsafeKeys(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()
	pattern := r.URL.Query().Get("pattern")
	if pattern == "" {
		pattern = "*"
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 100
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil {
			limit = l
		}
	}

	infos, err := s.monitor.ScanAndCheck(ctx, pattern, limit)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 过滤出不安全的key
	var unsafeKeys []encoders.EncodingInfo
	for _, info := range infos {
		if !info.IsSafe {
			unsafeKeys = append(unsafeKeys, info)
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"pattern":     pattern,
		"total_keys":  len(infos),
		"unsafe_keys": unsafeKeys,
		"count":       len(unsafeKeys),
	})
}

func (s *MonitoringServer) handleDashboard(w http.ResponseWriter, r *http.Request) {
	dashboard := `
<!DOCTYPE html>
<html>
<head>
    <title>Redis编码监控面板</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .safe { color: green; }
        .unsafe { color: red; }
        .warning { color: orange; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .stats { display: flex; gap: 20px; }
        .stat-box { flex: 1; text-align: center; padding: 20px; background: #f9f9f9; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Redis编码监控面板</h1>
        <p>实时监控Redis HashMap的编码类型，确保内存使用效率</p>
    </div>

    <div class="section">
        <h2>📊 系统状态</h2>
        <div class="stats" id="systemStats">
            <div class="stat-box">
                <h3>Redis状态</h3>
                <div id="redisStatus">检查中...</div>
            </div>
            <div class="stat-box">
                <h3>总Key数量</h3>
                <div id="totalKeys">-</div>
            </div>
            <div class="stat-box">
                <h3>不安全Key数量</h3>
                <div id="unsafeKeys">-</div>
            </div>
            <div class="stat-box">
                <h3>编码健康度</h3>
                <div id="healthScore">-</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🔧 操作面板</h2>
        <button onclick="checkHealth()">健康检查</button>
        <button onclick="loadStats()">刷新统计</button>
        <button onclick="loadUnsafeKeys()">查看不安全Key</button>
        <button onclick="checkSpecificKey()">检查指定Key</button>
    </div>

    <div class="section">
        <h2>📈 编码统计</h2>
        <div id="statsContent">点击"刷新统计"加载数据</div>
    </div>

    <div class="section">
        <h2>⚠️ 不安全Key列表</h2>
        <div id="unsafeKeysContent">点击"查看不安全Key"加载数据</div>
    </div>

    <script>
        function checkHealth() {
            fetch('/health')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('redisStatus').innerHTML = 
                        '<span class="safe">✅ ' + data.status + '</span>';
                })
                .catch(error => {
                    document.getElementById('redisStatus').innerHTML = 
                        '<span class="unsafe">❌ 连接失败</span>';
                });
        }

        function loadStats() {
            fetch('/encoding/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalKeys').textContent = data.total_keys;
                    document.getElementById('unsafeKeys').textContent = data.hashtable_count;
                    
                    const healthScore = data.total_keys > 0 ? 
                        ((data.listpack_count / data.total_keys) * 100).toFixed(1) + '%' : 'N/A';
                    document.getElementById('healthScore').innerHTML = 
                        data.hashtable_count === 0 ? 
                            '<span class="safe">' + healthScore + '</span>' :
                            '<span class="warning">' + healthScore + '</span>';

                    document.getElementById('statsContent').innerHTML = 
                        '<table>' +
                        '<tr><th>指标</th><th>值</th></tr>' +
                        '<tr><td>总Key数</td><td>' + data.total_keys + '</td></tr>' +
                        '<tr><td>listpack编码</td><td class="safe">' + data.listpack_count + '</td></tr>' +
                        '<tr><td>hashtable编码</td><td class="unsafe">' + data.hashtable_count + '</td></tr>' +
                        '<tr><td>平均Field数</td><td>' + data.avg_field_count.toFixed(1) + '</td></tr>' +
                        '<tr><td>平均内存/Key</td><td>' + (data.avg_memory_per_key/1024).toFixed(1) + 'KB</td></tr>' +
                        '<tr><td>总内存使用</td><td>' + (data.total_memory/1024/1024).toFixed(1) + 'MB</td></tr>' +
                        '</table>';
                });
        }

        function loadUnsafeKeys() {
            fetch('/encoding/unsafe?limit=50')
                .then(response => response.json())
                .then(data => {
                    if (data.unsafe_keys.length === 0) {
                        document.getElementById('unsafeKeysContent').innerHTML = 
                            '<p class="safe">✅ 没有发现不安全的Key</p>';
                        return;
                    }

                    let table = '<table><tr><th>Key</th><th>编码</th><th>Field数</th><th>内存使用</th><th>时间</th></tr>';
                    data.unsafe_keys.forEach(key => {
                        table += '<tr>' +
                            '<td>' + key.key + '</td>' +
                            '<td class="unsafe">' + key.encoding + '</td>' +
                            '<td>' + key.field_count + '</td>' +
                            '<td>' + (key.memory_usage/1024).toFixed(1) + 'KB</td>' +
                            '<td>' + new Date(key.timestamp).toLocaleTimeString() + '</td>' +
                            '</tr>';
                    });
                    table += '</table>';
                    
                    document.getElementById('unsafeKeysContent').innerHTML = 
                        '<p class="unsafe">⚠️ 发现 ' + data.count + ' 个不安全的Key</p>' + table;
                });
        }

        function checkSpecificKey() {
            const key = prompt('请输入要检查的Key:');
            if (!key) return;

            fetch('/encoding/check?key=' + encodeURIComponent(key))
                .then(response => response.json())
                .then(data => {
                    const safetyStatus = data.is_safe ? 
                        '<span class="safe">✅ 安全 (listpack)</span>' :
                        '<span class="unsafe">⚠️ 不安全 (hashtable)</span>';
                    
                    alert('Key检查结果:\\n' +
                          'Key: ' + data.key + '\\n' +
                          '编码: ' + data.encoding + '\\n' +
                          'Field数: ' + data.field_count + '\\n' +
                          '内存: ' + (data.memory_usage/1024).toFixed(1) + 'KB\\n' +
                          '状态: ' + (data.is_safe ? '安全' : '不安全'));
                })
                .catch(error => {
                    alert('检查失败: ' + error.message);
                });
        }

        // 页面加载时自动检查健康状态
        checkHealth();
        loadStats();

        // 每30秒自动刷新
        setInterval(() => {
            checkHealth();
            loadStats();
        }, 30000);
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(dashboard))
}

// AlertInfo 告警信息结构
type AlertInfo struct {
	Level      string    `json:"level"`
	Pattern    string    `json:"pattern"`
	Message    string    `json:"message"`
	TotalKeys  int       `json:"total_keys"`
	UnsafeKeys int       `json:"unsafe_keys"`
	AvgFields  float64   `json:"avg_fields"`
	Timestamp  time.Time `json:"timestamp"`
}
