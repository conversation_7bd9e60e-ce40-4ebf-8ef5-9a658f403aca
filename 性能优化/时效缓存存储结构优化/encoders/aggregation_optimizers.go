package encoders

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// AggregationStrategy 聚合策略接口
type AggregationStrategy interface {
	GetName() string
	GetDescription() string
	GenerateAggregatedKey(keyData *KeyData) string
	GenerateFieldName(keyData *KeyData) string
	GetMaxFieldsPerKey() int
	IsEncodingSafe(fieldCount int) bool
}

// ShopLocationAggregationStrategy 店铺+地址聚合策略
type ShopLocationAggregationStrategy struct{}

func (s *ShopLocationAggregationStrategy) GetName() string {
	return "ShopLocationAggregation"
}

func (s *ShopLocationAggregationStrategy) GetDescription() string {
	return "店铺+地址聚合: Shop+SellerLoc+BuyerLoc+Date为KEY，Channel+Window为Field"
}

func (s *ShopLocationAggregationStrategy) GenerateAggregatedKey(keyData *KeyData) string {
	// KEY格式: {ShopID}_{SellerLoc}_{BuyerLoc}_{Date}
	dateStr := keyData.Date.Format("20060102")
	return fmt.Sprintf("%d_%d_%d_%s",
		keyData.ShopID, keyData.SellerLoc, keyData.BuyerLoc, dateStr)
}

func (s *ShopLocationAggregationStrategy) GenerateFieldName(keyData *KeyData) string {
	// Field格式: {ChannelID}_{WindowStart}
	return fmt.Sprintf("%d_%d", keyData.ChannelID, keyData.WindowStart)
}

func (s *ShopLocationAggregationStrategy) GetMaxFieldsPerKey() int {
	// 一个店铺+地址对一天的Field数量估算:
	// - 20个渠道
	// - 6个时间窗口
	// 总计: 20 * 6 = 120个Field ✅ (远低于512阈值)
	return 120
}

func (s *ShopLocationAggregationStrategy) IsEncodingSafe(fieldCount int) bool {
	return fieldCount <= 400 // 非常安全
}

// ValueEncoder 值编码器接口
type ValueEncoder interface {
	GetName() string
	EncodeValue(min, max uint32) string
}

// OriginalValueEncoder 原始值编码器
type OriginalValueEncoder struct{}

func (o *OriginalValueEncoder) GetName() string {
	return "Original"
}

func (o *OriginalValueEncoder) EncodeValue(min, max uint32) string {
	return fmt.Sprintf("%d,%d", min, max)
}

// CompactValueEncoder 紧凑值编码器
type CompactValueEncoder struct{}

func (c *CompactValueEncoder) GetName() string {
	return "Compact"
}

func (c *CompactValueEncoder) EncodeValue(min, max uint32) string {
	// 对于小值场景，仍使用文本格式最优
	return fmt.Sprintf("%d,%d", min, max)
}

// AggregationOptimizer 聚合优化器
type AggregationOptimizer struct {
	Strategy AggregationStrategy
	Encoder  ValueEncoder // 复用现有的值编码器
}

func (a *AggregationOptimizer) GetName() string {
	return fmt.Sprintf("%s+%s", a.Strategy.GetName(), a.Encoder.GetName())
}

func (a *AggregationOptimizer) GetDescription() string {
	return fmt.Sprintf("%s + %s值编码", a.Strategy.GetDescription(), a.Encoder.GetName())
}

func (a *AggregationOptimizer) OptimizeKey(keyData *KeyData) string {
	return a.Strategy.GenerateAggregatedKey(keyData)
}

func (a *AggregationOptimizer) OptimizeField(keyData *KeyData) string {
	return a.Strategy.GenerateFieldName(keyData)
}

func (a *AggregationOptimizer) OptimizeValue(min, max uint32) string {
	return a.Encoder.EncodeValue(min, max)
}

func (a *AggregationOptimizer) IsEncodingSafe(fieldCount int) bool {
	return a.Strategy.IsEncodingSafe(fieldCount)
}

// EncodingMonitor 编码监控器
type EncodingMonitor struct {
	client *redis.Client
}

func NewEncodingMonitor(client *redis.Client) *EncodingMonitor {
	return &EncodingMonitor{client: client}
}

// CheckKeyEncoding 检查指定key的编码类型
func (m *EncodingMonitor) CheckKeyEncoding(ctx context.Context, key string) (EncodingInfo, error) {
	// 获取编码类型
	encoding, err := m.client.ObjectEncoding(ctx, key).Result()
	if err != nil {
		return EncodingInfo{}, err
	}

	// 获取field数量
	fieldCount, err := m.client.HLen(ctx, key).Result()
	if err != nil {
		return EncodingInfo{}, err
	}

	// 估算内存使用
	memUsage, err := m.client.MemoryUsage(ctx, key).Result()
	if err != nil {
		memUsage = 0 // 如果不支持MEMORY USAGE命令，设为0
	}

	return EncodingInfo{
		Key:         key,
		Encoding:    encoding,
		FieldCount:  int(fieldCount),
		MemoryUsage: memUsage,
		IsSafe:      encoding == "listpack",
		Timestamp:   time.Now(),
	}, nil
}

// BatchCheckEncoding 批量检查编码类型
func (m *EncodingMonitor) BatchCheckEncoding(ctx context.Context, keys []string) ([]EncodingInfo, error) {
	results := make([]EncodingInfo, 0, len(keys))

	for _, key := range keys {
		info, err := m.CheckKeyEncoding(ctx, key)
		if err != nil {
			continue // 跳过错误的key
		}
		results = append(results, info)
	}

	return results, nil
}

// ScanAndCheck 扫描匹配模式的key并检查编码
func (m *EncodingMonitor) ScanAndCheck(ctx context.Context, pattern string, limit int) ([]EncodingInfo, error) {
	var keys []string
	var cursor uint64

	for len(keys) < limit {
		var scanKeys []string
		var err error

		scanKeys, cursor, err = m.client.Scan(ctx, cursor, pattern, 100).Result()
		if err != nil {
			return nil, err
		}

		keys = append(keys, scanKeys...)

		if cursor == 0 {
			break
		}
	}

	// 限制数量
	if len(keys) > limit {
		keys = keys[:limit]
	}

	return m.BatchCheckEncoding(ctx, keys)
}

// EncodingInfo 编码信息结构
type EncodingInfo struct {
	Key         string    `json:"key"`
	Encoding    string    `json:"encoding"`
	FieldCount  int       `json:"field_count"`
	MemoryUsage int64     `json:"memory_usage"`
	IsSafe      bool      `json:"is_safe"`
	Timestamp   time.Time `json:"timestamp"`
}

// GetEncodingStats 获取编码统计信息
func (m *EncodingMonitor) GetEncodingStats(ctx context.Context, pattern string) (EncodingStats, error) {
	infos, err := m.ScanAndCheck(ctx, pattern, 1000)
	if err != nil {
		return EncodingStats{}, err
	}

	stats := EncodingStats{
		TotalKeys:      len(infos),
		ListpackCount:  0,
		HashtableCount: 0,
		TotalMemory:    0,
		AvgFieldCount:  0,
	}

	totalFields := 0
	for _, info := range infos {
		stats.TotalMemory += info.MemoryUsage
		totalFields += info.FieldCount

		if info.Encoding == "listpack" {
			stats.ListpackCount++
		} else if info.Encoding == "hashtable" {
			stats.HashtableCount++
		}
	}

	if len(infos) > 0 {
		stats.AvgFieldCount = float64(totalFields) / float64(len(infos))
		stats.AvgMemoryPerKey = float64(stats.TotalMemory) / float64(len(infos))
	}

	return stats, nil
}

// EncodingStats 编码统计信息
type EncodingStats struct {
	TotalKeys       int     `json:"total_keys"`
	ListpackCount   int     `json:"listpack_count"`
	HashtableCount  int     `json:"hashtable_count"`
	TotalMemory     int64   `json:"total_memory"`
	AvgFieldCount   float64 `json:"avg_field_count"`
	AvgMemoryPerKey float64 `json:"avg_memory_per_key"`
}

// GetAllAggregationOptimizers 获取所有聚合优化器
func GetAllAggregationOptimizers() []AggregationOptimizer {
	strategies := []AggregationStrategy{
		&ShopLocationAggregationStrategy{}, // 店铺+地址聚合策略 (唯一方案)
	}

	valueEncoders := []ValueEncoder{
		&OriginalValueEncoder{},
		&CompactValueEncoder{},
	}

	var optimizers []AggregationOptimizer
	for _, strategy := range strategies {
		for _, encoder := range valueEncoders {
			optimizers = append(optimizers, AggregationOptimizer{
				Strategy: strategy,
				Encoder:  encoder,
			})
		}
	}

	return optimizers
}
