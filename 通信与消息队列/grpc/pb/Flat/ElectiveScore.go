// Code generated by the FlatBuffers compiler. DO NOT EDIT.

package Flat

import (
	flatbuffers "github.com/google/flatbuffers/go"
)

type ElectiveScore struct {
	_tab flatbuffers.Table
}

func GetRootAsElectiveScore(buf []byte, offset flatbuffers.UOffsetT) *ElectiveScore {
	n := flatbuffers.GetUOffsetT(buf[offset:])
	x := &ElectiveScore{}
	x.Init(buf, n+offset)
	return x
}

func FinishElectiveScoreBuffer(builder *flatbuffers.Builder, offset flatbuffers.UOffsetT) {
	builder.Finish(offset)
}

func GetSizePrefixedRootAsElectiveScore(buf []byte, offset flatbuffers.UOffsetT) *ElectiveScore {
	n := flatbuffers.GetUOffsetT(buf[offset+flatbuffers.SizeUint32:])
	x := &ElectiveScore{}
	x.Init(buf, n+offset+flatbuffers.SizeUint32)
	return x
}

func FinishSizePrefixedElectiveScoreBuffer(builder *flatbuffers.Builder, offset flatbuffers.UOffsetT) {
	builder.FinishSizePrefixed(offset)
}

func (rcv *ElectiveScore) Init(buf []byte, i flatbuffers.UOffsetT) {
	rcv._tab.Bytes = buf
	rcv._tab.Pos = i
}

func (rcv *ElectiveScore) Table() flatbuffers.Table {
	return rcv._tab
}

func (rcv *ElectiveScore) Key() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(4))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func (rcv *ElectiveScore) Value() float64 {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(6))
	if o != 0 {
		return rcv._tab.GetFloat64(o + rcv._tab.Pos)
	}
	return 0.0
}

func (rcv *ElectiveScore) MutateValue(n float64) bool {
	return rcv._tab.MutateFloat64Slot(6, n)
}

func ElectiveScoreStart(builder *flatbuffers.Builder) {
	builder.StartObject(2)
}
func ElectiveScoreAddKey(builder *flatbuffers.Builder, key flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(0, flatbuffers.UOffsetT(key), 0)
}
func ElectiveScoreAddValue(builder *flatbuffers.Builder, value float64) {
	builder.PrependFloat64Slot(1, value, 0.0)
}
func ElectiveScoreEnd(builder *flatbuffers.Builder) flatbuffers.UOffsetT {
	return builder.EndObject()
}
