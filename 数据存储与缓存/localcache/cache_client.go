package localcache

import (
	"strconv"

	"github.com/allegro/bigcache/v3"
	"github.com/coocood/freecache"
	"github.com/dgraph-io/ristretto"
	lru "github.com/hashicorp/golang-lru"
	"github.com/maypok86/otter"
	gocache "github.com/patrickmn/go-cache"
)

// CacheClient 定义了缓存客户端的通用接口
type CacheClient interface {
	Set(key int, value struct{})
	Get(key int) (struct{}, bool)
	Close()
}

// HitRateTestClient 定义了命中率测试的客户端接口
type HitRateTestClient interface {
	Get(key string) (string, bool)
	Set(key string, value string)
	Name() string
	Close()
}

// BigCacheClient 是 BigCache 的包装器
type BigCacheClient struct {
	client *bigcache.BigCache
}

func (c *BigCacheClient) Set(key int, value struct{}) {
	_ = c.client.Set(strconv.Itoa(key), []byte{})
}

func (c *BigCacheClient) Get(key int) (struct{}, bool) {
	_, err := c.client.Get(strconv.Itoa(key))
	return struct{}{}, err == nil
}

func (c *BigCacheClient) Close() {
	_ = c.client.Close()
}

func (c *BigCacheClient) Name() string {
	return "bigcache"
}

// FreeCacheClient 是 FreeCache 的包装器
type FreeCacheClient struct {
	client *freecache.Cache
}

func (c *FreeCacheClient) Set(key int, value struct{}) {
	_ = c.client.Set([]byte(strconv.Itoa(key)), []byte{}, 0)
}

func (c *FreeCacheClient) Get(key int) (struct{}, bool) {
	_, err := c.client.Get([]byte(strconv.Itoa(key)))
	return struct{}{}, err == nil
}

func (c *FreeCacheClient) Close() {
	c.client.Clear()
}

func (c *FreeCacheClient) Name() string {
	return "freecache"
}

// RistrettoClient 是 Ristretto 的包装器
type RistrettoClient struct {
	client *ristretto.Cache
}

func (c *RistrettoClient) Set(key int, value struct{}) {
	c.client.Set(key, value, 1)
}

func (c *RistrettoClient) Get(key int) (struct{}, bool) {
	val, found := c.client.Get(key)
	if !found {
		return struct{}{}, false
	}
	return val.(struct{}), true
}

func (c *RistrettoClient) Close() {
	c.client.Close()
}

func (c *RistrettoClient) Name() string {
	return "ristretto"
}

// GolangLRUClient 是 golang-lru 的包装器
type GolangLRUClient struct {
	client *lru.Cache
}

func (c *GolangLRUClient) Set(key int, value struct{}) {
	c.client.Add(key, value)
}

func (c *GolangLRUClient) Get(key int) (struct{}, bool) {
	val, found := c.client.Get(key)
	if !found {
		return struct{}{}, false
	}
	return val.(struct{}), true
}

func (c *GolangLRUClient) Close() {}

func (c *GolangLRUClient) Name() string {
	return "golang-lru"
}

// GoCacheClient 是 go-cache 的包装器
type GoCacheClient struct {
	client *gocache.Cache
}

func (c *GoCacheClient) Set(key int, value struct{}) {
	c.client.Set(strconv.Itoa(key), value, gocache.DefaultExpiration)
}

func (c *GoCacheClient) Get(key int) (struct{}, bool) {
	val, found := c.client.Get(strconv.Itoa(key))
	if !found {
		return struct{}{}, false
	}
	return val.(struct{}), true
}

func (c *GoCacheClient) Close() {}

func (c *GoCacheClient) Name() string {
	return "go-cache"
}

// OtterClient 是 otter 的包装器
type OtterClient struct {
	client *otter.Cache[int, struct{}]
}

func (c *OtterClient) Set(key int, value struct{}) {
	c.client.Set(key, value)
}

func (c *OtterClient) Get(key int) (struct{}, bool) {
	return c.client.Get(key)
}

func (c *OtterClient) Close() {
	c.client.Close()
}

func (c *OtterClient) Name() string {
	return "otter"
}
