package main

import "github.com/RoaringBitmap/roaring"

// FlattenIndexRoaring uses a single bitmap per TPL, mapping the (Origin, Dest) pair to a single flattened index.
type FlattenIndexRoaring struct {
	data map[uint32]*roaring.Bitmap
}

// NewFlattenIndexRoaring now builds from decoupled data, performing a Cartesian product internally.
// This is expected to be VERY SLOW.
func NewFlattenIndexRoaring(data map[uint32]TplCoverage) *FlattenIndexRoaring {
	f := &FlattenIndexRoaring{
		data: make(map[uint32]*roaring.Bitmap),
	}

	for tplID, coverage := range data {
		f.data[tplID] = roaring.New()
		// Cartesian product generation
		for _, originID := range coverage.Origins {
			for _, destID := range coverage.Dests {
				index := originID*NumLocations + destID
				f.data[tplID].Add(index)
			}
		}
	}
	return f
}

func (f *FlattenIndexRoaring) IsServiceable(tpl, origin, dest uint32) bool {
	if tplBitmap, ok := f.data[tpl]; ok {
		// Calculate the flattened index to check
		index := origin*NumLocations + dest
		return tplBitmap.Contains(index)
	}
	return false
}

// BatchIsServiceable processes a batch of queries efficiently
func (f *FlattenIndexRoaring) BatchIsServiceable(queries []ServicePair) []bool {
	results := make([]bool, len(queries))
	for idx, query := range queries {
		results[idx] = f.IsServiceable(query.TplID, query.OriginID, query.DestID)
	}
	return results
}
