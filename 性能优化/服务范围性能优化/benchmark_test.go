package main

import (
	"sync"
	"testing"
)

// The global data source is now the decoupled map.
var decoupledData map[uint32]TplCoverage
var testQueries []ServicePair

// Initialize data once for all benchmarks.
func init() {
	decoupledData = generateDecoupledData()
	testQueries = generateQueries(10000)
}

// --- Build Benchmarks ---

// --- Build Benchmarks for Roaring Bitmap implementations ---

func BenchmarkBuildNestedMapRoaring(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewNestedMapRoaring(decoupledData)
	}
}

func BenchmarkBuildFlattenIndexRoaring(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewFlattenIndexRoaring(decoupledData)
	}
}

func BenchmarkBuildDecoupledSetsRoaring(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewDecoupledSetsRoaring(decoupledData)
	}
}

func BenchmarkBuildInvertedIndexRoaring(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewInvertedIndexRoaring(decoupledData)
	}
}

func BenchmarkBuildPartitionedBitmapRoaring(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewPartitionedBitmapRoaring(decoupledData)
	}
}

// --- Build Benchmarks for Native Map implementations ---

func BenchmarkBuildNestedMapSet(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewNestedMapSet(decoupledData)
	}
}

func BenchmarkBuildFlattenIndexMap(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewFlattenIndexMap(decoupledData)
	}
}

func BenchmarkBuildDecoupledSetsMap(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewDecoupledSetsMap(decoupledData)
	}
}

func BenchmarkBuildInvertedIndexMap(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewInvertedIndexMap(decoupledData)
	}
}

func BenchmarkBuildPartitionedMapSet(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewPartitionedMapSet(decoupledData)
	}
}

func BenchmarkBuildNativeBitVector(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_ = NewNativeBitVector(decoupledData)
	}
}

// --- Query Benchmarks ---

// Singleton instance for NestedMapRoaring
var (
	nestedMapOnce sync.Once
	nestedMap     *NestedMapRoaring
)

func BenchmarkQueryNestedMapRoaring(b *testing.B) {
	nestedMapOnce.Do(func() {
		nestedMap = NewNestedMapRoaring(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = nestedMap.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for FlattenIndexRoaring
var (
	flattenIndexOnce sync.Once
	flattenIndex     *FlattenIndexRoaring
)

func BenchmarkQueryFlattenIndexRoaring(b *testing.B) {
	flattenIndexOnce.Do(func() {
		flattenIndex = NewFlattenIndexRoaring(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = flattenIndex.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for DecoupledSetsRoaring
var (
	decoupledSetsOnce sync.Once
	decoupledSets     *DecoupledSetsRoaring
)

func BenchmarkQueryDecoupledSetsRoaring(b *testing.B) {
	decoupledSetsOnce.Do(func() {
		decoupledSets = NewDecoupledSetsRoaring(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = decoupledSets.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for InvertedIndexRoaring
var (
	invertedIndexOnce sync.Once
	invertedIndex     *InvertedIndexRoaring
)

func BenchmarkQueryInvertedIndexRoaring(b *testing.B) {
	invertedIndexOnce.Do(func() {
		invertedIndex = NewInvertedIndexRoaring(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = invertedIndex.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for PartitionedBitmapRoaring
var (
	partitionedBitmapOnce sync.Once
	partitionedBitmap     *PartitionedBitmapRoaring
)

func BenchmarkQueryPartitionedBitmapRoaring(b *testing.B) {
	partitionedBitmapOnce.Do(func() {
		partitionedBitmap = NewPartitionedBitmapRoaring(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = partitionedBitmap.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for NativeBitVector
var (
	nativeBitVectorOnce sync.Once
	nativeBitVector     *NativeBitVector
)

func BenchmarkQueryNativeBitVector(b *testing.B) {
	nativeBitVectorOnce.Do(func() {
		nativeBitVector = NewNativeBitVector(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = nativeBitVector.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// --- Query Benchmarks for Native Map implementations ---

// Singleton instance for NestedMapSet
var (
	nestedMapSetOnce sync.Once
	nestedMapSet     *NestedMapSet
)

func BenchmarkQueryNestedMapSet(b *testing.B) {
	nestedMapSetOnce.Do(func() {
		nestedMapSet = NewNestedMapSet(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = nestedMapSet.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for FlattenIndexMap
var (
	flattenIndexMapOnce sync.Once
	flattenIndexMap     *FlattenIndexMap
)

func BenchmarkQueryFlattenIndexMap(b *testing.B) {
	flattenIndexMapOnce.Do(func() {
		flattenIndexMap = NewFlattenIndexMap(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = flattenIndexMap.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for DecoupledSetsMap
var (
	decoupledSetsMapOnce sync.Once
	decoupledSetsMap     *DecoupledSetsMap
)

func BenchmarkQueryDecoupledSetsMap(b *testing.B) {
	decoupledSetsMapOnce.Do(func() {
		decoupledSetsMap = NewDecoupledSetsMap(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = decoupledSetsMap.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for InvertedIndexMap
var (
	invertedIndexMapOnce sync.Once
	invertedIndexMap     *InvertedIndexMap
)

func BenchmarkQueryInvertedIndexMap(b *testing.B) {
	invertedIndexMapOnce.Do(func() {
		invertedIndexMap = NewInvertedIndexMap(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = invertedIndexMap.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}

// Singleton instance for PartitionedMapSet
var (
	partitionedMapSetOnce sync.Once
	partitionedMapSet     *PartitionedMapSet
)

func BenchmarkQueryPartitionedMapSet(b *testing.B) {
	partitionedMapSetOnce.Do(func() {
		partitionedMapSet = NewPartitionedMapSet(decoupledData)
	})
	b.ResetTimer()
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		q := testQueries[i%len(testQueries)]
		_ = partitionedMapSet.IsServiceable(q.TplID, q.OriginID, q.DestID)
	}
}
