// Code generated by the FlatBuffers compiler. DO NOT EDIT.

package Flat

import (
	flatbuffers "github.com/google/flatbuffers/go"
)

type Address struct {
	_tab flatbuffers.Table
}

func GetRootAsAddress(buf []byte, offset flatbuffers.UOffsetT) *Address {
	n := flatbuffers.GetUOffsetT(buf[offset:])
	x := &Address{}
	x.Init(buf, n+offset)
	return x
}

func FinishAddressBuffer(builder *flatbuffers.Builder, offset flatbuffers.UOffsetT) {
	builder.Finish(offset)
}

func GetSizePrefixedRootAsAddress(buf []byte, offset flatbuffers.UOffsetT) *Address {
	n := flatbuffers.GetUOffsetT(buf[offset+flatbuffers.SizeUint32:])
	x := &Address{}
	x.Init(buf, n+offset+flatbuffers.SizeUint32)
	return x
}

func FinishSizePrefixedAddressBuffer(builder *flatbuffers.Builder, offset flatbuffers.UOffsetT) {
	builder.FinishSizePrefixed(offset)
}

func (rcv *Address) Init(buf []byte, i flatbuffers.UOffsetT) {
	rcv._tab.Bytes = buf
	rcv._tab.Pos = i
}

func (rcv *Address) Table() flatbuffers.Table {
	return rcv._tab
}

func (rcv *Address) Street() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(4))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func (rcv *Address) City() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(6))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func (rcv *Address) State() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(8))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func (rcv *Address) Country() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(10))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func (rcv *Address) PostalCode() []byte {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(12))
	if o != 0 {
		return rcv._tab.ByteVector(o + rcv._tab.Pos)
	}
	return nil
}

func (rcv *Address) IsCurrent() bool {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(14))
	if o != 0 {
		return rcv._tab.GetBool(o + rcv._tab.Pos)
	}
	return false
}

func (rcv *Address) MutateIsCurrent(n bool) bool {
	return rcv._tab.MutateBoolSlot(14, n)
}

func (rcv *Address) Latitude() float32 {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(16))
	if o != 0 {
		return rcv._tab.GetFloat32(o + rcv._tab.Pos)
	}
	return 0.0
}

func (rcv *Address) MutateLatitude(n float32) bool {
	return rcv._tab.MutateFloat32Slot(16, n)
}

func (rcv *Address) Longitude() float64 {
	o := flatbuffers.UOffsetT(rcv._tab.Offset(18))
	if o != 0 {
		return rcv._tab.GetFloat64(o + rcv._tab.Pos)
	}
	return 0.0
}

func (rcv *Address) MutateLongitude(n float64) bool {
	return rcv._tab.MutateFloat64Slot(18, n)
}

func AddressStart(builder *flatbuffers.Builder) {
	builder.StartObject(8)
}
func AddressAddStreet(builder *flatbuffers.Builder, street flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(0, flatbuffers.UOffsetT(street), 0)
}
func AddressAddCity(builder *flatbuffers.Builder, city flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(1, flatbuffers.UOffsetT(city), 0)
}
func AddressAddState(builder *flatbuffers.Builder, state flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(2, flatbuffers.UOffsetT(state), 0)
}
func AddressAddCountry(builder *flatbuffers.Builder, country flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(3, flatbuffers.UOffsetT(country), 0)
}
func AddressAddPostalCode(builder *flatbuffers.Builder, postalCode flatbuffers.UOffsetT) {
	builder.PrependUOffsetTSlot(4, flatbuffers.UOffsetT(postalCode), 0)
}
func AddressAddIsCurrent(builder *flatbuffers.Builder, isCurrent bool) {
	builder.PrependBoolSlot(5, isCurrent, false)
}
func AddressAddLatitude(builder *flatbuffers.Builder, latitude float32) {
	builder.PrependFloat32Slot(6, latitude, 0.0)
}
func AddressAddLongitude(builder *flatbuffers.Builder, longitude float64) {
	builder.PrependFloat64Slot(7, longitude, 0.0)
}
func AddressEnd(builder *flatbuffers.Builder) flatbuffers.UOffsetT {
	return builder.EndObject()
}
