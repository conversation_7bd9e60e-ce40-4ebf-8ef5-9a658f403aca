package main

// NestedMapSet uses a nested map structure: map[TplID]map[OriginID]map[DestID]bool
type NestedMapSet struct {
	data map[uint32]map[uint32]map[uint32]bool
}

// NewNestedMapSet now builds from decoupled data, performing a Cartesian product internally.
// This is expected to be VERY SLOW.
func NewNestedMapSet(data map[uint32]TplCoverage) *NestedMapSet {
	r := &NestedMapSet{
		data: make(map[uint32]map[uint32]map[uint32]bool),
	}

	for tplID, coverage := range data {
		r.data[tplID] = make(map[uint32]map[uint32]bool, len(coverage.Origins))
		// Cartesian product generation
		for _, originID := range coverage.Origins {
			destMap := make(map[uint32]bool, len(coverage.Dests))
			for _, destID := range coverage.Dests {
				destMap[destID] = true
			}
			r.data[tplID][originID] = destMap
		}
	}
	return r
}

func (r *NestedMapSet) IsServiceable(tpl, origin, dest uint32) bool {
	if tplMap, ok := r.data[tpl]; ok {
		if destMap, ok := tplMap[origin]; ok {
			return destMap[dest]
		}
	}
	return false
}
