package main

const (
	// Defines the offset to separate origin and destination IDs in the same map.
	partitionOffset = uint32(NumLocations)
)

// PartitionedMapSet uses a single map per TPL, with the index space
// partitioned between origins and destinations.
type PartitionedMapSet struct {
	// The key is the TPL ID, the value is the single combined map.
	data map[uint32]map[uint32]bool
}

// NewPartitionedMapSet builds the structure from the decoupled data source.
func NewPartitionedMapSet(data map[uint32]TplCoverage) *PartitionedMapSet {
	p := &PartitionedMapSet{
		data: make(map[uint32]map[uint32]bool),
	}

	for tplID, coverage := range data {
		// Pre-allocate map with estimated capacity
		combinedMap := make(map[uint32]bool, len(coverage.Origins)+len(coverage.Dests))

		// Add all origin IDs directly
		for _, originID := range coverage.Origins {
			combinedMap[originID] = true
		}

		// Add all destination IDs with offset
		for _, destID := range coverage.Dests {
			combinedMap[destID+partitionOffset] = true
		}

		p.data[tplID] = combinedMap
	}

	return p
}

// IsServiceable checks for serviceability using the partitioned map.
// It performs two map lookups on the same map object.
func (p *PartitionedMapSet) IsServiceable(tpl, origin, dest uint32) bool {
	if combinedMap, ok := p.data[tpl]; ok {
		return combinedMap[origin] && combinedMap[dest+partitionOffset]
	}
	return false
}
