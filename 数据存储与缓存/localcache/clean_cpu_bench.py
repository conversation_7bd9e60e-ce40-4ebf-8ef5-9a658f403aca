import re
import json
import sys

infile = sys.argv[1] if len(sys.argv) > 1 else 'cpu_bench_lines.txt'
outfile = 'cpu_bench_clean.json'

pattern = re.compile(r'\[([^\]]+)\] concurrency=(\d+) QPS=([\d.]+) CPU_avg=([\d.]+)% CPU_peak=([\d.]+)%')

records = []
with open(infile) as f:
    for line in f:
        line = line.strip()
        m = pattern.search(line)
        if m:
            lib, concurrency, qps, cpu_avg, cpu_peak = m.groups()
            records.append({
                'lib': lib,
                'concurrency': int(concurrency),
                'qps': float(qps),
                'cpu_avg': float(cpu_avg),
                'cpu_peak': float(cpu_peak)
            })

with open(outfile, 'w') as f:
    for rec in records:
        f.write(json.dumps(rec, ensure_ascii=False) + '\n')

print(f"清洗完成，输出 {outfile}，共 {len(records)} 条记录。") 