## 时效 Redis 缓存存储结构优化：实验报告

### 实验数据

- 业务维度：店铺 `ShopID`、卖家地址 `SellerLoc`、买家地址 `BuyerLoc`、渠道 `ChannelID ∈ {8000..8004}`、日期 `Date`、时间窗起始 `WindowStart ∈ {0,4,8,12,16,20}`、窗口大小 `WindowSize = 4h`。
- 取值域：
  - ShopID：从 `100000000` 起的 10^5 规模采样；Seller/Buyer 地址从 `10000` 起的 5×10^3 规模采样。
  - 值域：`Min ∈ [1,9]`，`Max ∈ {Min, Min+1}`。
- 生成策略（与实现一致）：
  - 每组店铺组合（ShopID, SellerLoc, BuyerLoc）生成 **3 天** × 5 个 Channel × 6 个窗口 = **90 条**记录。
  - 目标“sampleSize”设为 N，则总记录数为 `N × 6`（每 Key 对应 6 个窗口）。
  - 通过 `shopGroupCount = (N×6)/90` 近似反推需要的店铺组合数，补齐至目标规模（见 `generateTestData`）。
- 示例记录（单条）：

```text
ShopID=100123456, SellerLoc=10123, BuyerLoc=10234, ChannelID=8003,
Date=2025-08-17, WindowStart=8, WindowSize=4, Min=5, Max=6
```

### 实验方法

1) 初始化：每个策略前均执行 `FLUSHALL`、`GC`；记录基线内存 `used_memory`（`INFO memory`）。
2) 写入：使用 Pipeline（批量 1000）逐条 `HSET`/`SET`（取决于策略）；计时得到写入耗时 `T_write` 与吞吐 `OPS_write = 总记录数 / T_write`。
3) 读取：对样本进行抽样读取（`min(总记录数/10, 10000)`），读操作也使用 Pipeline；计时得到 `T_read` 与吞吐 `OPS_read = 抽样条数 / T_read`。
4) 内存：写入后再次采集 `used_memory_after`，以增量 `Δ=after−base` 作为本轮策略的总内存；折算单记录字节数 `Δ / 总记录数`。

注：聚合策略控制单 Key 的 `field` 数 ≤ 90（3天 * 5 Channel × 6 窗口），显著低于 listpack 风险阈值（约 512）。

### 策略详解与示例

*本节详细阐述全部10种测试策略的构成、核心思想与逻辑，并提供与`benchmark.go`实现对齐的伪代码。*

*示例数据: `{ShopID: 100123456, Seller: 10123, Buyer: 10234, Channel: 8003, Date: 2025-08-17, WindowStart: 8, Min: 5, Max: 6}`*

---
**1. Original (基线)**
- **构成**: 基础模型
- **核心思想**: 不做任何优化，使用原生字符串拼接作为Key，直接存入Hash。
- **伪代码**:
  ```go
  // func (s *OriginalStrategy) WriteData(...)
  key   = Sprintf("%d_%d_%d_%d_%s", data.ShopID, ..., data.Date.Format("20060102"))
  field = Sprintf("%d_%d", data.WindowStart, data.WindowStart+data.WindowSize)
  value = Sprintf("%d,%d", data.Min, data.Max)
  HSET(key, field, value)
  ```

---
**2. ValueIntOptimized (值压缩)**
- **构成**: `Original + Value:Int`
- **核心思想**: 利用Redis对小整数的高效编码，将`Min,Max`两个值合并存入一个整数。
- **伪代码**:
  ```go
  // func (s *ValueIntOptimizedStrategy) WriteData(...)
  key   = Sprintf("...") // 同上
  field = Sprintf("...") // 同上
  value = compressValueToInt(data.Min, data.Max) // return (min << 8) | max
  HSET(key, field, value)
  ```

---
**3. Base62 (Key文本编码)**
- **构成**: `Key:Base62 Encoding`
- **核心思想**: 将Key中的数字部分转为更高基数的Base62编码，以缩短字符串长度。
- **伪代码**:
  ```go
  // func (s *Base62Strategy) compressKey(data)
  key = Sprintf("%s_%s_%s_%s_%s",
      uint64ToBase62(data.ShopID),
      uint64ToBase62(data.SellerLoc),
      // ...
      uint64ToBase62(data.Date.Weekday())
  )
  field = Itoa(data.WindowStart / 4)
  value = Sprintf("%d,%d", data.Min, data.Max)
  HSET(key, field, value)
  ```

---
**4. BinaryPacked (Key位打包)**
- **构成**: `Key:BinaryPack(70bit)`
- **核心思想**: 将Key的各维度信息按二进制位（bit）紧凑排列，再编码为十六进制字符串。
- **位分配**: `ShopID(30b)+Seller(16b)+Buyer(16b)+Channel(5b)+Weekday(3b)=70b`
- **伪代码**:
  ```go
  // func (s *BinaryPackedStrategy) compressKey(data)
  shop_part = data.ShopID & (1<<30-1)
  // ...
  high6 = shop_part >> 24
  low64 = (shop_part & 0xFFFFFF)<<40 | ...
  key = FormatUint(high6, 16) + ":" + FormatUint(low64, 16)

  field = Itoa(data.WindowStart / 4)
  value = Sprintf("%d,%d", data.Min, data.Max)
  HSET(key, field, value)
  ```

---
**5. Binary64Bit (Key极限压缩 + 值压缩)**
- **构成**: `Key:BinaryPack(60bit) + Value:Int`
- **核心思想**: 将Key的各维度信息按二进制位极限压缩到60位以内，并对Value进行整数压缩。
- **位分配**: `ShopID(20b)+SellerLoc(16b)+BuyerLoc(16b)+ChannelID(5b)+Date(3b)=60b`
- **伪代码**:
  ```go
  // func (s *Binary64BitStrategy) compressKey(data)
  shop_c = data.ShopID - 100000000
  // ...
  packed = shop_c<<40 | seller_c<<24 | buyer_c<<8 | ...
  key = FormatUint(packed, 16)

  field = data.WindowStart / 4
  value = compressValueToInt(data.Min, data.Max)
  HSET(key, field, value)
  ```

---
**6. ShopLocationAggregation (聚合)**
- **构成**: `Data Aggregation`
- **核心思想**: **结构性优化**。Key不再包含Channel和Date，大幅减少Key的数量。将Channel和Date信息转移到Field中。
- **伪代码**:
  ```go
  // func (s *ShopLocationAggregationStrategy) WriteData(...)
  key   = Sprintf("%d_%d_%d", data.ShopID, data.SellerLoc, data.BuyerLoc)
  field = Sprintf("%d_%d_%d", data.Date.Weekday(), data.ChannelID, data.WindowStart)
  value = Sprintf("%d,%d", data.Min, data.Max)
  HSET(key, field, value)
  ```

---
**7. AggregationBinaryPack (聚合 + Key/Field压缩)**
- **构成**: `Aggregation + Key/Field:BinaryPack`
- **核心思想**: 在聚合的基础上，对Key和Field字符串本身再进行二进制压缩，编码为十六进制。
- **位分配**:
  - **Key (62b)**: `ShopID(30b) + SellerLoc(16b) + BuyerLoc(16b)`
  - **Field (11b)**: `Weekday(3b) + ChannelID(5b) + WindowIndex(3b)`
- **伪代码**:
  ```go
  // key = s.compressAggregatedKey(data)
  shop_norm = data.ShopID - 100000000
  packed_key = shop_norm<<32 | data.SellerLoc<<16 | data.BuyerLoc
  key = FormatUint(packed_key, 16)

  // field = compressAggregationField(data)
  packed_field = (data.Weekday<<8) | (data.ChannelID-8000)<<3 | (data.WindowStart/4)
  field = FormatUint(packed_field, 16)

  value = Sprintf("%d,%d", data.Min, data.Max)
  HSET(key, field, value)
  ```

---
**8. AggregationBinaryInt (聚合 + 全方位压缩)**
- **构成**: `Aggregation + Key/Field:BinaryPack + Value:Int` 
- **核心思想**: 在`AggregationBinaryPack`的基础上，对Value也采用小整数压缩，达到最大效率。
- **位分配**:
  - **Key (62b)**: `ShopID(30b) + SellerLoc(16b) + BuyerLoc(16b)`
  - **Field (11b)**: `Weekday(3b) + ChannelID(5b) + WindowIndex(3b)`
  - **Value (16b)**: `Min(8b) + Max(8b)`
- **伪代码**:
  ```go
  // key = s.compressAggregatedKey(data)
  key = ... // 同上

  // field = compressAggregationField(data)
  field = ... // 同上

  // value = compressValueToInt(data.Min, data.Max)
  value = (data.Min << 8) | data.Max

  HSET(key, field, value)
  ```

---
**9. KVBinaryPBList (KV + Protobuf)**
- **构成**: `KV Structure + Key:BinaryPack(62bit) + Value:Protobuf`
- **核心思想**: 放弃Hash，采用KV结构。Key使用与聚合策略相同的二进制压缩，Value将一个Key下的所有`{Weekday,Channel,Window,Min,Max}`信息打包成一个Protobuf消息体，一次性写入。
- **位分配**:
  - **Key (62b)**: `ShopID(30b) + SellerLoc(16b) + BuyerLoc(16b)`
  - **Value (Protobuf)**: `repeated uint32 items`, 每个item为19位 `Weekday(3b)|Channel(5b)|WindowIdx(3b)|Min(4b)|Max(4b)`
- **伪代码**:
  ```go
  // func (s *KVBinaryPBListStrategy) WriteData(...)
  // 1. 按聚合Key对数据进行分组
  groups = group testData by compressAggregatedKey(data)

  // 2. 对每个分组，将所有明细打包成 Protobuf List
  for key, group in groups:
    pb_items = new(pb.KVPackedItems)
    for d in group:
      // item: Weekday(3b)|Chan(5b)|Win(3b)|Min(4b)|Max(4b) = 19bit
      packed_item = packPackedItem(d)
      pb_items.Items = append(pb_items.Items, packed_item)
    
    // 3. 序列化并写入
    bytes, _ = proto.Marshal(pb_items)
    SET(key, bytes)
  ```

---
**10. KVBinaryBitPack (KV + 自定义位打包)**
- **构成**: `KV Structure + Key:Binary(8B) + Value:Binary`
- **核心思想**: 极致压缩。采用KV结构，Key为聚合后生成的64位整数直接转为8字节`raw string`，Value则将同一天同一渠道的6个窗口数据自定义打包成一个56位的二进制块，多个块再拼接成字节流。
- **位分配**:
  - **Key (64b)**: `ShopID(30b) + SellerLoc(16b) + BuyerLoc(16b)` -> 8字节二进制
  - **Value (Binary)**: `Count(4B) + N * [Entry(7B)]`
    - **Entry (56b)**: `Weekday(3b)|Channel(5b)|Win1Min(4b)|Win1Max(4b)|...|Win6Max(4b)`
- **伪代码**:
  ```go
  // func (s *KVBinaryBitPackStrategy) WriteData(...)
  // 1. 按聚合Key对数据进行分组
  groups = group testData by compressAggregatedKey(data)

  // 2. 对每个分组，按(Weekday, Channel)二次聚合
  for key, group in groups:
    wc_groups = group `group` by (Weekday, Channel)
    
    // 3. 将每个(Weekday,Channel)下的6个窗口数据打包成字节流
    value_bytes = packWC6EntriesToBytes(wc_groups)
    SET(key, value_bytes)
  ```

### 实验结果

#### 规模：`sampleSize=1,000,000` (总计600万条记录)

| 策略 | Key数量 | 内存使用 | 写入OPS | 读取OPS |
|------|--------:|---------:|--------:|--------:|
| Original | 1.0M | 160.6MB | 291411 | 474116 |
| ValueIntOptimized | 1.0M | 141.9MB (-12%) | 325505 | 421783 |
| Base62 | 1.0M | 122.4MB (-24%) | 275772 | 400688 |
| BinaryPacked | 1.0M | 122.4MB (-24%) | 335558 | 563056 |
| Binary64Bit | 1.0M | 95.8MB (-40%) | 373303 | 514305 |
| ShopLocationAggregation | 66.7K | 102.7MB (-36%) | 308077 | 391919 |
| AggregationBinaryPack | 66.7K | 53.4MB (-67%) | 367361 | 455719 |
| AggregationBinaryInt | 66.7K | 37.1MB (-77%) | 402073 | 476956 |
| KVBinaryPBList | 66.7K | 25.4MB (-84%) | 6509308 | 357149 |
| KVBinaryBitPack | 66.7K | 13.2MB (-92%) | 4791972 | 193453 |

*注：写入OPS基于总记录数（600万）计算。`KVBinaryPBList` 和 `KVBinaryBitPack` 策略由于在客户端预先聚合数据，大幅减少了实际发送到Redis的命令数，因此其写入OPS远高于其他策略，此数据反映了客户端吞吐能力，但单次写入的数据包更大，可能无法完全反映服务端真实负载。*

### 策略分析与结论

根据最新的 `sampleSize=1M`（600万记录）实验数据，我们对策略进行重新评估。

#### 1. 内存压缩效率

- **新王者诞生**: `KVBinaryBitPack` 策略以惊人的 **-92%** 内存压缩率登顶，仅使用 **13.2MB** 内存，展示了自定义二进制位打包的极限威力。
- **Protobuf紧随其后**: `KVBinaryPBList` 策略也达到了 **-84%** 的压缩率，证明了KV结构配合高效序列化方案（Protobuf）的巨大优势。
- **原冠军表现依然强劲**: `AggregationBinaryInt` 策略以 **-77%** 的压缩率位居第三，它在维持HashMap结构和相对简单实现的同时，提供了非常高的压缩比。
- **聚合是基础**: 所有聚合类策略 (`ShopLocationAggregation`及后续) 的内存节省都超过了非聚合的Key压缩策略，再次印证了“结构优化优先于编码优化”的原则。

#### 2. 性能表现 (OPS)

- **写入性能**: `KVBinaryPBList` 和 `KVBinaryBitPack` 的写入OPS极高（分别为650万和479万）。这是因为它们在客户端将大量记录聚合为单条KV写入，减少了网络往返和Redis命令执行次数。此数据反映了客户端吞吐能力，但需注意，单次写入的数据包更大，服务端解码/处理成本也更高。
- **读取性能**:
  - 非聚合策略中，`BinaryPacked` 和 `Binary64Bit` 表现突出（均超过50万OPS），证明二进制Key能有效提升查询效率。
  - 聚合策略中，`AggregationBinaryInt` 的读取性能（47万OPS）非常均衡。
  - `KVBinaryBitPack` 的读取性能相对较低（19万OPS），这可能是因为其复杂的自定义二进制结构需要在客户端进行解析，带来了额外的CPU开销。

#### 结论

- **最佳内存压缩策略：`KVBinaryBitPack`**
    - **优势**: 无与伦比的内存压缩率（-92%），适合对内存极度敏感、成本优先的场景。
    - **权衡**: 实现复杂度最高，且读取性能相对较低，需要客户端进行专门的二进制解析。

- **最佳综合性能策略：`AggregationBinaryInt`**
    - **优势**: 内存压缩率非常高（-77%），且实现相对简单（维持了HashMap结构），读写性能均衡且优秀。
    - **适用场景**: 绝大多数追求高性能与高压缩率平衡的场景，是**最值得推荐的通用方案**。

- **高性能KV选择：`KVBinaryPBList`**
    - **优势**: 内存压缩率极高（-84%），写入吞吐巨大，读取性能尚可（35万OPS）。
    - **适用场景**: 适合写入密集型，且可以接受引入Protobuf作为依赖的场景。

- **核心思想演进**:
    1.  **聚合是基石**: 结构性优化（聚合）是实现数量级内存优化的第一步。
    2.  **KV结构是进阶**: 从HashMap迁移到KV结构，配合高效的序列化（Protobuf）或自定义二进制打包，能实现极致的内存压缩。
    3.  **性能与压缩的权衡**: 压缩率最高的方案（`KVBinaryBitPack`）可能不是读取最快的方案。必须根据业务的读写模式（读多还是写多）和性能要求来选择最合适的策略。
