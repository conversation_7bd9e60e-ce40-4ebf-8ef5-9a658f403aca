// benchmark.fbs
namespace Benchmark.Flat; // 定义命名空间

table ElectiveScore {
  key:string;
  value:double;
}

table Scores {
  math:double;
  physics:double;
  chemistry:double;
  electives:[ElectiveScore]; // map[string]float64 becomes a vector of tables
}

table Address {
  street:string;
  city:string;
  state:string;
  country:string;
  postal_code:string;
  is_current:bool;
  latitude:float;
  longitude:double;
}

table MetadataEntry {
  key:string;
  value:string;
}

table Person {
  name:string;
  age:int; // Corresponds to int32
  email:string;
  addresses:[Address];
  phone_numbers:[string];
  is_student:bool;
  height:float; // Corresponds to float32
  weight:double; // Corresponds to float64
  avatar:[ubyte]; // []byte
  birth_date:long; // Store as Unix timestamp (e.g., nanoseconds)
  favorite_numbers:[long]; // []int64
  metadata:[MetadataEntry]; // map[string]string becomes a vector of tables
  academic_scores:Scores;
}

root_type Person; // 指定根类型