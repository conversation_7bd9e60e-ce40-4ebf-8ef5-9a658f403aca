// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: message.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Person struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Name         string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Age          int32                  `protobuf:"varint,2,opt,name=age,proto3" json:"age,omitempty"`
	Email        string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Addresses    []*Address             `protobuf:"bytes,4,rep,name=addresses,proto3" json:"addresses,omitempty"`
	PhoneNumbers []string               `protobuf:"bytes,5,rep,name=phone_numbers,json=phoneNumbers,proto3" json:"phone_numbers,omitempty"`
	// 添加新的数据类型
	IsStudent       bool                   `protobuf:"varint,6,opt,name=is_student,json=isStudent,proto3" json:"is_student,omitempty"`
	Height          float32                `protobuf:"fixed32,7,opt,name=height,proto3" json:"height,omitempty"`
	Weight          float64                `protobuf:"fixed64,8,opt,name=weight,proto3" json:"weight,omitempty"`
	Avatar          []byte                 `protobuf:"bytes,9,opt,name=avatar,proto3" json:"avatar,omitempty"`
	BirthDate       *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=birth_date,json=birthDate,proto3" json:"birth_date,omitempty"`
	FavoriteNumbers []int64                `protobuf:"varint,11,rep,packed,name=favorite_numbers,json=favoriteNumbers,proto3" json:"favorite_numbers,omitempty"`
	Metadata        map[string]string      `protobuf:"bytes,12,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	AcademicScores  *Scores                `protobuf:"bytes,13,opt,name=academic_scores,json=academicScores,proto3" json:"academic_scores,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Person) Reset() {
	*x = Person{}
	mi := &file_message_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Person) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Person) ProtoMessage() {}

func (x *Person) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Person.ProtoReflect.Descriptor instead.
func (*Person) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{0}
}

func (x *Person) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Person) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *Person) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Person) GetAddresses() []*Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *Person) GetPhoneNumbers() []string {
	if x != nil {
		return x.PhoneNumbers
	}
	return nil
}

func (x *Person) GetIsStudent() bool {
	if x != nil {
		return x.IsStudent
	}
	return false
}

func (x *Person) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Person) GetWeight() float64 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Person) GetAvatar() []byte {
	if x != nil {
		return x.Avatar
	}
	return nil
}

func (x *Person) GetBirthDate() *timestamppb.Timestamp {
	if x != nil {
		return x.BirthDate
	}
	return nil
}

func (x *Person) GetFavoriteNumbers() []int64 {
	if x != nil {
		return x.FavoriteNumbers
	}
	return nil
}

func (x *Person) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Person) GetAcademicScores() *Scores {
	if x != nil {
		return x.AcademicScores
	}
	return nil
}

type Address struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Street     string                 `protobuf:"bytes,1,opt,name=street,proto3" json:"street,omitempty"`
	City       string                 `protobuf:"bytes,2,opt,name=city,proto3" json:"city,omitempty"`
	State      string                 `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Country    string                 `protobuf:"bytes,4,opt,name=country,proto3" json:"country,omitempty"`
	PostalCode string                 `protobuf:"bytes,5,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// 添加新字段
	IsCurrent     bool    `protobuf:"varint,6,opt,name=is_current,json=isCurrent,proto3" json:"is_current,omitempty"`
	Latitude      float32 `protobuf:"fixed32,7,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude     float64 `protobuf:"fixed64,8,opt,name=longitude,proto3" json:"longitude,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Address) Reset() {
	*x = Address{}
	mi := &file_message_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{1}
}

func (x *Address) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *Address) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Address) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Address) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *Address) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *Address) GetIsCurrent() bool {
	if x != nil {
		return x.IsCurrent
	}
	return false
}

func (x *Address) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *Address) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

// 新增的嵌套消息类型
type Scores struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Math          float64                `protobuf:"fixed64,1,opt,name=math,proto3" json:"math,omitempty"`
	Physics       float64                `protobuf:"fixed64,2,opt,name=physics,proto3" json:"physics,omitempty"`
	Chemistry     float64                `protobuf:"fixed64,3,opt,name=chemistry,proto3" json:"chemistry,omitempty"`
	Electives     map[string]float64     `protobuf:"bytes,4,rep,name=electives,proto3" json:"electives,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Scores) Reset() {
	*x = Scores{}
	mi := &file_message_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Scores) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scores) ProtoMessage() {}

func (x *Scores) ProtoReflect() protoreflect.Message {
	mi := &file_message_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scores.ProtoReflect.Descriptor instead.
func (*Scores) Descriptor() ([]byte, []int) {
	return file_message_proto_rawDescGZIP(), []int{2}
}

func (x *Scores) GetMath() float64 {
	if x != nil {
		return x.Math
	}
	return 0
}

func (x *Scores) GetPhysics() float64 {
	if x != nil {
		return x.Physics
	}
	return 0
}

func (x *Scores) GetChemistry() float64 {
	if x != nil {
		return x.Chemistry
	}
	return 0
}

func (x *Scores) GetElectives() map[string]float64 {
	if x != nil {
		return x.Electives
	}
	return nil
}

var File_message_proto protoreflect.FileDescriptor

const file_message_proto_rawDesc = "" +
	"\n" +
	"\rmessage.proto\x12\amessage\x1a\x1fgoogle/protobuf/timestamp.proto\"\x98\x04\n" +
	"\x06Person\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x10\n" +
	"\x03age\x18\x02 \x01(\x05R\x03age\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12.\n" +
	"\taddresses\x18\x04 \x03(\v2\x10.message.AddressR\taddresses\x12#\n" +
	"\rphone_numbers\x18\x05 \x03(\tR\fphoneNumbers\x12\x1d\n" +
	"\n" +
	"is_student\x18\x06 \x01(\bR\tisStudent\x12\x16\n" +
	"\x06height\x18\a \x01(\x02R\x06height\x12\x16\n" +
	"\x06weight\x18\b \x01(\x01R\x06weight\x12\x16\n" +
	"\x06avatar\x18\t \x01(\fR\x06avatar\x129\n" +
	"\n" +
	"birth_date\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tbirthDate\x12)\n" +
	"\x10favorite_numbers\x18\v \x03(\x03R\x0ffavoriteNumbers\x129\n" +
	"\bmetadata\x18\f \x03(\v2\x1d.message.Person.MetadataEntryR\bmetadata\x128\n" +
	"\x0facademic_scores\x18\r \x01(\v2\x0f.message.ScoresR\x0eacademicScores\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xdf\x01\n" +
	"\aAddress\x12\x16\n" +
	"\x06street\x18\x01 \x01(\tR\x06street\x12\x12\n" +
	"\x04city\x18\x02 \x01(\tR\x04city\x12\x14\n" +
	"\x05state\x18\x03 \x01(\tR\x05state\x12\x18\n" +
	"\acountry\x18\x04 \x01(\tR\acountry\x12\x1f\n" +
	"\vpostal_code\x18\x05 \x01(\tR\n" +
	"postalCode\x12\x1d\n" +
	"\n" +
	"is_current\x18\x06 \x01(\bR\tisCurrent\x12\x1a\n" +
	"\blatitude\x18\a \x01(\x02R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\b \x01(\x01R\tlongitude\"\xd0\x01\n" +
	"\x06Scores\x12\x12\n" +
	"\x04math\x18\x01 \x01(\x01R\x04math\x12\x18\n" +
	"\aphysics\x18\x02 \x01(\x01R\aphysics\x12\x1c\n" +
	"\tchemistry\x18\x03 \x01(\x01R\tchemistry\x12<\n" +
	"\telectives\x18\x04 \x03(\v2\x1e.message.Scores.ElectivesEntryR\telectives\x1a<\n" +
	"\x0eElectivesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value:\x028\x01B\x0eZ\fbenchmark/pbb\x06proto3"

var (
	file_message_proto_rawDescOnce sync.Once
	file_message_proto_rawDescData []byte
)

func file_message_proto_rawDescGZIP() []byte {
	file_message_proto_rawDescOnce.Do(func() {
		file_message_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_message_proto_rawDesc), len(file_message_proto_rawDesc)))
	})
	return file_message_proto_rawDescData
}

var file_message_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_message_proto_goTypes = []any{
	(*Person)(nil),                // 0: message.Person
	(*Address)(nil),               // 1: message.Address
	(*Scores)(nil),                // 2: message.Scores
	nil,                           // 3: message.Person.MetadataEntry
	nil,                           // 4: message.Scores.ElectivesEntry
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_message_proto_depIdxs = []int32{
	1, // 0: message.Person.addresses:type_name -> message.Address
	5, // 1: message.Person.birth_date:type_name -> google.protobuf.Timestamp
	3, // 2: message.Person.metadata:type_name -> message.Person.MetadataEntry
	2, // 3: message.Person.academic_scores:type_name -> message.Scores
	4, // 4: message.Scores.electives:type_name -> message.Scores.ElectivesEntry
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_message_proto_init() }
func file_message_proto_init() {
	if File_message_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_message_proto_rawDesc), len(file_message_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_message_proto_goTypes,
		DependencyIndexes: file_message_proto_depIdxs,
		MessageInfos:      file_message_proto_msgTypes,
	}.Build()
	File_message_proto = out.File
	file_message_proto_goTypes = nil
	file_message_proto_depIdxs = nil
}
