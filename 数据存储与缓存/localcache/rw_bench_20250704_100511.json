{"Time":"2025-07-04T10:05:13.592107+08:00","Action":"start","Package":"localcache"}
{"Time":"2025-07-04T10:05:14.240324+08:00","Action":"output","Package":"localcache","Output":"goos: darwin\n"}
{"Time":"2025-07-04T10:05:14.240363+08:00","Action":"output","Package":"localcache","Output":"goarch: amd64\n"}
{"Time":"2025-07-04T10:05:14.240367+08:00","Action":"output","Package":"localcache","Output":"pkg: localcache\n"}
{"Time":"2025-07-04T10:05:14.240374+08:00","Action":"output","Package":"localcache","Output":"cpu: Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz\n"}
{"Time":"2025-07-04T10:05:14.240393+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance"}
{"Time":"2025-07-04T10:05:14.240397+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance","Output":"=== RUN   BenchmarkReadWritePerformance\n"}
{"Time":"2025-07-04T10:05:14.240411+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance","Output":"BenchmarkReadWritePerformance\n"}
{"Time":"2025-07-04T10:05:14.96951+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000"}
{"Time":"2025-07-04T10:05:14.969572+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoCache_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:14.969611+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000","Output":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:16.839402+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000","Output":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000-12         \t 2299740\t       557.0 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:05:18.612964+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000-12         \t 1764772\t       612.3 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:05:20.48209+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000-12         \t 1945748\t       626.2 ns/op\t       1 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:05:22.285112+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000-12         \t 2233117\t       524.2 ns/op\t       1 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:05:24.076347+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_100%_Read_3000-12         \t 2200485\t       550.9 ns/op\t       1 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:05:24.076417+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000"}
{"Time":"2025-07-04T10:05:24.076437+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000","Output":"=== RUN   BenchmarkReadWritePerformance/BigCache_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:24.076464+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000","Output":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:25.935565+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000","Output":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000-12        \t 1930684\t       606.5 ns/op\t      65 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:05:27.722871+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000-12        \t 1746933\t       641.3 ns/op\t      66 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:05:29.652202+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000-12        \t 1904586\t       660.6 ns/op\t      66 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:05:31.496353+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000-12        \t 1809566\t       647.8 ns/op\t      66 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:05:33.360967+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_100%_Read_3000-12        \t 1822719\t       630.9 ns/op\t      66 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:05:33.361015+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000"}
{"Time":"2025-07-04T10:05:33.361045+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FreeCache_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:33.361049+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:35.343084+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000-12       \t 1917256\t       667.8 ns/op\t      33 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:37.027247+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000-12       \t 1799670\t       600.3 ns/op\t      34 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:38.936318+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000-12       \t 1979637\t       657.6 ns/op\t      33 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:41.533986+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000-12       \t 1941976\t       573.1 ns/op\t      33 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:43.23694+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Read_3000-12       \t 1701577\t       729.3 ns/op\t      34 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:43.236964+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000"}
{"Time":"2025-07-04T10:05:43.236968+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Ristretto_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:43.236971+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:44.958471+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000-12       \t 1944090\t       631.5 ns/op\t      18 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:46.804816+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000-12       \t 2107461\t       570.0 ns/op\t      18 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:48.839044+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000-12       \t 2079328\t       636.8 ns/op\t      18 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:50.527672+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000-12       \t 1805769\t       574.2 ns/op\t      19 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:52.262867+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Read_3000-12       \t 2014870\t       501.6 ns/op\t      18 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:52.262947+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000"}
{"Time":"2025-07-04T10:05:52.26295+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FastCache_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:52.262953+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000","Output":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000\n"}
{"Time":"2025-07-04T10:05:54.225982+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000","Output":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000-12       \t 2510233\t       551.8 ns/op\t      33 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:55.964404+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000-12       \t 1854960\t       606.7 ns/op\t      34 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:57.82011+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000-12       \t 2013897\t       596.6 ns/op\t      33 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:05:59.674604+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000-12       \t 1975081\t       569.2 ns/op\t      33 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:01.427351+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_100%_Read_3000-12       \t 2199259\t       536.6 ns/op\t      33 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:01.427375+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000"}
{"Time":"2025-07-04T10:06:01.427378+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoLRU_100%_Read_3000\n"}
{"Time":"2025-07-04T10:06:01.427393+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000\n"}
{"Time":"2025-07-04T10:06:03.485843+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000-12           \t 1895368\t       687.1 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:05.371045+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000-12           \t 1851139\t       662.2 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:07.181723+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000-12           \t 1784274\t       627.8 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:09.136506+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000-12           \t 1878430\t       669.9 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:11.06442+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Read_3000-12           \t 1877368\t       656.7 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:11.064448+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_100%_Read_3000"}
{"Time":"2025-07-04T10:06:11.064452+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_100%_Read_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Otter_100%_Read_3000\n"}
{"Time":"2025-07-04T10:06:11.064456+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_100%_Read_3000","Output":"BenchmarkReadWritePerformance/Otter_100%_Read_3000\n"}
{"Time":"2025-07-04T10:06:12.925521+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_100%_Read_3000","Output":"BenchmarkReadWritePerformance/Otter_100%_Read_3000-12           \t 2380896\t       529.9 ns/op\t       1 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:14.686947+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_100%_Read_3000-12           \t 2644272\t       448.2 ns/op\t       1 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:16.558237+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_100%_Read_3000-12           \t 2638692\t       486.2 ns/op\t       1 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:18.498718+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_100%_Read_3000-12           \t 2256404\t       544.1 ns/op\t       1 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:20.251103+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_100%_Read_3000-12           \t 2493010\t       468.1 ns/op\t       1 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:20.251124+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000"}
{"Time":"2025-07-04T10:06:20.251128+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:06:20.251158+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:06:22.382104+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000-12         \t 1651491\t       743.5 ns/op\t       8 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:24.742029+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000-12         \t 1297369\t      1041 ns/op\t       9 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:27.001856+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000-12         \t 1148942\t      1039 ns/op\t       9 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:29.405899+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000-12         \t 1213988\t      1028 ns/op\t       9 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:32.903846+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_75%_Read_25%_Write_3000-12         \t 1237894\t      1082 ns/op\t       9 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:32.903895+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000"}
{"Time":"2025-07-04T10:06:32.903929+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:06:32.903935+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:06:34.729124+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000-12        \t 1504183\t       773.7 ns/op\t      50 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:36.424627+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000-12        \t 1624431\t       728.5 ns/op\t      50 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:38.30905+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000-12        \t 1555195\t       772.1 ns/op\t      50 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:40.146473+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000-12        \t 1613163\t       724.5 ns/op\t      50 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:41.97152+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_75%_Read_25%_Write_3000-12        \t 1655761\t       721.7 ns/op\t      50 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:41.972126+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000"}
{"Time":"2025-07-04T10:06:41.972153+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:06:41.972159+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:06:43.817944+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000-12       \t 1693694\t       697.5 ns/op\t      26 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:45.700146+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000-12       \t 1705426\t       734.8 ns/op\t      26 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:47.686975+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000-12       \t 1688616\t       724.1 ns/op\t      26 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:49.498124+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000-12       \t 1656478\t       664.4 ns/op\t      26 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:51.404471+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_75%_Read_25%_Write_3000-12       \t 1669120\t       721.0 ns/op\t      26 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:06:51.404497+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000"}
{"Time":"2025-07-04T10:06:51.404501+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:06:51.404505+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:06:53.691168+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000-12       \t 2143125\t       767.5 ns/op\t      45 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:55.628488+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000-12       \t"}
{"Time":"2025-07-04T10:06:55.628531+08:00","Action":"output","Package":"localcache","Output":" 1619335\t       769.9 ns/op\t      45 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:57.544347+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000-12       \t 1571764\t       754.1 ns/op\t      46 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:06:59.367219+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000-12       \t 1374512\t       747.0 ns/op\t      46 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:07:01.209915+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_75%_Read_25%_Write_3000-12       \t 1421600\t       731.7 ns/op\t      46 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:07:01.209942+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000"}
{"Time":"2025-07-04T10:07:01.209945+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:07:01.209949+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:07:03.340584+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000-12       \t 1913631\t       807.0 ns/op\t      26 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:05.178869+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000-12       \t 1845472\t       658.9 ns/op\t      27 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:07.007653+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000-12       \t 1556274\t       661.1 ns/op\t      27 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:08.911389+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000-12       \t 1801317\t       670.8 ns/op\t      27 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:10.835617+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_75%_Read_25%_Write_3000-12       \t 1689506\t       680.9 ns/op\t      27 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:10.835751+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000"}
{"Time":"2025-07-04T10:07:10.835763+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:07:10.835771+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:07:12.508815+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000-12           \t 1421186\t       705.1 ns/op\t      12 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:14.388284+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000-12           \t 1762825\t       658.5 ns/op\t      12 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:16.238786+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000-12           \t 1799876\t       664.6 ns/op\t      12 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:18.164704+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000-12           \t 1802359\t       684.0 ns/op\t      12 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:20.021287+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_75%_Read_25%_Write_3000-12           \t 1754366\t       667.6 ns/op\t      12 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:20.021313+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000"}
{"Time":"2025-07-04T10:07:20.021318+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:07:20.021321+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000\n"}
{"Time":"2025-07-04T10:07:21.928403+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000","Output":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000-12           \t 1828185\t       674.2 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:23.957586+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000-12           \t 1856652\t       708.8 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:25.868113+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000-12           \t 1783134\t       670.9 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:27.825477+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000-12           \t 1629163\t       742.9 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:29.616401+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_75%_Read_25%_Write_3000-12           \t 1888957\t       597.2 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:29.616444+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000"}
{"Time":"2025-07-04T10:07:29.61645+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:07:29.616452+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:07:31.56024+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000-12         \t 1843734\t       674.8 ns/op\t      14 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:33.552621+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000-12         \t 1886371\t       682.2 ns/op\t      14 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:35.551145+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000-12         \t 1855614\t       688.9 ns/op\t      14 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:37.516691+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000-12         \t 1873196\t       668.7 ns/op\t      14 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:39.465819+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_50%_Read_50%_Write_3000-12         \t 1839003\t       664.0 ns/op\t      14 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:39.46585+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000"}
{"Time":"2025-07-04T10:07:39.465855+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:07:39.465861+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:07:41.396895+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000-12        \t 1840186\t       635.9 ns/op\t     277 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:07:43.296057+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000-12        \t 1889863\t       627.0 ns/op\t      49 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:07:45.188113+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000-12        \t 1931578\t       626.5 ns/op\t     129 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:07:47.135884+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000-12        \t 1751349\t       702.1 ns/op\t     304 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:07:49.030319+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_50%_Read_50%_Write_3000-12        \t 1568655\t       734.9 ns/op\t     117 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:07:49.030344+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000"}
{"Time":"2025-07-04T10:07:49.030349+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:07:49.030352+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:07:50.889949+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000-12       \t 1865896\t       623.7 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:52.895872+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000-12       \t 1667367\t       698.4 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:55.117874+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000-12       \t 1628574\t       871.7 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:57.129619+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000-12       \t 1588503\t       768.1 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:59.121705+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_50%_Read_50%_Write_3000-12       \t 1515781\t       785.5 ns/op\t      18 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:07:59.121732+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000"}
{"Time":"2025-07-04T10:07:59.121736+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:07:59.12174+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:08:01.052104+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000-12       \t 1452099\t       793.4 ns/op\t      72 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:08:03.002838+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000-12       \t 1450110\t       812.3 ns/op\t      72 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:08:04.950327+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000-12       \t 1333540\t       814.9 ns/op\t      73 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:08:07.002408+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000-12       \t 1586715\t       810.3 ns/op\t      72 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:08:09.069637+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_50%_Read_50%_Write_3000-12       \t"}
{"Time":"2025-07-04T10:08:09.069677+08:00","Action":"output","Package":"localcache","Output":" 1466892\t       855.8 ns/op\t      72 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:08:09.069688+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000"}
{"Time":"2025-07-04T10:08:09.069693+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:08:09.069699+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:08:10.872109+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000-12       \t 1960386\t       586.1 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:12.741803+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000-12       \t 1894452\t       609.3 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:14.817865+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000-12       \t 1981501\t       704.4 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:16.81996+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000-12       \t 1564386\t       748.7 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:18.655557+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_50%_Read_50%_Write_3000-12       \t 1474904\t       760.7 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:18.655581+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000"}
{"Time":"2025-07-04T10:08:18.655585+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:08:18.655589+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:08:20.654484+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000-12           \t 1553673\t       778.9 ns/op\t      22 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:08:23.597645+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000-12           \t 1556220\t       816.4 ns/op\t      22 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:08:25.540403+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000-12           \t 1342430\t       773.3 ns/op\t      22 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:08:27.460905+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000-12           \t 1780302\t       690.2 ns/op\t      22 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:08:29.388846+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_50%_Read_50%_Write_3000-12           \t 1710331\t       719.4 ns/op\t      22 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:08:29.388873+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000"}
{"Time":"2025-07-04T10:08:29.388877+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:08:29.38888+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000\n"}
{"Time":"2025-07-04T10:08:31.348704+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000","Output":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000-12           \t 1412136\t       819.7 ns/op\t      34 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:33.296497+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000-12           \t 1469629\t       774.7 ns/op\t      34 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:35.385888+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000-12           \t 1454919\t       838.3 ns/op\t      34 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:37.407192+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000-12           \t 1512655\t       789.7 ns/op\t      35 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:39.388348+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_50%_Read_50%_Write_3000-12           \t 1275816\t       869.0 ns/op\t      35 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:39.388377+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000"}
{"Time":"2025-07-04T10:08:39.388381+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:08:39.388399+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:08:41.098546+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000-12         \t 1831390\t       596.6 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:42.845945+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000-12         \t 1549143\t       672.5 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:44.686025+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000-12         \t 2061012\t       581.5 ns/op\t      19 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:46.615349+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000-12         \t 1887968\t       659.4 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:48.349069+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_25%_Read_75%_Write_3000-12         \t 1637940\t       704.9 ns/op\t      20 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:48.349094+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000"}
{"Time":"2025-07-04T10:08:48.349099+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:08:48.349103+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:08:50.378636+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000-12        \t 1320955\t       892.8 ns/op\t      25 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:52.448157+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000-12        \t 1272037\t       967.6 ns/op\t      52 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:54.347211+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000-12        \t 1349970\t       869.5 ns/op\t     202 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:56.024672+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000-12        \t 1333693\t       918.5 ns/op\t     450 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:58.05157+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_25%_Read_75%_Write_3000-12        \t 1398183\t       905.7 ns/op\t     392 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:08:58.051597+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000"}
{"Time":"2025-07-04T10:08:58.051607+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:08:58.051613+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:00.013+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000-12       \t 1298871\t       877.9 ns/op\t      10 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:02.037929+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000-12       \t 1504057\t       842.4 ns/op\t      10 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:04.145016+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000-12       \t 1413592\t       920.0 ns/op\t      10 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:06.007099+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000-12       \t 1772894\t       659.1 ns/op\t      10 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:07.896827+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_25%_Read_75%_Write_3000-12       \t 1807534\t       659.2 ns/op\t      10 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:07.896865+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000"}
{"Time":"2025-07-04T10:09:07.89687+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:07.896873+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:09.942592+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000-12       \t 1419541\t       838.6 ns/op\t      98 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:09:11.923286+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000-12       \t 1344392\t       883.9 ns/op\t      98 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:09:13.945584+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000-12       \t 1340066\t       844.3 ns/op\t      98 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:09:15.976947+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000-12       \t 1415392\t       837.4 ns/op\t      98 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:09:18.044767+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_25%_Read_75%_Write_3000-12       \t 1326520\t       911.3 ns/op\t      98 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:09:18.04486+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000"}
{"Time":"2025-07-04T10:09:18.044869+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:18.044877+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:20.038153+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000-12       \t 1122974\t       928.3 ns/op\t      14 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:23.16948+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000-12       \t 1259113\t       967.9 ns/op\t      14 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:25.11548+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000-12       \t 1825206\t       663.2 ns/op\t      13 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:27.077745+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000-12       \t 1792904\t       683.7 ns/op\t      13 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:29.027469+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_25%_Read_75%_Write_3000-12       \t 1864610\t       668.4 ns/op\t      13 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:29.027514+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000"}
{"Time":"2025-07-04T10:09:29.027539+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:29.027588+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:30.967133+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000-12           \t 1678995\t       739.3 ns/op\t      32 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:32.884182+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000-12           \t 1574734\t       745.8 ns/op\t      32 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:34.85898+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000-12           \t 1673510\t       739.9 ns/op\t      32 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:36.796768+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000-12           \t 1598354\t       714.5 ns/op\t      32 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:38.723804+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_25%_Read_75%_Write_3000-12           \t 1614631\t       729.9 ns/op\t      32 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:38.723832+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000"}
{"Time":"2025-07-04T10:09:38.723836+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:38.72384+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000\n"}
{"Time":"2025-07-04T10:09:41.104431+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000","Output":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000-12           \t 1023366\t      1366 ns/op\t      51 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:43.288704+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000-12           \t  812860\t      1232 ns/op\t      52 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:45.384527+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000-12           \t 1001941\t      1101 ns/op\t      51 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:47.530074+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000-12           \t 1109071\t      1081 ns/op\t      51 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:49.88563+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_25%_Read_75%_Write_3000-12           \t 1076071\t      1115 ns/op\t      51 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:09:49.886599+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000"}
{"Time":"2025-07-04T10:09:49.887619+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoCache_100%_Write_3000\n"}
{"Time":"2025-07-04T10:09:49.887638+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000","Output":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000\n"}
{"Time":"2025-07-04T10:09:52.015526+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000","Output":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000-12                 \t 1625554\t       803.0 ns/op\t      26 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:53.982802+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000-12                 \t 1482752\t       789.9 ns/op\t      26 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:55.934806+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000-12                 \t 1384904\t       854.3 ns/op\t      26 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:57.950123+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000-12                 \t 1590228\t       785.5 ns/op\t      26 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:59.892733+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoCache_100%_Write_3000-12                 \t 1526163\t       753.1 ns/op\t      26 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:09:59.892772+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000"}
{"Time":"2025-07-04T10:09:59.892775+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/BigCache_100%_Write_3000\n"}
{"Time":"2025-07-04T10:09:59.892778+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000","Output":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:01.732365+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000","Output":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000-12                \t 1426020\t       731.9 ns/op\t     171 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:03.794468+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000-12                \t 1566321\t       827.6 ns/op\t      17 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:05.927271+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000-12                \t 1570068\t       857.8 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:07.925556+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000-12                \t 1626048\t       791.7 ns/op\t      94 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:09.923139+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/BigCache_100%_Write_3000-12                \t 1466119\t       823.3 ns/op\t     370 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:09.923163+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000"}
{"Time":"2025-07-04T10:10:09.923173+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FreeCache_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:09.923177+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:11.796935+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000-12               \t 1637811\t       764.1 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:13.820444+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000-12               \t 1531286\t       825.1 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:15.704648+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000-12               \t 1405018\t       810.5 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:17.769249+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000-12               \t 1544376\t       820.2 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:19.679642+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FreeCache_100%_Write_3000-12               \t 1383134\t       760.7 ns/op\t       2 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:19.679666+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000"}
{"Time":"2025-07-04T10:10:19.679669+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Ristretto_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:19.679672+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:21.690924+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000-12               \t 1283428\t       929.1 ns/op\t     123 B/op\t       3 allocs/op\n"}
{"Time":"2025-07-04T10:10:23.799003+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000-12               \t 1229474\t       965.4 ns/op\t     123 B/op\t       3 allocs/op\n"}
{"Time":"2025-07-04T10:10:25.832712+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000-12               \t 1282552\t       928.1 ns/op\t     123 B/op\t       3 allocs/op\n"}
{"Time":"2025-07-04T10:10:28.025077+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000-12               \t 1296943\t       942.4 ns/op\t     123 B/op\t       3 allocs/op\n"}
{"Time":"2025-07-04T10:10:30.032281+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Ristretto_100%_Write_3000-12               \t 1266031\t       943.9 ns/op\t     123 B/op\t       3 allocs/op\n"}
{"Time":"2025-07-04T10:10:30.032312+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000"}
{"Time":"2025-07-04T10:10:30.032317+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/FastCache_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:30.032322+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000","Output":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:31.990624+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000","Output":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000-12               \t 1515780\t       789.7 ns/op\t       7 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:33.904399+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000-12               \t 1747759\t       705.6 ns/op\t       6 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:35.831149+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000-12               \t 1736005\t       690.3 ns/op\t       6 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:37.803908+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000-12               \t 1465544\t       783.2 ns/op\t       6 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:39.728098+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/FastCache_100%_Write_3000-12               \t 1669680\t       741.2 ns/op\t       6 B/op\t       0 allocs/op\n"}
{"Time":"2025-07-04T10:10:39.728661+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000"}
{"Time":"2025-07-04T10:10:39.728671+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/GoLRU_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:39.728676+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:41.734385+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000-12                   \t 1405730\t       862.5 ns/op\t      42 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:10:43.816552+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000-12                   \t 1492400\t       913.3 ns/op\t      42 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:10:45.733745+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000-12                   \t 1331410\t       836.2 ns/op\t      42 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:10:47.699111+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000-12                   \t 1470382\t       790.8 ns/op\t      42 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:10:49.580229+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/GoLRU_100%_Write_3000-12                   \t 1477630\t       787.2 ns/op\t      42 B/op\t       2 allocs/op\n"}
{"Time":"2025-07-04T10:10:49.580255+08:00","Action":"run","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_100%_Write_3000"}
{"Time":"2025-07-04T10:10:49.580259+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_100%_Write_3000","Output":"=== RUN   BenchmarkReadWritePerformance/Otter_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:49.580264+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_100%_Write_3000","Output":"BenchmarkReadWritePerformance/Otter_100%_Write_3000\n"}
{"Time":"2025-07-04T10:10:50.888549+08:00","Action":"output","Package":"localcache","Test":"BenchmarkReadWritePerformance/Otter_100%_Write_3000","Output":"BenchmarkReadWritePerformance/Otter_100%_Write_3000-12                   \t  727288\t      1619 ns/op\t      69 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:10:54.582222+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_100%_Write_3000-12                   \t  823802\t      1527 ns/op\t      68 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:10:55.798985+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_100%_Write_3000-12                   \t  709734\t      1520 ns/op\t      69 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:10:56.971418+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_100%_Write_3000-12                   \t  666043\t      1526 ns/op\t      69 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:10:59.341469+08:00","Action":"output","Package":"localcache","Output":"BenchmarkReadWritePerformance/Otter_100%_Write_3000-12                   \t  810732\t      1424 ns/op\t      68 B/op\t       1 allocs/op\n"}
{"Time":"2025-07-04T10:10:59.348689+08:00","Action":"output","Package":"localcache","Output":"PASS\n"}
{"Time":"2025-07-04T10:10:59.602082+08:00","Action":"output","Package":"localcache","Output":"ok  \tlocalcache\t346.015s\n"}
{"Time":"2025-07-04T10:10:59.60249+08:00","Action":"pass","Package":"localcache","Elapsed":346.019}
