# 缓存性能基准测试报告

## 🧪 测试环境与复现方法

- **硬件平台**：Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz
- **操作系统**：macOS (darwin/amd64)
- **Go 版本**：Go 1.23+
- **测试数据量**：100,000 key-value pairs
- **并发度**：3000 goroutines
- **Key/Value 长度**：30 bytes

### 前提依赖
- Python 3.7+
- pip 包：`pandas`、`matplotlib`
- 工具：`jq`、`gawk`（macOS 可用 `brew install jq gawk`）

### 执行方法
1. 进入脚本目录
   ```sh
   cd 数据存储与缓存/localcache
   ```
2. （可选）创建并激活 Python 虚拟环境
   ```sh
   python3 -m venv venv
   source venv/bin/activate
   pip install pandas matplotlib
   ```
3. 运行一键基准测试脚本
   ```sh
   ./shellscript.sh
   ```
4. 结果查看
   - 所有可视化图片和清洗数据会自动生成在当前目录
   - 打开本 markdown 报告即可查看最新结果

---

> **缓存库用法示例**
> 
> [`cache_usage_examples_test.go`](./cache_usage_examples_test.go) 文件，包含 GoCache、BigCache、FreeCache、Ristretto、FastCache、GoLRU、Otter 七个主流缓存库的最小可运行用法。
> 
> 运行方法：
> ```sh
> go test -v cache_usage_examples_test.go
> ```

---

## 📚 测试缓存库

| 缓存库 | 版本 | 特点 | 算法 |
|--------|------|------|------|
| **GoCache** | v2.1.0 | 内存缓存，支持过期 | LRU + TTL |
| **BigCache** | v3.1.0 | 高性能，零 GC | 自定义分片 |
| **FreeCache** | v1.2.4 | 零 GC，高并发 | LRU |
| **Ristretto** | v0.1.1 | 智能缓存替换 | TinyLFU |
| **FastCache** | v1.12.2 | 极致效率 | 自定义 |
| **GoLRU** | v2.0.7 | 标准 LRU 实现 | LRU |
| **Otter** | v1.2.1 | 高性能 TinyLFU | TinyLFU |

## 🔧 缓存库原理

### 1. GoCache (patrickmn/go-cache)
**核心原理**：基于 Go 内置 map 的内存缓存，使用读写锁保证并发安全
- **数据结构**：`map[string]Item` + `sync.RWMutex`
- **过期策略**：TTL（Time To Live）+ 定期清理
- **淘汰算法**：基于过期时间的被动淘汰
- **并发控制**：读写锁，读操作共享，写操作独占
- **特点**：实现简单，支持过期时间，适合小规模数据

### 2. BigCache (allegro/bigcache)
**核心原理**：分片 + 环形缓冲区设计，避免 GC 压力
- **数据结构**：多个分片（shard），每个分片包含 `map[uint64]uint32` + 环形缓冲区
- **零 GC 设计**：使用字节数组存储，避免指针引用
- **分片策略**：根据 key 哈希值分配到不同分片，减少锁竞争
- **淘汰算法**：FIFO（先进先出），当缓冲区满时覆盖最旧数据
- **特点**：高并发，零 GC，适合大数据量场景

### 3. FreeCache (coocood/freecache)
**核心原理**：零 GC 设计 + LRU 淘汰策略
- **数据结构**：分段存储，使用 `[]byte` 作为底层存储
- **零 GC 设计**：所有数据存储在预分配的字节数组中
- **LRU 实现**：双向链表 + 哈希表，O(1) 时间复杂度
- **分段锁**：将缓存分为多个段，每个段独立加锁
- **特点**：零 GC 压力，内存使用可控，支持 TTL

### 4. Ristretto (dgraph-io/ristretto)
**核心原理**：TinyLFU 算法 + 异步处理
- **TinyLFU 算法**：基于频率的淘汰策略，比 LRU 更智能
- **Count-Min Sketch**：使用概率数据结构统计访问频率
- **异步处理**：读写操作异步化，提高并发性能
- **分层存储**：Window LRU + Main LRU 两层结构
- **特点**：智能淘汰，高命中率，适合复杂访问模式

### 5. FastCache (VictoriaMetrics/fastcache)
**核心原理**：极简设计 + 高内存效率
- **数据结构**：分桶存储，每个桶使用独立的 map
- **内存优化**：紧凑的内存布局，最小化内存开销
- **并发控制**：分桶锁，减少锁竞争
- **淘汰策略**：随机淘汰，实现简单高效
- **特点**：内存效率极高，实现简单，适合内存敏感场景

### 6. GoLRU (hashicorp/golang-lru)
**核心原理**：标准 LRU 算法实现
- **数据结构**：双向链表 + 哈希表
- **LRU 算法**：最近最少使用淘汰策略
- **操作复杂度**：Get/Set 操作均为 O(1)
- **并发控制**：单一互斥锁保护
- **特点**：经典 LRU 实现，算法成熟稳定

### 7. Otter (maypok86/otter)
**核心原理**：现代化 TinyLFU 实现 + 高性能优化
- **TinyLFU 算法**：基于 Caffeine 的 Go 实现
- **W-TinyLFU**：Window + TinyLFU 混合策略
- **无锁设计**：大量使用 CAS 操作，减少锁竞争
- **泛型支持**：Go 1.18+ 泛型，类型安全
- **特点**：现代化设计，高性能，智能淘汰策略

---

## 🔑 底层哈希算法分析

### 哈希算法概览

| 缓存库 | 哈希算法 | 特点 | 性能特征 |
|--------|----------|------|----------|
| **SyncMap** | Go runtime 内置哈希 | 高质量，抗碰撞 | 平衡性能与质量 |
| **GoCache** | Go runtime 内置哈希 | 通用高质量哈希 | 稳定可靠 |
| **BigCache** | FNV-like 快速哈希 | 简单快速 | 速度优先 |
| **FreeCache** | xxhash 算法 | 高性能，低碰撞 | 速度与质量并重 |
| **Ristretto** | xxhash + Count-Min Sketch | 高质量 + 概率统计 | 智能频率统计 |
| **FastCache** | 自定义分桶哈希 | 极简实现 | 最小开销 |
| **GoLRU** | Go runtime 内置哈希 | 标准实现 | 稳定性好 |
| **Otter** | Go runtime 内置哈希 | 现代化设计 | 类型安全 |

### 详细算法分析

#### 1. **Go Runtime 内置哈希** (SyncMap, GoCache, GoLRU, Otter)
- **实现基础**: `hash/maphash.Comparable` 和 Go 运行时优化
- **算法特点**:
  - 使用随机种子防止哈希攻击
  - 针对不同数据类型优化（字符串、整数、结构体等）
  - 运行时自动选择最优哈希函数
  - 抗碰撞性能优秀

#### 2. **FNV-like 哈希** (BigCache)
- **实现基础**: 基于 FNV（Fowler-Noll-Vo）算法的变种
- **算法特点**:
  - 简单的异或和乘法操作
  - 计算速度极快
  - 实现简单，内存开销小

#### 3. **xxhash 算法** (FreeCache, Ristretto)
- **实现基础**: 高性能非加密哈希算法
- **算法特点**:
  - 优秀的速度/质量比
  - 64位输出，碰撞率极低
  - SIMD 指令优化
  - 广泛的平台支持

#### 4. **自定义分桶哈希** (FastCache)
- **实现基础**: 简化的分桶策略
- **算法特点**:
  - 只关注分桶，不关注哈希质量
  - 使用简单的位运算
  - 针对缓存场景优化

#### 5. **Count-Min Sketch** (Ristretto 频率统计)
- **实现基础**: 概率数据结构，用于频率估计
- **算法特点**:
  - 使用多个哈希函数
  - 空间效率极高 O(log n)
  - 允许一定的估计误差

### 选择建议

- **极致性能场景**: 选择 FastCache/BigCache (简单快速哈希)
- **平衡性能与质量**: 选择 FreeCache/Ristretto (xxhash)
- **稳定可靠场景**: 选择 SyncMap/GoCache (Go 内置哈希)
- **智能缓存场景**: 选择 Ristretto/Otter (高质量哈希 + 智能算法)

---

## 📊 测试结果分析

### 1. 读写比例性能测试 (`BenchmarkReadWritePerformance`)(右图以go-lru为基准)

![读写比例性能对比](rw_perf_line_with_baseline.png)

---

### 2. 内存使用效率测试 (`BenchmarkMemoryUsage`)

![内存占用对比](memory_usage_multi_capacity.png)

---

### 3. GC性能测试 (`BenchmarkCacheGC`)

![GC暂停时间对比](gc_pause_bar.png)

---

### 4. CPU使用率测试 (`BenchmarkCacheConcurrencySaturation`)

![CPU效率对比](cpu_efficiency_curve.png)

---

### 5. 命中率测试 (`BenchmarkCacheHitRate`)

![命中率对比](zipf_hitrate_simulator_comparison.png)

---

## 🏁 结论与推荐

### 1. 读写性能
- **高并发读写场景**：推荐 **FreeCache**、**FastCache**，QPS 表现突出，适合高吞吐业务。
- **读多写少/低延迟**：**GoCache**、**GoLRU** 适合小规模、低延迟需求。

### 2. 内存占用
- **极致内存效率**：**BigCache**、**FastCache** 在大容量下 B/entry 最低，适合内存敏感场景。
- **大数据量+低GC压力**：**BigCache**、**FreeCache** 零 GC 设计，适合大数据量、长时间运行。

### 3. CPU 占用
- **高并发低CPU占用**：**FastCache** 在消耗相同CPU资源的情况下能承受的QPS更高，表现较好。
- **智能淘汰+均衡性能**：**Ristretto**、**Otter** 兼顾命中率和资源消耗，适合复杂访问模式。

### 4. 命中率
- **高命中率需求**：**Ristretto**、**Otter**（TinyLFU 算法）在 Zipf 分布下命中率表现优异。

---
