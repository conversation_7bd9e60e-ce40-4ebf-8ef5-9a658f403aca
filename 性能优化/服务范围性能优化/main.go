package main

import (
	"fmt"
	"os"
	"runtime"
	"runtime/debug"
	"time"
)

const (
	// Define a fixed number of queries for our custom benchmark
	numQueries = 1_000_000
)

func main() {
	// Check if we should run the demo
	if len(os.Args) > 1 && os.Args[1] == "demo" {
		runBatchDemo()
		return
	}

	fmt.Println("--- Custom Benchmark Suite ---")

	// --- 1. Data Generation ---
	fmt.Println("\nGenerating decoupled data source... (This should be fast)")
	startTime := time.Now()
	decoupledData := generateDecoupledData()
	fmt.Printf("Data generation complete. Took: %v\n", time.Since(startTime))

	// Generate both individual queries and batch requests for testing
	testQueries := generateQueries(10000)
	batchRequests := generateBatchRequests(1000) // 1000 batch requests, each with 25 items

	// --- 2. Run Benchmarks for each implementation ---

	// Test NativeBitVector (raw bit vector, branchless query)
	runBenchmarkFor("NativeBitVector", func() interface{} {
		return NewNativeBitVector(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*NativeBitVector).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test DecoupledSetsRoaring (native model, should be fast)
	runBenchmarkFor("DecoupledSetsRoaring", func() interface{} {
		return NewDecoupledSetsRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*DecoupledSetsRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test InvertedIndexMap (logically same as Decoupled, different data layout with Go maps)
	runBenchmarkFor("InvertedIndexMap", func() interface{} {
		return NewInvertedIndexMap(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*InvertedIndexMap).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test PartitionedBitmapRoaring (advanced layout optimization)
	runBenchmarkFor("PartitionedBitmapRoaring", func() interface{} {
		return NewPartitionedBitmapRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*PartitionedBitmapRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test NestedMapRoaring (will be slow due to internal Cartesian product)
	runBenchmarkFor("NestedMapRoaring", func() interface{} {
		return NewNestedMapRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*NestedMapRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	// Test FlattenIndexRoaring (will be slow due to internal Cartesian product)
	runBenchmarkFor("FlattenIndexRoaring", func() interface{} {
		return NewFlattenIndexRoaring(decoupledData)
	}, func(instance interface{}, tpl, origin, dest uint32) bool {
		return instance.(*FlattenIndexRoaring).IsServiceable(tpl, origin, dest)
	}, testQueries)

	fmt.Println("\n--- Batch Request Benchmarks ---")

	// Test batch requests with different implementations
	runBatchBenchmarkFor("NativeBitVector", func() interface{} {
		return NewNativeBitVector(decoupledData)
	}, func(instance interface{}, req BatchServiceabilityRequest) [][]bool {
		return processBatchRequest(instance.(*NativeBitVector), req)
	}, batchRequests)

	runBatchBenchmarkFor("InvertedIndexMap", func() interface{} {
		return NewInvertedIndexMap(decoupledData)
	}, func(instance interface{}, req BatchServiceabilityRequest) [][]bool {
		return processBatchRequestMap(instance.(*InvertedIndexMap), req)
	}, batchRequests)

	fmt.Println("\n--- Benchmark Suite Complete ---")
}

// runBenchmarkFor is a helper function to test a given implementation
func runBenchmarkFor(
	name string,
	buildFunc func() interface{},
	queryFunc func(instance interface{}, tpl, origin, dest uint32) bool,
	queries []ServicePair,
) {
	fmt.Printf("\n--- Testing: %s ---\n", name)

	// --- Measure Build Time & Memory ---
	runtime.GC() // Clean up before measurement
	var memBefore, memAfter runtime.MemStats
	runtime.ReadMemStats(&memBefore)

	buildStartTime := time.Now()
	instance := buildFunc()
	buildDuration := time.Since(buildStartTime)

	runtime.GC() // Clean up after build to get accurate memory reading
	runtime.ReadMemStats(&memAfter)

	// Check for underflow before calculating. Use HeapAlloc for more accurate measurement of data structures.
	var memUsed float64
	if memAfter.HeapAlloc > memBefore.HeapAlloc {
		memUsed = float64(memAfter.HeapAlloc-memBefore.HeapAlloc) / 1024 / 1024
	} else {
		memUsed = 0 // Measurement was unstable, report 0
	}

	fmt.Printf("Build Time: %v\n", buildDuration)
	fmt.Printf("Memory Used by Structure: %.2f MB\n", memUsed)

	// --- Measure Query Time ---
	queryStartTime := time.Now()
	for i := 0; i < numQueries; i++ {
		q := queries[i%len(queries)]
		_ = queryFunc(instance, q.TplID, q.OriginID, q.DestID)
	}
	queryDuration := time.Since(queryStartTime)
	avgQueryTime := float64(queryDuration.Nanoseconds()) / float64(numQueries)

	fmt.Printf("Query Time (%d queries): %v\n", numQueries, queryDuration)
	fmt.Printf("Average Query Time: %.2f ns/op\n", avgQueryTime)

	// Aggressively clean up memory before the next test
	instance = nil
	debug.FreeOSMemory()
}

// processBatchRequest processes a batch request using NativeBitVector
func processBatchRequest(instance *NativeBitVector, req BatchServiceabilityRequest) [][]bool {
	results := make([][]bool, len(req.Items))

	for i, item := range req.Items {
		itemResults := make([]bool, len(item.TplIDs))
		for j, tplID := range item.TplIDs {
			itemResults[j] = instance.IsServiceable(tplID, item.SellerID, req.BuyerID)
		}
		results[i] = itemResults
	}

	return results
}

// processBatchRequestMap processes a batch request using InvertedIndexMap
func processBatchRequestMap(instance *InvertedIndexMap, req BatchServiceabilityRequest) [][]bool {
	results := make([][]bool, len(req.Items))

	for i, item := range req.Items {
		itemResults := make([]bool, len(item.TplIDs))
		for j, tplID := range item.TplIDs {
			itemResults[j] = instance.IsServiceable(tplID, item.SellerID, req.BuyerID)
		}
		results[i] = itemResults
	}

	return results
}

// runBatchBenchmarkFor is a helper function to test batch request processing
func runBatchBenchmarkFor(
	name string,
	buildFunc func() interface{},
	batchFunc func(instance interface{}, req BatchServiceabilityRequest) [][]bool,
	requests []BatchServiceabilityRequest,
) {
	fmt.Printf("\n--- Batch Testing: %s ---\n", name)

	// --- Measure Build Time & Memory ---
	runtime.GC() // Clean up before measurement
	var memBefore, memAfter runtime.MemStats
	runtime.ReadMemStats(&memBefore)

	buildStartTime := time.Now()
	instance := buildFunc()
	buildDuration := time.Since(buildStartTime)

	runtime.GC() // Clean up after build to get accurate memory reading
	runtime.ReadMemStats(&memAfter)

	// Check for underflow before calculating
	var memUsed float64
	if memAfter.HeapAlloc > memBefore.HeapAlloc {
		memUsed = float64(memAfter.HeapAlloc-memBefore.HeapAlloc) / 1024 / 1024
	} else {
		memUsed = 0 // Measurement was unstable, report 0
	}

	fmt.Printf("Build Time: %v\n", buildDuration)
	fmt.Printf("Memory Used by Structure: %.2f MB\n", memUsed)

	// --- Measure Batch Processing Time ---
	totalQueries := 0
	for _, req := range requests {
		for _, item := range req.Items {
			totalQueries += len(item.TplIDs)
		}
	}

	batchStartTime := time.Now()
	for _, req := range requests {
		_ = batchFunc(instance, req)
	}
	batchDuration := time.Since(batchStartTime)
	avgQueryTime := float64(batchDuration.Nanoseconds()) / float64(totalQueries)

	fmt.Printf("Batch Processing Time (%d requests, %d total queries): %v\n", len(requests), totalQueries, batchDuration)
	fmt.Printf("Average Query Time: %.2f ns/op\n", avgQueryTime)
	fmt.Printf("Requests per second: %.0f req/s\n", float64(len(requests))/batchDuration.Seconds())

	// Aggressively clean up memory before the next test
	instance = nil
	debug.FreeOSMemory()
}

func runBatchDemo() {
	fmt.Println("=== Item Card 页面批量查询演示 ===\n")

	// 初始化数据
	fmt.Println("1. 初始化物流服务数据...")
	data := generateDecoupledData()
	nativeBitVector := NewNativeBitVector(data)
	invertedIndexMap := NewInvertedIndexMap(data)
	fmt.Printf("   - 物流商数量: %d\n", Num3PLs)
	fmt.Printf("   - 地址总数: %d\n", NumLocations)
	fmt.Printf("   - 数据结构构建完成\n\n")

	// 生成批量请求
	fmt.Println("2. 生成 Item Card 页面请求...")
	requests := generateBatchRequests(1)
	req := requests[0]

	fmt.Printf("   - 买家位置: %d\n", req.BuyerID)
	fmt.Printf("   - 页面商品数: %d\n", len(req.Items))
	fmt.Printf("   - 每个商品的物流选项: %d\n", Num3PLsPerItem)
	fmt.Printf("   - 总查询数: %d\n\n", len(req.Items)*Num3PLsPerItem)

	// 性能测试
	fmt.Println("3. 性能对比测试...")

	// NativeBitVector 测试
	start := time.Now()
	results1 := processBatchRequest(nativeBitVector, req)
	duration1 := time.Since(start)

	// InvertedIndexMap 测试
	start = time.Now()
	results2 := processBatchRequestMap(invertedIndexMap, req)
	duration2 := time.Since(start)

	fmt.Printf("   - NativeBitVector: %v\n", duration1)
	fmt.Printf("   - InvertedIndexMap: %v\n", duration2)
	if duration2 > duration1 {
		fmt.Printf("   - 性能提升: %.1f%%\n\n", float64(duration2-duration1)/float64(duration2)*100)
	} else {
		fmt.Printf("   - InvertedIndexMap 更快: %.1f%%\n\n", float64(duration1-duration2)/float64(duration1)*100)
	}

	// 结果展示
	fmt.Println("4. 查询结果展示 (前5个商品):")
	fmt.Println("   商品ID | 卖家位置 | 3PL服务可达性")
	fmt.Println("   -------|----------|------------------")

	for i := 0; i < 5 && i < len(req.Items); i++ {
		item := req.Items[i]
		fmt.Printf("   %6d | %8d | ", item.ItemID, item.SellerID)

		for j, tplID := range item.TplIDs {
			if results1[i][j] {
				fmt.Printf("3PL%d:✅ ", tplID)
			} else {
				fmt.Printf("3PL%d:❌ ", tplID)
			}
		}
		fmt.Println()
	}

	// 统计信息
	fmt.Println("\n5. 统计信息:")
	totalQueries := len(req.Items) * Num3PLsPerItem
	serviceableCount := 0

	for i := range results1 {
		for j := range results1[i] {
			if results1[i][j] {
				serviceableCount++
			}
		}
	}

	fmt.Printf("   - 总查询数: %d\n", totalQueries)
	fmt.Printf("   - 可服务查询: %d\n", serviceableCount)
	fmt.Printf("   - 服务覆盖率: %.1f%%\n", float64(serviceableCount)/float64(totalQueries)*100)

	// 验证一致性
	consistent := true
	for i := range results1 {
		for j := range results1[i] {
			if results1[i][j] != results2[i][j] {
				consistent = false
				break
			}
		}
		if !consistent {
			break
		}
	}

	if consistent {
		fmt.Println("   - 两种实现结果一致 ✅")
	} else {
		fmt.Println("   - 两种实现结果不一致 ❌")
	}

	fmt.Println("\n=== 演示完成 ===")
}
