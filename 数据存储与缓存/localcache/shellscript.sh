#!/usr/bin/env bash
# ----------------------------------------------
# 一步到位：Go基准测试+数据清洗+画图
# Usage: ./shellscript.sh
# ----------------------------------------------
set -euo pipefail

# 1. 读写性能基准测试
step=1
jsonfile=rw_bench_$(date +%Y%m%d_%H%M%S).json
echo "[$step/7] Running Read/Write performance benchmark..."
go test -bench=BenchmarkReadWritePerformance -benchmem -count=10 -run=^$ -json -timeout=1h > "$jsonfile"
echo "  Output: $jsonfile"

# 2. 过滤读写性能基准行
step=$((step+1))
src="$jsonfile"
echo "[$step/7] Filtering benchmark lines from $src ..."
jq -r '
  select(.Action=="output")
  | .Output
  | select(test("^Benchmark") and test("ns/op") and test("B/op") and test("allocs/op"))
' "$src" > bench_lines.txt

# 3. 清洗为结构化 JSON
step=$((step+1))
echo "[$step/7] Cleaning to bench_clean.json ..."
awk '
  /ns\/op/ && NF>=8 {
    test=$1; n=$2; ns=$(NF-5); bytes=$(NF-3); allocs=$(NF-1);
    sub(/ns\/op/,"",ns); sub(/B\/op/,"",bytes); sub(/allocs\/op/,"",allocs);
    printf "{\"Test\":\"%s\",\"N\":%s,\"NsPerOp\":%s,\"BytesPerOp\":%s,\"AllocsPerOp\":%s}\n", \
           test,n,ns,bytes,allocs;
  }
' bench_lines.txt > bench_clean.json
records=$(wc -l < bench_clean.json)
echo "  bench_clean.json generated with $records records."

# 4. 画读写性能图
step=$((step+1))
echo "[$step/7] Drawing performance chart ..."
python3 draw.py perf





# 5. 内存占用基准测试
step=$((step+1))
echo "[$step/7] Running memory usage benchmark ..."
memory_json=memory_bench_$(date +%Y%m%d_%H%M%S).json
go test -bench=BenchmarkMemoryUsage -benchmem -count=3 -run=^$ -json -timeout=1h > "$memory_json"
echo "  Memory usage benchmark result saved to $memory_json"

jq -r '
  select(.Action=="output")
  | .Output
  | select(test("BenchmarkMemoryUsage") and test("MB"))
' "$memory_json" > memory_bench_lines.txt

# 6. 清洗内存基准数据
step=$((step+1))
echo "[$step/7] Cleaning memory usage data ..."
gawk '
/BenchmarkMemoryUsage/ && /B\/entry/ {
    split($1, a, "/")
    split(a[2], b, "_")
    lib = b[1]
    split(b[2], c, "-")
    cap = c[1]
    match($0, /([0-9.]+) B\/entry/, m)
    per_entry_b = m[1]
    match($0, /([0-9.]+) MB/, n)
    total_mb = n[1]
    printf("{\"name\": \"%s\", \"capacity\": %s, \"total_mb\": %s, \"per_entry_b\": %s}\n", lib, cap, total_mb, per_entry_b)
}
' memory_bench_lines.txt > memory_bench_clean.json
echo "  Memory usage JSON saved to memory_bench_clean.json"

# 7. 画内存占用图
step=$((step+1))
echo "[$step/7] Drawing memory usage chart ..."
python3 draw.py memory
# python3 draw.py memory_baseline





# 8. 并发基准测试
step=$((step+1))
echo "[$step/7] Running Cache Concurrency Saturation benchmark ..."
cpu_json=cpu_bench_$(date +%Y%m%d_%H%M%S).json
go test -bench=BenchmarkCacheConcurrencySaturation -run=^$ -json > "$cpu_json"

# 9. 清洗 QPS/CPU 行
step=$((step+1))
echo "[$step/7] Cleaning concurrency QPS/CPU data ..."
jq -r '
  select(.Action=="output")
  | .Output
  | select(test("QPS=") and test("CPU_avg="))
' "$cpu_json" > cpu_bench_lines.txt
python3 clean_cpu_bench.py cpu_bench_lines.txt

# 10. 画并发效率图
step=$((step+1))
echo "[$step/7] Drawing CPU efficiency chart ..."
python3 draw.py cpu_eff




# 11. GC基准测试
step=$((step+1))
echo "[$step/7] Running Cache GC benchmark ..."
gc_json=gc_bench_$(date +%Y%m%d_%H%M%S).json
go test -bench=BenchmarkCacheGC -benchmem -count=10 -run=^$ -json -timeout=1h > "$gc_json"
echo "  Cache GC benchmark result saved to $gc_json"

# 12. 清洗GC基准数据
step=$((step+1))
echo "[$step/7] Cleaning GC benchmark data ..."
jq -r '
  select(.Action=="output")
  | .Output
  | select(test("BenchmarkCacheGC/"))
  | select(test("GC_pause_ms"))
' "$gc_json" > gc_bench_lines.txt
awk '{
    lib = ""
    if (match($0, /BenchmarkCacheGC\/[A-Za-z0-9]+/)) {
        s = substr($0, RSTART, RLENGTH)
        sub("BenchmarkCacheGC/", "", s)
        lib = s
    }
    pause = ""
    if (match($0, /[0-9.]+ GC_pause_ms/)) {
        s = substr($0, RSTART, RLENGTH)
        sub(" GC_pause_ms", "", s)
        pause = s
    }
    cycles = ""
    if (match($0, /[0-9.]+ GC_cycles/)) {
        s = substr($0, RSTART, RLENGTH)
        sub(" GC_cycles", "", s)
        cycles = s
    }
    if (lib && pause && cycles) {
        printf("{\"lib\": \"%s\", \"gc_pause_ms\": %s, \"gc_cycles\": %s}\n", lib, pause, cycles)
    }
}' gc_bench_lines.txt > gc_bench_clean.json
echo "  GC bench clean json generated."

# 13. 画GC性能图
step=$((step+1))
echo "[$step/7] Drawing GC performance chart ..."
python3 draw.py gc




# 14. 命中率基准测试
step=$((step+1))
echo "[$step/7] Running Cache Hit Rate Simulator benchmark ..."
hitrate_json=hitrate_simulator_$(date +%Y%m%d_%H%M%S).json
go test -bench=BenchmarkCacheHitRateSimulator -run=^$ -json -v -timeout=60s > "$hitrate_json"
echo "  Cache hit rate simulator benchmark result saved to $hitrate_json"

# 15. 清洗命中率数据
step=$((step+1))
echo "[$step/7] Cleaning hit rate simulator data ..."
python3 clean_hitrate_simulator.py "$hitrate_json"
echo "  Hit rate data cleaned and saved to hitrate_simulator_clean.json"

# 16. 画命中率图 (使用清理后的数据)
step=$((step+1))
echo "[$step/7] Drawing hit rate chart ..."
python3 draw.py hitrate_simulator

echo "All done!"