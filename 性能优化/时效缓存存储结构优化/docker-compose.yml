services:
  redis:
    image: redis:7.2-alpine
    container_name: redis-optimization
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - redis-net

  redis-insight:
    image: redislabs/redisinsight:1.14.0
    container_name: redis-insight
    ports:
      - "8001:8001"
    volumes:
      - redisinsight_data:/db
    environment:
      - RIPORT=8001
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - redis-net

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: redis-optimization-app
    volumes:
      - .:/workspace
      - go_cache:/go/pkg/mod
    working_dir: /workspace
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - GO111MODULE=on
      - GOPROXY=https://goproxy.cn,direct
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - redis-net
    profiles:
      - app
    tty: true
    stdin_open: true

  # 性能监控
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - redis-net
    profiles:
      - monitoring

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    depends_on:
      - redis-exporter
    networks:
      - redis-net
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    networks:
      - redis-net
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  redisinsight_data:
    driver: local
  go_cache:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  redis-net:
    driver: bridge
